<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food & Drinks Kitchen Display</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .header {
            background-color: #2e7d32; /* Green for food/drinks */
        }
        .order-card {
            border-left: 4px solid #2e7d32;
        }
        .order-item {
            padding: 5px 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
            margin: 0;
        }
        .order-items {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 10px 0;
        }
        .order-card.completed {
            opacity: 0;
            transform: translateX(-100%);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Food & Drinks Kitchen Orders</h1>
    </div>
    <div class="orders-container" id="orders-container">
        {% if orders %}
            {% for order in orders %}
                <div class="order-card" data-timestamp="{{ order.timestamp }}" data-order-id="{{ order.id }}">
                    <div class="order-content">
                        <div class="order-info">
                            <div class="table-number">Table {{ order.tableNumber }}</div>
                            <div class="customer-name">{{ order.customerName }}</div>
                        </div>
                        <div class="order-items">
                            {% for item in order.items %}
                                <div class="order-item">
                                    <span class="item-name">{{ item.name }}</span>
                                    {% if item.notes %}
                                        <span class="item-notes">({{ item.notes }})</span>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="order-footer">
                        <span class="order-timer">{{ order.timestamp }}</span>
                        <button class="complete-btn" onclick="handleComplete('{{ order.id }}', this)">Complete</button>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="no-orders">No food/drink orders at this time</div>
        {% endif %}
    </div>

    <script>
        // Function to format time (MM:SS)
        function formatTime(timestamp) {
            if (!timestamp) return "0:00";
            
            const now = new Date();
            const start = new Date(timestamp);
            const diff = Math.floor((now - start) / 1000);
            const minutes = Math.floor(diff / 60);
            const seconds = diff % 60;
            return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        // Handle complete order
        async function handleComplete(orderId, button) {
            const orderCard = button.closest('.order-card');
            try {
                const response = await fetch(`http://localhost:5001/orders/${orderId}/kitchen`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    // Add completed class to trigger animation
                    orderCard.classList.add('completed');
                    
                    // Remove the order card after animation
                    setTimeout(() => {
                        orderCard.remove();
                        
                        // Check if there are any orders left
                        const container = document.getElementById('orders-container');
                        if (container.children.length === 0) {
                            container.innerHTML = '<div class="no-orders">No food/drink orders at this time</div>';
                        }
                    }, 300);
                } else {
                    alert('Failed to mark order as complete');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to mark order as complete');
            }
        }

        // Update timers every second
        function updateTimers() {
            document.querySelectorAll('.order-card').forEach(card => {
                const timer = card.querySelector('.order-timer');
                const timestamp = card.getAttribute('data-timestamp');
                if (timer && timestamp) {
                    timer.textContent = formatTime(timestamp);
                }
            });
        }

        // Auto-refresh orders every 5 seconds
        function refreshOrders() {
            window.location.reload();
        }

        // Initialize timers and auto-refresh
        setInterval(updateTimers, 1000);
        setInterval(refreshOrders, 5000);
    </script>
</body>
</html>