/**
 * Bill Archive Service
 * Stores all bills permanently in a structured folder system
 */

const fs = require('fs');
const path = require('path');
const QRCode = require('qrcode');

class BillArchiveService {
    constructor(options = {}) {
        this.options = {
            archiveDir: './bill-archive',
            createPdf: true,
            ...options
        };

        // Create archive directory if it doesn't exist
        if (!fs.existsSync(this.options.archiveDir)) {
            fs.mkdirSync(this.options.archiveDir, { recursive: true });
        }

        console.log('Bill Archive Service initialized with options:', this.options);
    }

    /**
     * Archive a bill
     * @param {Object} order - Order data
     * @param {Object} paymentDetails - Payment details
     * @returns {Object} Archive info
     */
    async archiveBill(order, paymentDetails = {}) {
        try {
            // Create date-based folder structure
            const date = new Date(order.timestamp || new Date());
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');

            const yearDir = path.join(this.options.archiveDir, String(year));
            const monthDir = path.join(yearDir, month);
            const dayDir = path.join(monthDir, day);

            // Create directories if they don't exist
            if (!fs.existsSync(yearDir)) {
                fs.mkdirSync(yearDir);
            }
            if (!fs.existsSync(monthDir)) {
                fs.mkdirSync(monthDir);
            }
            if (!fs.existsSync(dayDir)) {
                fs.mkdirSync(dayDir);
            }

            // Generate unique filename
            const timestamp = date.toISOString().replace(/[:.]/g, '-');
            const billId = order.id || Date.now();
            const filename = `bill-${billId}-${timestamp}`;

            // Process items to ensure they have the correct format
            let processedItems = [];
            if (order.items) {
                // If items is a string, try to parse it
                const items = typeof order.items === 'string' ? JSON.parse(order.items) : order.items;

                // Process each item to ensure it has the correct format
                processedItems = items.map(item => {
                    return {
                        name: item.name || '',
                        price: parseFloat(item.price) || 0,
                        quantity: parseInt(item.quantity) || 1,
                        notes: item.notes || ''
                    };
                });
            }

            // Combine order and payment details
            const billData = {
                ...order,
                items: processedItems,
                payment: paymentDetails,
                archiveTimestamp: new Date().toISOString()
            };

            // Log payment details for debugging
            console.log('Archiving bill with payment details:', paymentDetails);

            // Save as JSON
            const jsonPath = path.join(dayDir, `${filename}.json`);
            fs.writeFileSync(jsonPath, JSON.stringify(billData, null, 2));

            // Generate QR code for the bill
            const qrCodeData = `bill:${billId}`;
            const qrCodePath = path.join(dayDir, `${filename}-qr.png`);
            await this.generateQRCode(qrCodeData, qrCodePath);

            // Save as PDF if enabled
            let pdfPath = null;
            if (this.options.createPdf) {
                pdfPath = path.join(dayDir, `${filename}.pdf`);
                await this.generatePDF(billData, pdfPath, qrCodePath);
            }

            console.log(`Bill ${billId} archived successfully`);

            return {
                success: true,
                billId: billId,
                jsonPath: jsonPath,
                qrCodePath: qrCodePath,
                pdfPath: pdfPath
            };
        } catch (error) {
            console.error('Error archiving bill:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Generate a QR code for a bill
     * @param {string} data - QR code data
     * @param {string} outputPath - Output file path
     * @returns {Promise<boolean>} Success status
     */
    async generateQRCode(data, outputPath) {
        try {
            // Generate QR code as a data URL instead of a file
            // This avoids the need for canvas dependency
            const qrDataUrl = await QRCode.toDataURL(data, {
                errorCorrectionLevel: 'H',
                margin: 1,
                scale: 8
            });

            // Extract the base64 data from the data URL
            const base64Data = qrDataUrl.replace(/^data:image\/png;base64,/, '');

            // Write the base64 data to a file
            fs.writeFileSync(outputPath, Buffer.from(base64Data, 'base64'));

            return true;
        } catch (error) {
            console.error('Error generating QR code:', error);
            return false;
        }
    }

    /**
     * Generate a text receipt for a bill
     * @param {Object} billData - Bill data
     * @param {string} outputPath - Output file path
     * @param {string} qrCodePath - Path to QR code image
     * @returns {Promise<boolean>} Success status
     */
    async generatePDF(billData, outputPath, qrCodePath) {
        try {
            // Create a simple text file receipt
            // In a production environment, you might want to use a proper PDF library

            let content = 'BILL RECEIPT\n';
            content += '=============\n\n';

            // Bill details
            content += `Bill ID: ${billData.id}\n`;
            content += `Date: ${new Date(billData.timestamp).toLocaleString()}\n`;
            content += `Table: ${billData.tableNumber}\n`;
            content += `Customer: ${billData.customerName}\n\n`;

            // Items
            content += 'ITEMS\n';
            content += '-----\n';

            if (billData.items && Array.isArray(billData.items)) {
                let subtotal = 0;

                billData.items.forEach(item => {
                    const quantity = item.quantity || 1;
                    const price = item.price || 0;
                    const total = quantity * price;

                    content += `${item.name} x${quantity} @ $${price.toFixed(2)} = $${total.toFixed(2)}\n`;
                    subtotal += total;
                });

                content += '\n';

                // Add tax, tip, etc. if available
                if (billData.payment) {
                    if (billData.payment.subtotal) {
                        content += `Subtotal: $${billData.payment.subtotal.toFixed(2)}\n`;
                    }
                    if (billData.payment.gst) {
                        content += `GST (${(billData.payment.gstRate * 100).toFixed(0)}%): $${billData.payment.gst.toFixed(2)}\n`;
                    }
                    if (billData.payment.pst) {
                        content += `PST (${(billData.payment.pstRate * 100).toFixed(0)}%): $${billData.payment.pst.toFixed(2)}\n`;
                    }
                    if (billData.payment.tax && !billData.payment.gst && !billData.payment.pst) {
                        content += `Tax (${(billData.payment.taxRate * 100).toFixed(0)}%): $${billData.payment.tax.toFixed(2)}\n`;
                    }
                    if (billData.payment.tip) {
                        content += `Tip: $${billData.payment.tip.toFixed(2)}\n`;
                    }
                }

                // Total
                const total = billData.payment?.total || subtotal;
                content += `Total: $${total.toFixed(2)}\n\n`;

                // Payment method
                if (billData.payment?.paymentMethod) {
                    content += `Payment Method: ${billData.payment.paymentMethod}\n`;
                }
            }

            content += '\nQR Code: See attached image file\n';
            content += '\nThank you for your business!';

            // Write to file
            fs.writeFileSync(outputPath, content);

            // Also create an HTML version for better viewing
            const htmlPath = outputPath.replace('.pdf', '.html');
            const htmlContent = this.generateHtmlReceipt(billData, qrCodePath);
            fs.writeFileSync(htmlPath, htmlContent);

            return true;
        } catch (error) {
            console.error('Error generating receipt:', error);
            return false;
        }
    }

    /**
     * Generate an HTML receipt
     * @param {Object} billData - Bill data
     * @param {string} qrCodePath - Path to QR code image
     * @returns {string} HTML content
     */
    generateHtmlReceipt(billData, qrCodePath) {
        // Get relative path to QR code for HTML
        const qrCodeRelativePath = path.relative(path.dirname(qrCodePath), qrCodePath).replace(/\\/g, '/');

        let html = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Bill Receipt #${billData.id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .receipt { max-width: 800px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; }
        .header { text-align: center; margin-bottom: 20px; }
        .details { margin-bottom: 20px; }
        .items { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .items th, .items td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        .total { text-align: right; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; color: #666; }
        .qrcode { text-align: center; margin: 20px 0; }
        .qrcode img { max-width: 200px; }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <h1>Bill Receipt</h1>
            <p>Paradiz Hookah Lounge</p>
        </div>

        <div class="details">
            <p><strong>Bill ID:</strong> ${billData.id}</p>
            <p><strong>Date:</strong> ${new Date(billData.timestamp).toLocaleString()}</p>
            <p><strong>Table:</strong> ${billData.tableNumber}</p>
            <p><strong>Customer:</strong> ${billData.customerName}</p>
        </div>

        <table class="items">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
`;

        let subtotal = 0;

        if (billData.items && Array.isArray(billData.items)) {
            billData.items.forEach(item => {
                const quantity = item.quantity || 1;
                const price = item.price || 0;
                const total = quantity * price;
                subtotal += total;

                html += `
                <tr>
                    <td>${item.name}</td>
                    <td>${quantity}</td>
                    <td>$${price.toFixed(2)}</td>
                    <td>$${total.toFixed(2)}</td>
                </tr>
                `;
            });
        }

        html += `
            </tbody>
        </table>

        <div class="total">
`;

        // Add tax, tip, etc. if available
        if (billData.payment) {
            if (billData.payment.subtotal) {
                html += `<p>Subtotal: $${billData.payment.subtotal.toFixed(2)}</p>`;
            }
            if (billData.payment.gst) {
                html += `<p>GST (${(billData.payment.gstRate * 100).toFixed(0)}%): $${billData.payment.gst.toFixed(2)}</p>`;
            }
            if (billData.payment.pst) {
                html += `<p>PST (${(billData.payment.pstRate * 100).toFixed(0)}%): $${billData.payment.pst.toFixed(2)}</p>`;
            }
            if (billData.payment.tax && !billData.payment.gst && !billData.payment.pst) {
                html += `<p>Tax (${(billData.payment.taxRate * 100).toFixed(0)}%): $${billData.payment.tax.toFixed(2)}</p>`;
            }
            if (billData.payment.tip) {
                html += `<p>Tip: $${billData.payment.tip.toFixed(2)}</p>`;
            }
        }

        // Total
        const total = billData.payment?.total || subtotal;
        html += `<p class="total-amount">Total: $${total.toFixed(2)}</p>`;

        // Payment method
        if (billData.payment?.paymentMethod) {
            html += `<p>Payment Method: ${billData.payment.paymentMethod}</p>`;
        }

        html += `
        </div>

        <div class="qrcode">
            <img src="${qrCodeRelativePath}" alt="QR Code">
            <p>Scan to verify receipt</p>
        </div>

        <div class="footer">
            <p>Thank you for your business!</p>
        </div>
    </div>
</body>
</html>`;

        return html;
    }

    /**
     * Get all bills for a specific date
     * @param {string} date - Date in YYYY-MM-DD format
     * @returns {Array} Bills
     */
    getBillsByDate(date) {
        try {
            const [year, month, day] = date.split('-');
            const dayDir = path.join(this.options.archiveDir, year, month, day);

            if (!fs.existsSync(dayDir)) {
                return [];
            }

            // Get all JSON files in the directory
            const files = fs.readdirSync(dayDir)
                .filter(file => file.endsWith('.json'));

            // Read and parse each file
            return files.map(file => {
                const filePath = path.join(dayDir, file);
                const fileContent = fs.readFileSync(filePath, 'utf8');
                return JSON.parse(fileContent);
            });
        } catch (error) {
            console.error('Error getting bills by date:', error);
            return [];
        }
    }

    /**
     * Get a specific bill by ID
     * @param {string|number} billId - Bill ID
     * @returns {Object|null} Bill data or null if not found
     */
    getBillById(billId) {
        try {
            // Search through all directories
            const years = fs.readdirSync(this.options.archiveDir);

            for (const year of years) {
                const yearDir = path.join(this.options.archiveDir, year);
                if (!fs.statSync(yearDir).isDirectory()) continue;

                const months = fs.readdirSync(yearDir);
                for (const month of months) {
                    const monthDir = path.join(yearDir, month);
                    if (!fs.statSync(monthDir).isDirectory()) continue;

                    const days = fs.readdirSync(monthDir);
                    for (const day of days) {
                        const dayDir = path.join(monthDir, day);
                        if (!fs.statSync(dayDir).isDirectory()) continue;

                        // Find bill JSON files
                        const files = fs.readdirSync(dayDir)
                            .filter(file => file.endsWith('.json') && file.includes(`bill-${billId}`));

                        if (files.length > 0) {
                            const filePath = path.join(dayDir, files[0]);
                            const fileContent = fs.readFileSync(filePath, 'utf8');
                            return JSON.parse(fileContent);
                        }
                    }
                }
            }

            return null;
        } catch (error) {
            console.error('Error getting bill by ID:', error);
            return null;
        }
    }

    /**
     * Search bills by criteria
     * @param {Object} criteria - Search criteria
     * @returns {Array} Matching bills
     */
    searchBills(criteria = {}) {
        try {
            const results = [];

            // Search through all directories
            const years = fs.readdirSync(this.options.archiveDir);

            for (const year of years) {
                const yearDir = path.join(this.options.archiveDir, year);
                if (!fs.statSync(yearDir).isDirectory()) continue;

                const months = fs.readdirSync(yearDir);
                for (const month of months) {
                    const monthDir = path.join(yearDir, month);
                    if (!fs.statSync(monthDir).isDirectory()) continue;

                    const days = fs.readdirSync(monthDir);
                    for (const day of days) {
                        const dayDir = path.join(monthDir, day);
                        if (!fs.statSync(dayDir).isDirectory()) continue;

                        // Find bill JSON files
                        const files = fs.readdirSync(dayDir)
                            .filter(file => file.endsWith('.json'));

                        for (const file of files) {
                            const filePath = path.join(dayDir, file);
                            const fileContent = fs.readFileSync(filePath, 'utf8');
                            const billData = JSON.parse(fileContent);

                            // Check if bill matches criteria
                            let matches = true;

                            if (criteria.customerId && billData.customerId !== criteria.customerId) {
                                matches = false;
                            }

                            if (criteria.customerName && !billData.customerName.includes(criteria.customerName)) {
                                matches = false;
                            }

                            if (criteria.tableNumber && billData.tableNumber !== criteria.tableNumber) {
                                matches = false;
                            }

                            if (criteria.minAmount && billData.payment?.total < criteria.minAmount) {
                                matches = false;
                            }

                            if (criteria.maxAmount && billData.payment?.total > criteria.maxAmount) {
                                matches = false;
                            }

                            if (criteria.paymentMethod && billData.payment?.paymentMethod !== criteria.paymentMethod) {
                                matches = false;
                            }

                            if (matches) {
                                results.push(billData);
                            }
                        }
                    }
                }
            }

            return results;
        } catch (error) {
            console.error('Error searching bills:', error);
            return [];
        }
    }

    /**
     * Get bill file paths
     * @param {string|number} billId - Bill ID
     * @returns {Object} File paths
     */
    getBillFilePaths(billId) {
        try {
            // Search through all directories
            const years = fs.readdirSync(this.options.archiveDir);

            for (const year of years) {
                const yearDir = path.join(this.options.archiveDir, year);
                if (!fs.statSync(yearDir).isDirectory()) continue;

                const months = fs.readdirSync(yearDir);
                for (const month of months) {
                    const monthDir = path.join(yearDir, month);
                    if (!fs.statSync(monthDir).isDirectory()) continue;

                    const days = fs.readdirSync(monthDir);
                    for (const day of days) {
                        const dayDir = path.join(monthDir, day);
                        if (!fs.statSync(dayDir).isDirectory()) continue;

                        // Find bill files
                        const jsonFiles = fs.readdirSync(dayDir)
                            .filter(file => file.endsWith('.json') && file.includes(`bill-${billId}`));

                        if (jsonFiles.length > 0) {
                            const baseName = jsonFiles[0].replace('.json', '');
                            const jsonPath = path.join(dayDir, jsonFiles[0]);
                            const pdfPath = path.join(dayDir, `${baseName}.pdf`);
                            const qrPath = path.join(dayDir, `${baseName}-qr.png`);

                            return {
                                jsonPath: fs.existsSync(jsonPath) ? jsonPath : null,
                                pdfPath: fs.existsSync(pdfPath) ? pdfPath : null,
                                qrPath: fs.existsSync(qrPath) ? qrPath : null
                            };
                        }
                    }
                }
            }

            return {
                jsonPath: null,
                pdfPath: null,
                qrPath: null
            };
        } catch (error) {
            console.error('Error getting bill file paths:', error);
            return {
                jsonPath: null,
                pdfPath: null,
                qrPath: null
            };
        }
    }
}

module.exports = BillArchiveService;
