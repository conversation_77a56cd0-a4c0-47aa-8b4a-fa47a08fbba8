/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html, body {
    height: 100%;
    overflow: hidden;
}

body {
    background-color: #f5f5f5;
    background-image: linear-gradient(rgba(255, 255, 255, 0.5) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255, 255, 255, 0.5) 1px, transparent 1px);
    background-size: 20px 20px;
    color: #333;
}

/* Main container */
.main-container {
    display: flex;
    height: 100vh;
    padding: 15px;
    gap: 15px;
    overflow: hidden;
}

/* Left side: Bill details */
.bill-side {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.bill-header {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    flex-shrink: 0;
}

.bill-header h2 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #2c3e50;
    display: flex;
    align-items: center;
}

.customer-info {
    display: flex;
    gap: 15px;
    font-size: 13px;
    padding: 5px 0;
}

.customer-info span {
    display: flex;
    align-items: center;
    font-weight: 500;
}

/* Items container - this is the scrollable part */
.items-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0;
    min-height: 0; /* Important for flex children to respect overflow */
}

.items-container h3 {
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    font-size: 14px;
    margin: 0;
    flex-shrink: 0;
}

#items-list {
    list-style: none;
    overflow-y: auto; /* This is the only element that should scroll */
    flex: 1;
    padding: 0;
}

#items-list li {
    padding: 8px 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

#items-list li:hover {
    background-color: #f5f5f5;
}

#items-list li span:first-child {
    display: flex;
    align-items: center;
    font-weight: 500;
}

#items-list li span:last-child {
    font-weight: 600;
    color: #2c3e50;
}

/* Bill summary */
.bill-summary {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
    flex-shrink: 0;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3px;
    font-size: 13px;
}

.summary-row.total {
    margin-top: 5px;
    padding-top: 5px;
    border-top: 1px solid #ddd;
    font-size: 15px;
    font-weight: 600;
}

/* Right side: Payment options */
.payment-side {
    width: 40%;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.payment-header {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.payment-header h2 {
    font-size: 16px;
    color: #2c3e50;
    display: flex;
    align-items: center;
}

.payment-buttons {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow-y: auto;
}

.payment-btn-row {
    display: flex;
    gap: 10px;
}

.payment-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 6px;
    background-color: #3498db;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    outline: none;
    position: relative;
    z-index: 1;
}

.payment-btn:hover {
    background-color: #2980b9;
}

.payment-btn:active {
    background-color: #1f6aa6;
    transform: translateY(1px);
}

.payment-btn i {
    margin-right: 8px;
}

.payment-info {
    margin-top: auto; /* Push to bottom */
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    flex-shrink: 0;
}

.payment-info p {
    color: #7f8c8d;
    font-size: 14px;
}

/* Rest of your existing CSS remains the same */
/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    padding-top: 30px; /* Increased top padding to make room for the larger close button */
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    width: 350px;
    max-width: 90%;
    position: relative;
}

.close {
    position: absolute;
    right: 10px;
    top: 10px;
    font-size: 32px; /* Increased from 22px */
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    width: 40px; /* Added fixed width */
    height: 40px; /* Added fixed height */
    line-height: 40px; /* Center the X vertically */
    text-align: center; /* Center the X horizontally */
    border-radius: 50%; /* Make it circular */
    background-color: #f1f1f1; /* Light background for better visibility */
    display: flex; /* Use flexbox for better centering */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
}

.close:hover {
    color: #333;
    background-color: #e0e0e0; /* Darker background on hover */
}

.close:active {
    background-color: #d0d0d0; /* Even darker when pressed */
    transform: scale(0.95); /* Slight scale down effect when pressed */
}

.whatsapp-input,
.amount-input {
    margin: 15px 0;
}

.whatsapp-input input,
.amount-input input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    margin-bottom: 15px;
}

.keypad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.key {
    padding: 12px;
    background-color: #f1f1f1;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.key:hover {
    background-color: #e0e0e0;
}

/* Special styles */
.item-quantity {
    display: inline-block;
    background-color: #3498db;
    color: white;
    font-size: 11px;
    font-weight: bold;
    padding: 2px 5px;
    border-radius: 10px;
    margin-left: 5px;
}

.split-badge {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    font-size: 11px;
    font-weight: bold;
    padding: 2px 5px;
    border-radius: 10px;
    margin-left: 5px;
}

.tax-badge {
    display: inline-block;
    background-color: #3498db;
    color: white;
    font-size: 9px;
    font-weight: bold;
    padding: 1px 4px;
    border-radius: 50%;
    margin-left: 4px;
    cursor: help;
}

.tax-note {
    font-size: 9px;
    color: #7f8c8d;
    font-style: italic;
    margin-left: 3px;
    font-weight: normal;
}

/* Icons */
i {
    width: 18px;
    text-align: center;
    margin-right: 6px;
    color: #3498db;
}

h2 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 3px;
    border-bottom: 1px solid #3498db;
    position: relative;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 40px;
    height: 1px;
    background-color: #4CAF50;
}

/* Error message */
.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.error-message i {
    color: #721c24;
    margin-right: 10px;
}

/* Success message */
.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.success-message i {
    color: #155724;
    margin-right: 10px;
}

/* Transaction ID */
.transaction-id {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
    margin: 10px 0;
    font-size: 14px;
    word-break: break-all;
}

/* Action buttons */
.action-btn,
.send-btn {
    width: 100%;
    padding: 10px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover,
.send-btn:hover {
    background-color: #2980b9;
}

/* Message notifications */
.success-message,
.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.5s ease;
    opacity: 1;
    max-width: 400px;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.success-message i,
.error-message i {
    font-size: 20px;
    margin-right: 10px;
}

.success-message i {
    color: #28a745;
}

.error-message i {
    color: #dc3545;
}