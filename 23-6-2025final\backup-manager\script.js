document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const backupList = document.getElementById('backup-list');
    const backupDetails = document.getElementById('backup-details');
    const createBackupBtn = document.getElementById('create-backup-btn');
    const exportDataBtn = document.getElementById('export-data-btn');
    const refreshBtn = document.getElementById('refresh-btn');
    const backBtn = document.getElementById('back-btn');
    const typeButtons = document.querySelectorAll('.type-btn');

    // Data Export Elements
    const dataExportSection = document.getElementById('data-export-section');
    const exportStartDateInput = document.getElementById('export-start-date');
    const exportEndDateInput = document.getElementById('export-end-date');
    const exportToExcelCheckbox = document.getElementById('export-to-excel');
    const exportToEmailCheckbox = document.getElementById('export-to-email');
    const exportToGoogleCheckbox = document.getElementById('export-to-google');
    const exportToPrintCheckbox = document.getElementById('export-to-print');
    const emailExportOptions = document.getElementById('email-export-options');
    const googleExportOptions = document.getElementById('google-export-options');
    const emailRecipientInput = document.getElementById('email-recipient');
    const googleSpreadsheetIdInput = document.getElementById('google-spreadsheet-id');
    const generateExportBtn = document.getElementById('generate-export-btn');
    const autoExportEnabledCheckbox = document.getElementById('auto-export-enabled');
    const autoExportOptions = document.getElementById('auto-export-options');
    const autoExportTimeInput = document.getElementById('auto-export-time');
    const autoExportEmailInput = document.getElementById('auto-export-email');

    // Tab Elements
    const tabFiles = document.getElementById('tab-files');
    const tabOrders = document.getElementById('tab-orders');
    const tabContentFiles = document.getElementById('tab-content-files');
    const tabContentOrders = document.getElementById('tab-content-orders');

    // Modal Elements
    const backupModal = document.getElementById('backup-modal');
    const restoreModal = document.getElementById('restore-modal');
    const orderDetailsModal = document.getElementById('order-details-modal');
    const successModal = document.getElementById('success-modal');
    const errorModal = document.getElementById('error-modal');
    const successMessageText = document.getElementById('success-message-text');
    const errorMessageText = document.getElementById('error-message-text');
    const successActions = document.getElementById('success-actions');
    const closeButtons = document.querySelectorAll('.close');
    const cancelButtons = document.querySelectorAll('.cancel-btn');
    const backupForm = document.getElementById('backup-form');
    const confirmRestoreBtn = document.getElementById('confirm-restore-btn');
    const orderDetailsContainer = document.getElementById('order-details-container');

    // State
    let selectedBackup = null;
    let currentFilter = 'all';
    let backups = [];
    let ordersData = null;
    let currentView = 'backups'; // 'backups' or 'export'

    // Initialize
    loadBackups();
    initializeExportDates();

    // Event Listeners
    createBackupBtn.addEventListener('click', showBackupModal);
    exportDataBtn.addEventListener('click', toggleDataExportView);
    refreshBtn.addEventListener('click', loadBackups);

    // Data Export Event Listeners
    exportToEmailCheckbox.addEventListener('change', function() {
        emailExportOptions.style.display = this.checked ? 'block' : 'none';
    });

    exportToGoogleCheckbox.addEventListener('change', function() {
        googleExportOptions.style.display = this.checked ? 'block' : 'none';
    });

    autoExportEnabledCheckbox.addEventListener('change', function() {
        autoExportOptions.style.display = this.checked ? 'block' : 'none';
    });

    generateExportBtn.addEventListener('click', generateDataExport);

    // Tab event listeners
    tabFiles.addEventListener('click', function() {
        setActiveTab('files');
    });

    tabOrders.addEventListener('click', function() {
        setActiveTab('orders');
        if (selectedBackup && !ordersData) {
            loadOrderData(selectedBackup);
        }
    });

    // Back button
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            window.location.href = '../settings/index.html';
        });
    }

    // Type filter buttons
    typeButtons.forEach(button => {
        button.addEventListener('click', () => {
            typeButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            currentFilter = button.dataset.type;
            filterBackups();
        });
    });

    // Close modals
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            backupModal.style.display = 'none';
            restoreModal.style.display = 'none';
            orderDetailsModal.style.display = 'none';
            successModal.style.display = 'none';
            errorModal.style.display = 'none';
        });
    });

    cancelButtons.forEach(button => {
        button.addEventListener('click', () => {
            backupModal.style.display = 'none';
            restoreModal.style.display = 'none';
        });
    });

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === backupModal) {
            backupModal.style.display = 'none';
        } else if (event.target === restoreModal) {
            restoreModal.style.display = 'none';
        } else if (event.target === orderDetailsModal) {
            orderDetailsModal.style.display = 'none';
        } else if (event.target === successModal) {
            successModal.style.display = 'none';
        } else if (event.target === errorModal) {
            errorModal.style.display = 'none';
        }
    });

    // Create backup form submission
    backupForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const backupType = document.getElementById('backup-type').value;
        createBackup(backupType);
    });

    // Confirm restore button
    confirmRestoreBtn.addEventListener('click', () => {
        if (selectedBackup) {
            restoreBackup(selectedBackup.name, selectedBackup.type);
        }
    });

    // Functions

    // Load backups from server
    function loadBackups() {
        backupList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading backups...</div>';

        fetch('http://localhost:5000/api/backups')
            .then(response => response.json())
            .then(data => {
                backups = data;
                filterBackups();
            })
            .catch(error => {
                console.error('Error loading backups:', error);
                backupList.innerHTML = `<div class="error">Error loading backups: ${error.message}</div>`;
            });
    }

    // Filter backups based on selected type
    function filterBackups() {
        let filteredBackups = backups;

        if (currentFilter !== 'all') {
            filteredBackups = backups.filter(backup => backup.type === currentFilter);
        }

        renderBackupList(filteredBackups);
    }

    // Render the backup list
    function renderBackupList(backups) {
        if (backups.length === 0) {
            backupList.innerHTML = '<div class="no-backups">No backups found</div>';
            return;
        }

        backupList.innerHTML = '';

        backups.forEach(backup => {
            const backupItem = document.createElement('div');
            backupItem.className = 'backup-item';
            if (selectedBackup && selectedBackup.name === backup.name) {
                backupItem.classList.add('selected');
            }

            const date = new Date(backup.timestamp);
            const formattedDate = date.toLocaleString();
            const formattedSize = formatSize(backup.size);

            backupItem.innerHTML = `
                <div class="backup-item-header">
                    <div class="backup-name">${backup.name}</div>
                    <div class="backup-type ${backup.type}">${backup.type}</div>
                </div>
                <div class="backup-info">
                    <div class="backup-date">${formattedDate}</div>
                    <div class="backup-size">${formattedSize}</div>
                </div>
            `;

            backupItem.addEventListener('click', () => {
                document.querySelectorAll('.backup-item').forEach(item => {
                    item.classList.remove('selected');
                });
                backupItem.classList.add('selected');
                selectedBackup = backup;
                loadBackupDetails(backup);
            });

            backupList.appendChild(backupItem);
        });
    }

    // Set active tab
    function setActiveTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`tab-${tabName}`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        document.getElementById(`tab-content-${tabName}`).classList.add('active');
    }

    // Load details for a specific backup
    function loadBackupDetails(backup) {
        selectedBackup = backup;
        ordersData = null; // Reset orders data when a new backup is selected

        // Set the Files tab as active
        setActiveTab('files');

        const detailsContent = document.getElementById('tab-content-files');
        detailsContent.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading details...</div>';

        fetch(`http://localhost:5000/api/backups/${backup.name}?type=${backup.type}`)
            .then(response => response.json())
            .then(data => {
                // Check if the response is an error message
                if (data.success === false) {
                    detailsContent.innerHTML = `<div class="error-message">Error: ${data.error || 'Failed to load backup details'}</div>`;
                    return;
                }

                const date = new Date(data.timestamp);
                const formattedDate = date.toLocaleString();
                const formattedSize = formatSize(data.size);

                detailsContent.innerHTML = `
                    <div class="details-header">
                        <h3>${data.name}</h3>
                        <p>Created: ${formattedDate}</p>
                        <p>Type: ${data.type}</p>
                        <p>Size: ${formattedSize}</p>
                    </div>

                    <div class="details-section">
                        <h4>Files in Backup (${data.files ? data.files.length : 0})</h4>
                        <div class="file-list">
                            ${data.files ? data.files.map(file => `<div class="file-item">${file}</div>`).join('') : 'No files found'}
                        </div>
                    </div>

                    <div class="details-actions">
                        <button class="primary-btn download-btn">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="danger-btn restore-btn">
                            <i class="fas fa-undo"></i> Restore
                        </button>
                    </div>
                `;

                // Add event listener for restore button
                const restoreBtn = detailsContent.querySelector('.restore-btn');
                restoreBtn.addEventListener('click', () => {
                    showRestoreModal(data);
                });

                // Add event listener for download button
                const downloadBtn = detailsContent.querySelector('.download-btn');
                downloadBtn.addEventListener('click', () => {
                    // This would typically trigger a download, but for now just show an alert
                    alert('Download functionality will be implemented in a future update.');
                });
            })
            .catch(error => {
                console.error('Error loading backup details:', error);
                detailsContent.innerHTML = `<div class="error-message">Error: Failed to load backup details. Please try again.</div>`;
            });
    }

    // Load order data from a backup
    function loadOrderData(backup) {
        const ordersContainer = document.querySelector('.orders-container');
        ordersContainer.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading order data...</div>';

        fetch(`http://localhost:5000/api/backups/${backup.name}/orders?type=${backup.type}`)
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    ordersContainer.innerHTML = `<div class="error-message">Error: ${data.error || 'Failed to load order data'}</div>`;
                    return;
                }

                // Store the orders data
                ordersData = data;

                if (data.orders.length === 0) {
                    ordersContainer.innerHTML = '<p>No orders found in this backup.</p>';
                    return;
                }

                // Create the orders table
                let tableHtml = `
                    <h3>Orders in Backup (${data.count})</h3>
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Table</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                data.orders.forEach(order => {
                    const date = new Date(order.timestamp);
                    const formattedDate = date.toLocaleString();

                    tableHtml += `
                        <tr>
                            <td>${order.id}</td>
                            <td>${order.tableNumber}</td>
                            <td>${order.customerName}</td>
                            <td>${formattedDate}</td>
                            <td>${order.status}</td>
                            <td>
                                <button class="order-details-btn" data-order-id="${order.id}">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </td>
                        </tr>
                    `;
                });

                tableHtml += `
                        </tbody>
                    </table>
                `;

                ordersContainer.innerHTML = tableHtml;

                // Add event listeners to the view buttons
                const viewButtons = ordersContainer.querySelectorAll('.order-details-btn');
                viewButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const orderId = parseInt(button.getAttribute('data-order-id'));
                        showOrderDetails(orderId);
                    });
                });
            })
            .catch(error => {
                console.error('Error loading order data:', error);
                ordersContainer.innerHTML = `<div class="error-message">Error: Failed to load order data. Please try again.</div>`;
            });
    }

    // Show order details in a modal
    function showOrderDetails(orderId) {
        if (!ordersData || !ordersData.orders) {
            alert('Order data not available');
            return;
        }

        const order = ordersData.orders.find(o => o.id === orderId);
        if (!order) {
            alert('Order not found');
            return;
        }

        const date = new Date(order.timestamp);
        const formattedDate = date.toLocaleString();

        // Parse the items if needed
        let orderItems = [];
        let totalAmount = 0;

        try {
            if (order.parsedItems && Array.isArray(order.parsedItems)) {
                orderItems = order.parsedItems;
                // Calculate total
                totalAmount = orderItems.reduce((sum, item) => {
                    return sum + (parseFloat(item.price) * parseInt(item.quantity));
                }, 0);
            } else if (typeof order.items === 'string') {
                orderItems = JSON.parse(order.items);
                // Calculate total
                totalAmount = orderItems.reduce((sum, item) => {
                    return sum + (parseFloat(item.price) * parseInt(item.quantity));
                }, 0);
            }
        } catch (e) {
            console.error('Error parsing order items:', e);
        }

        // Build the HTML for the modal
        let html = `
            <div class="order-header">
                <h3>Order #${order.id}</h3>
                <p><strong>Date:</strong> ${formattedDate}</p>
                <p><strong>Table:</strong> ${order.tableNumber}</p>
                <p><strong>Customer:</strong> ${order.customerName}</p>
                <p><strong>Status:</strong> ${order.status}</p>
                ${order.paymentMethod ? `<p><strong>Payment Method:</strong> ${order.paymentMethod}</p>` : ''}
            </div>
        `;

        if (orderItems.length > 0) {
            html += `
                <table class="order-items-table">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            orderItems.forEach(item => {
                const itemTotal = parseFloat(item.price) * parseInt(item.quantity);
                html += `
                    <tr>
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>$${parseFloat(item.price).toFixed(2)}</td>
                        <td>$${itemTotal.toFixed(2)}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
                <div class="order-total">
                    Total: $${totalAmount.toFixed(2)}
                </div>
            `;
        } else {
            html += '<p>No items found for this order.</p>';
        }

        // Display the modal
        orderDetailsContainer.innerHTML = html;
        orderDetailsModal.style.display = 'block';

        // Add event listener to close the modal
        const closeBtn = orderDetailsModal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            orderDetailsModal.style.display = 'none';
        });

        // Close the modal when clicking outside of it
        window.addEventListener('click', function(event) {
            if (event.target === orderDetailsModal) {
                orderDetailsModal.style.display = 'none';
            }
        });
    }

    // Show backup creation modal
    function showBackupModal() {
        backupModal.style.display = 'block';
    }

    // Show restore confirmation modal
    function showRestoreModal(backup) {
        document.getElementById('restore-backup-name').textContent = backup.name;
        document.getElementById('restore-backup-date').textContent = new Date(backup.timestamp).toLocaleString();
        restoreModal.style.display = 'block';

        // Set up the confirm restore button
        confirmRestoreBtn.onclick = function() {
            restoreBackup(backup.name, backup.type);
        };
    }

    // Create a new backup
    function createBackup(type) {
        backupModal.style.display = 'none';

        // Show loading state
        const loadingMessage = document.createElement('div');
        loadingMessage.className = 'loading-overlay';
        loadingMessage.innerHTML = '<div class="loading-message"><i class="fas fa-spinner fa-spin"></i> Creating backup...</div>';
        document.body.appendChild(loadingMessage);

        fetch(`http://localhost:5000/api/backup?type=${type}`, {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                document.body.removeChild(loadingMessage);
                if (data.success) {
                    alert(`Backup created successfully: ${data.backupPath}`);
                    loadBackups();
                } else {
                    alert(`Error creating backup: ${data.error}`);
                }
            })
            .catch(error => {
                document.body.removeChild(loadingMessage);
                console.error('Error creating backup:', error);
                alert(`Error creating backup: ${error.message}`);
            });
    }

    // Restore from a backup
    function restoreBackup(backupName, backupType) {
        restoreModal.style.display = 'none';

        // Show loading state
        const loadingMessage = document.createElement('div');
        loadingMessage.className = 'loading-overlay';
        loadingMessage.innerHTML = '<div class="loading-message"><i class="fas fa-spinner fa-spin"></i> Restoring from backup...</div>';
        document.body.appendChild(loadingMessage);

        fetch(`http://localhost:5000/api/restore/${backupName}?type=${backupType}`, {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                document.body.removeChild(loadingMessage);
                if (data.success) {
                    alert(`Restored successfully from backup: ${backupName}`);
                    // Reload the page to reflect changes
                    window.location.reload();
                } else {
                    alert(`Error restoring from backup: ${data.error}`);
                }
            })
            .catch(error => {
                document.body.removeChild(loadingMessage);
                console.error('Error restoring from backup:', error);
                alert(`Error restoring from backup: ${error.message}`);
            });
    }

    // Format file size for display
    function formatSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Add loading overlay styles
    const style = document.createElement('style');
    style.textContent = `
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .loading-message {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
    `;
    document.head.appendChild(style);

    // Data Export Functions

    // Initialize export dates with default values
    function initializeExportDates() {
        const today = new Date();
        const oneWeekAgo = new Date(today);
        oneWeekAgo.setDate(today.getDate() - 7);

        exportStartDateInput.value = formatDateForInput(oneWeekAgo);
        exportEndDateInput.value = formatDateForInput(today);

        // Load auto-export settings
        loadAutoExportSettings();
    }

    // Format date for input fields (YYYY-MM-DD)
    function formatDateForInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // Toggle between backup view and data export view
    function toggleDataExportView() {
        if (currentView === 'backups') {
            // Switch to export view
            backupList.parentElement.style.display = 'none';
            backupDetails.style.display = 'none';
            dataExportSection.style.display = 'block';
            currentView = 'export';
            exportDataBtn.innerHTML = '<i class="fas fa-database"></i> View Backups';
        } else {
            // Switch to backup view
            backupList.parentElement.style.display = 'block';
            backupDetails.style.display = 'block';
            dataExportSection.style.display = 'none';
            currentView = 'backups';
            exportDataBtn.innerHTML = '<i class="fas fa-file-export"></i> Export Data';
        }
    }

    // Generate and export data
    async function generateDataExport() {
        // Validate inputs
        const startDate = exportStartDateInput.value;
        const endDate = exportEndDateInput.value;

        if (!startDate || !endDate) {
            showError('Please select both start and end dates');
            return;
        }

        // Check if at least one export destination is selected
        if (!exportToExcelCheckbox.checked &&
            !exportToEmailCheckbox.checked &&
            !exportToGoogleCheckbox.checked &&
            !exportToPrintCheckbox.checked) {
            showError('Please select at least one export destination');
            return;
        }

        // Validate email if email export is selected
        if (exportToEmailCheckbox.checked && !validateEmail(emailRecipientInput.value)) {
            showError('Please enter a valid email address');
            return;
        }

        // Validate Google Spreadsheet ID if Google export is selected
        if (exportToGoogleCheckbox.checked && !googleSpreadsheetIdInput.value.trim()) {
            showError('Please enter a Google Spreadsheet ID');
            return;
        }

        // Show loading state
        generateExportBtn.disabled = true;
        generateExportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

        try {
            // Prepare export options
            const exportOptions = {
                startDate: startDate,
                endDate: endDate,
                exportToLocal: exportToExcelCheckbox.checked,
                exportToEmail: exportToEmailCheckbox.checked,
                exportToGoogleSheets: exportToGoogleCheckbox.checked,
                printReport: exportToPrintCheckbox.checked,
                emailRecipient: emailRecipientInput.value,
                spreadsheetId: googleSpreadsheetIdInput.value
            };

            // Call the API to generate the export
            const response = await fetch('/api/export-sales', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(exportOptions)
            });

            const result = await response.json();

            if (result.success) {
                // Show success message
                showSuccess(result);

                // Save auto-export settings if enabled
                if (autoExportEnabledCheckbox.checked) {
                    saveAutoExportSettings();
                }
            } else {
                showError(result.error || 'Failed to generate export');
            }
        } catch (error) {
            console.error('Error generating export:', error);
            showError('An error occurred while generating the export');
        } finally {
            // Reset button state
            generateExportBtn.disabled = false;
            generateExportBtn.innerHTML = '<i class="fas fa-file-export"></i> Generate Export';
        }
    }

    // Save auto-export settings
    async function saveAutoExportSettings() {
        try {
            const settings = {
                enabled: autoExportEnabledCheckbox.checked,
                time: autoExportTimeInput.value,
                email: autoExportEmailInput.value
            };

            await fetch('/api/settings/auto-export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(settings)
            });

            console.log('Auto-export settings saved');
        } catch (error) {
            console.error('Error saving auto-export settings:', error);
        }
    }

    // Load auto-export settings
    async function loadAutoExportSettings() {
        try {
            const response = await fetch('/api/settings/auto-export');
            const data = await response.json();

            if (data.success && data.settings) {
                const settings = data.settings;
                autoExportEnabledCheckbox.checked = settings.enabled;
                autoExportTimeInput.value = settings.time || '23:59';
                autoExportEmailInput.value = settings.email || '';

                // Show/hide auto export options based on enabled state
                autoExportOptions.style.display = settings.enabled ? 'block' : 'none';
            }
        } catch (error) {
            console.error('Error loading auto-export settings:', error);
        }
    }

    // Show success modal
    function showSuccess(result) {
        successMessageText.textContent = 'Your export has been generated successfully!';

        // Clear previous actions
        successActions.innerHTML = '';

        // Add actions based on export results
        if (result.localPath) {
            const openButton = document.createElement('button');
            openButton.className = 'action-btn';
            openButton.innerHTML = '<i class="fas fa-folder-open"></i> Open File Location';
            openButton.onclick = function() {
                window.open(`file://${result.localPath.replace(/\\/g, '/')}`, '_blank');
            };
            successActions.appendChild(openButton);
        }

        if (result.emailSent) {
            const emailInfo = document.createElement('p');
            emailInfo.innerHTML = '<i class="fas fa-envelope"></i> Export sent to your email';
            successActions.appendChild(emailInfo);
        }

        if (result.googleSheetsUpdated) {
            const googleButton = document.createElement('button');
            googleButton.className = 'action-btn';
            googleButton.innerHTML = '<i class="fas fa-table"></i> Open Google Sheet';
            googleButton.onclick = function() {
                window.open(`https://docs.google.com/spreadsheets/d/${googleSpreadsheetIdInput.value}`, '_blank');
            };
            successActions.appendChild(googleButton);
        }

        // Show the modal
        successModal.style.display = 'block';
    }

    // Show error modal
    function showError(message) {
        errorMessageText.textContent = message;
        errorModal.style.display = 'block';
    }

    // Validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
});
