<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#ffffff">
    <title>Display Settings - Paradiz Hookah Lounge</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles.css">
    <script src="../../theme.js"></script>
</head>
<body>
    <div class="container">
        <div class="header-with-back">
            <button class="back-button" id="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>Display Settings</h1>
        </div>

        <div class="settings-section">
            <h2>Theme</h2>
            <p class="settings-description">Choose between light and dark mode for the application.</p>
            <div class="theme-toggle-container">
                <div class="theme-option">
                    <input type="radio" id="theme-light" name="theme" value="light">
                    <label for="theme-light">
                        <div class="theme-preview light">
                            <div class="theme-icon">☀️</div>
                            <div class="theme-name">Light</div>
                        </div>
                    </label>
                </div>

                <div class="theme-option">
                    <input type="radio" id="theme-dark" name="theme" value="dark">
                    <label for="theme-dark">
                        <div class="theme-preview dark">
                            <div class="theme-icon">🌙</div>
                            <div class="theme-name">Dark</div>
                        </div>
                    </label>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <h2>Theme Preview</h2>
            <p class="settings-description">This is how the selected theme will look across the application.</p>
            <div class="theme-preview-container">
                <div class="preview-item">
                    <h3>Headers</h3>
                    <div class="preview-header">This is a header</div>
                </div>
                <div class="preview-item">
                    <h3>Buttons</h3>
                    <div class="preview-buttons">
                        <button class="preview-button primary">Primary</button>
                        <button class="preview-button secondary">Secondary</button>
                    </div>
                </div>
                <div class="preview-item">
                    <h3>Cards</h3>
                    <div class="preview-card">
                        <div class="preview-card-header">Card Title</div>
                        <div class="preview-card-body">This is the content of the card.</div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="script.js"></script>
</body>
</html>