const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class ExpenseService {
    constructor(db) {
        this.db = db;
        this.dataDir = path.join(__dirname, 'data');
        this.expensesFile = path.join(this.dataDir, 'expenses.json');
        this.ensureDataDirectory();
        this.initializeFiles();
        this.initializeDatabase();
    }

    ensureDataDirectory() {
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
    }

    initializeFiles() {
        if (!fs.existsSync(this.expensesFile)) {
            fs.writeFileSync(this.expensesFile, JSON.stringify({ expenses: [] }));
        }
    }

    initializeDatabase() {
        // Create expenses table if it doesn't exist
        this.db.run(`
            CREATE TABLE IF NOT EXISTS expenses (
                id TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                amount REAL NOT NULL,
                category TEXT NOT NULL,
                date TEXT NOT NULL,
                notes TEXT,
                receipt_image TEXT,
                created_at TEXT NOT NULL
            )
        `, (err) => {
            if (err) {
                console.error('Error creating expenses table:', err);
            } else {
                console.log('Expenses table created or already exists');
            }
        });
    }

    async recordExpense(expense) {
        return new Promise((resolve, reject) => {
            const id = crypto.randomUUID();
            const timestamp = new Date().toISOString();
            
            const newExpense = {
                id,
                description: expense.description,
                amount: parseFloat(expense.amount),
                category: expense.category,
                date: expense.date || timestamp.split('T')[0],
                notes: expense.notes || '',
                receipt_image: expense.receipt_image || null,
                created_at: timestamp
            };
            
            // Insert into database
            this.db.run(
                `INSERT INTO expenses (id, description, amount, category, date, notes, receipt_image, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    newExpense.id,
                    newExpense.description,
                    newExpense.amount,
                    newExpense.category,
                    newExpense.date,
                    newExpense.notes,
                    newExpense.receipt_image,
                    newExpense.created_at
                ],
                function(err) {
                    if (err) {
                        console.error('Error recording expense:', err);
                        reject(err);
                        return;
                    }
                    
                    // Also save to JSON file as backup
                    try {
                        const data = JSON.parse(fs.readFileSync(this.expensesFile));
                        data.expenses.push(newExpense);
                        fs.writeFileSync(this.expensesFile, JSON.stringify(data, null, 2));
                        resolve(newExpense);
                    } catch (fileErr) {
                        console.error('Error saving expense to file:', fileErr);
                        // Still resolve with the expense since it was saved to DB
                        resolve(newExpense);
                    }
                }.bind(this)
            );
        });
    }

    async getExpenses(filters = {}) {
        return new Promise((resolve, reject) => {
            let query = `SELECT * FROM expenses`;
            const queryParams = [];
            
            // Apply filters
            if (Object.keys(filters).length > 0) {
                const filterConditions = [];
                
                if (filters.startDate && filters.endDate) {
                    filterConditions.push(`date >= ? AND date <= ?`);
                    queryParams.push(filters.startDate, filters.endDate);
                } else if (filters.startDate) {
                    filterConditions.push(`date >= ?`);
                    queryParams.push(filters.startDate);
                } else if (filters.endDate) {
                    filterConditions.push(`date <= ?`);
                    queryParams.push(filters.endDate);
                }
                
                if (filters.category) {
                    filterConditions.push(`category = ?`);
                    queryParams.push(filters.category);
                }
                
                if (filters.minAmount) {
                    filterConditions.push(`amount >= ?`);
                    queryParams.push(parseFloat(filters.minAmount));
                }
                
                if (filters.maxAmount) {
                    filterConditions.push(`amount <= ?`);
                    queryParams.push(parseFloat(filters.maxAmount));
                }
                
                if (filterConditions.length > 0) {
                    query += ` WHERE ${filterConditions.join(' AND ')}`;
                }
            }
            
            // Add ordering
            query += ` ORDER BY date DESC, created_at DESC`;
            
            this.db.all(query, queryParams, (err, rows) => {
                if (err) {
                    console.error('Error fetching expenses:', err);
                    reject(err);
                    return;
                }
                
                resolve(rows);
            });
        });
    }

    async getExpenseById(id) {
        return new Promise((resolve, reject) => {
            this.db.get(
                `SELECT * FROM expenses WHERE id = ?`,
                [id],
                (err, row) => {
                    if (err) {
                        console.error('Error fetching expense:', err);
                        reject(err);
                        return;
                    }
                    
                    resolve(row);
                }
            );
        });
    }

    async updateExpense(id, updates) {
        return new Promise((resolve, reject) => {
            // First get the existing expense
            this.getExpenseById(id)
                .then(expense => {
                    if (!expense) {
                        reject(new Error('Expense not found'));
                        return;
                    }
                    
                    // Prepare update fields
                    const updateFields = [];
                    const updateValues = [];
                    
                    if (updates.description !== undefined) {
                        updateFields.push('description = ?');
                        updateValues.push(updates.description);
                    }
                    
                    if (updates.amount !== undefined) {
                        updateFields.push('amount = ?');
                        updateValues.push(parseFloat(updates.amount));
                    }
                    
                    if (updates.category !== undefined) {
                        updateFields.push('category = ?');
                        updateValues.push(updates.category);
                    }
                    
                    if (updates.date !== undefined) {
                        updateFields.push('date = ?');
                        updateValues.push(updates.date);
                    }
                    
                    if (updates.notes !== undefined) {
                        updateFields.push('notes = ?');
                        updateValues.push(updates.notes);
                    }
                    
                    if (updates.receipt_image !== undefined) {
                        updateFields.push('receipt_image = ?');
                        updateValues.push(updates.receipt_image);
                    }
                    
                    if (updateFields.length === 0) {
                        resolve(expense); // No updates to make
                        return;
                    }
                    
                    // Add ID to values for WHERE clause
                    updateValues.push(id);
                    
                    // Execute update
                    this.db.run(
                        `UPDATE expenses SET ${updateFields.join(', ')} WHERE id = ?`,
                        updateValues,
                        function(err) {
                            if (err) {
                                console.error('Error updating expense:', err);
                                reject(err);
                                return;
                            }
                            
                            if (this.changes === 0) {
                                reject(new Error('Expense not found or no changes made'));
                                return;
                            }
                            
                            // Get updated expense
                            this.getExpenseById(id)
                                .then(updatedExpense => {
                                    // Also update in JSON file
                                    try {
                                        const data = JSON.parse(fs.readFileSync(this.expensesFile));
                                        const index = data.expenses.findIndex(e => e.id === id);
                                        if (index !== -1) {
                                            data.expenses[index] = updatedExpense;
                                            fs.writeFileSync(this.expensesFile, JSON.stringify(data, null, 2));
                                        }
                                    } catch (fileErr) {
                                        console.error('Error updating expense in file:', fileErr);
                                    }
                                    
                                    resolve(updatedExpense);
                                })
                                .catch(reject);
                        }.bind(this)
                    );
                })
                .catch(reject);
        });
    }

    async deleteExpense(id) {
        return new Promise((resolve, reject) => {
            this.db.run(
                `DELETE FROM expenses WHERE id = ?`,
                [id],
                function(err) {
                    if (err) {
                        console.error('Error deleting expense:', err);
                        reject(err);
                        return;
                    }
                    
                    if (this.changes === 0) {
                        reject(new Error('Expense not found'));
                        return;
                    }
                    
                    // Also remove from JSON file
                    try {
                        const data = JSON.parse(fs.readFileSync(this.expensesFile));
                        data.expenses = data.expenses.filter(e => e.id !== id);
                        fs.writeFileSync(this.expensesFile, JSON.stringify(data, null, 2));
                    } catch (fileErr) {
                        console.error('Error removing expense from file:', fileErr);
                    }
                    
                    resolve({ success: true, id });
                }
            );
        });
    }

    async getExpenseCategories() {
        return [
            'Supplies',
            'Utilities',
            'Rent',
            'Salaries',
            'Equipment',
            'Marketing',
            'Maintenance',
            'Food',
            'Beverages',
            'Tobacco',
            'Other'
        ];
    }

    async getExpenseSummary(startDate, endDate) {
        return new Promise((resolve, reject) => {
            this.db.all(
                `SELECT category, SUM(amount) as total 
                FROM expenses 
                WHERE date >= ? AND date <= ? 
                GROUP BY category 
                ORDER BY total DESC`,
                [startDate, endDate],
                (err, rows) => {
                    if (err) {
                        console.error('Error getting expense summary:', err);
                        reject(err);
                        return;
                    }
                    
                    this.db.get(
                        `SELECT SUM(amount) as grandTotal FROM expenses WHERE date >= ? AND date <= ?`,
                        [startDate, endDate],
                        (err, result) => {
                            if (err) {
                                console.error('Error getting expense total:', err);
                                reject(err);
                                return;
                            }
                            
                            resolve({
                                categories: rows,
                                total: result ? result.grandTotal || 0 : 0
                            });
                        }
                    );
                }
            );
        });
    }
}

module.exports = ExpenseService;
