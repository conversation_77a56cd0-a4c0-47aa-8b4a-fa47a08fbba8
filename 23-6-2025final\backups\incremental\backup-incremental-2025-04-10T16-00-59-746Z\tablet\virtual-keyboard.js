// Improved Tablet-Friendly Virtual Keyboard
class VirtualKeyboard {
    constructor(targetInput) {
        this.targetInput = targetInput;
        this.isVisible = false;
        this.isMinimized = false;
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.offsetX = 0;
        this.offsetY = 0;
        this.shiftActive = false;
        this.justOpened = false; // Flag to prevent immediate closing
        this.createKeyboard();
        this.setupEventListeners();

        // Default position (centered and floating)
        this.position = {
            x: Math.max(window.innerWidth / 2 - 300, 0),
            y: Math.max(window.innerHeight / 2, 0)
        };
    }

    createKeyboard() {
        // Create keyboard container
        this.keyboardContainer = document.createElement('div');
        this.keyboardContainer.className = 'virtual-keyboard';
        this.keyboardContainer.style.display = 'none';

        // Create keyboard header for dragging and controls
        const keyboardHeader = document.createElement('div');
        keyboardHeader.className = 'keyboard-header';

        // Add title
        const keyboardTitle = document.createElement('div');
        keyboardTitle.className = 'keyboard-title';
        keyboardTitle.textContent = 'Keyboard';
        keyboardHeader.appendChild(keyboardTitle);

        // Add controls
        const keyboardControls = document.createElement('div');
        keyboardControls.className = 'keyboard-controls';

        // Minimize button
        this.minimizeButton = document.createElement('button');
        this.minimizeButton.className = 'keyboard-control-btn minimize-btn';
        this.minimizeButton.innerHTML = '−';
        this.minimizeButton.title = 'Minimize keyboard';
        keyboardControls.appendChild(this.minimizeButton);

        // Close button
        const closeButton = document.createElement('button');
        closeButton.className = 'keyboard-control-btn close-btn';
        closeButton.innerHTML = '×';
        closeButton.title = 'Close keyboard';
        keyboardControls.appendChild(closeButton);

        keyboardHeader.appendChild(keyboardControls);
        this.keyboardContainer.appendChild(keyboardHeader);

        // Create keyboard body (will be hidden when minimized)
        this.keyboardBody = document.createElement('div');
        this.keyboardBody.className = 'keyboard-body';

        // Create keyboard layout - optimized for tablet use
        const layout = [
            ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
            ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
            ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
            ['Shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.'],
            ['Space', 'Backspace', 'Done']
        ];

        // Create keyboard rows
        layout.forEach(row => {
            const keyboardRow = document.createElement('div');
            keyboardRow.className = 'keyboard-row';

            row.forEach(key => {
                const button = document.createElement('button');
                button.className = 'keyboard-key';
                button.textContent = key;

                // Special styling for certain keys
                if (key === 'Space') {
                    button.classList.add('space-key');
                } else if (key === 'Backspace') {
                    button.classList.add('backspace-key');
                } else if (key === 'Done') {
                    button.classList.add('done-key');
                } else if (key === 'Shift') {
                    button.classList.add('shift-key');
                }

                keyboardRow.appendChild(button);
            });

            this.keyboardBody.appendChild(keyboardRow);
        });

        this.keyboardContainer.appendChild(this.keyboardBody);

        // Add keyboard to the document
        document.body.appendChild(this.keyboardContainer);
    }

    setupEventListeners() {
        // Show keyboard when input is focused
        this.targetInput.addEventListener('focus', () => {
            this.show();
        });

        // Hide keyboard when input is blurred
        this.targetInput.addEventListener('blur', (e) => {
            // Check if the click was on the keyboard
            if (!this.justOpened && !this.keyboardContainer.contains(e.relatedTarget)) {
                // Hide the keyboard completely
                this.hide();
            }
        });

        // Handle keyboard clicks
        this.keyboardContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('keyboard-key')) {
                const key = e.target.textContent;
                this.handleKeyPress(key);
            } else if (e.target === this.minimizeButton) {
                this.toggleMinimize();
            } else if (e.target.classList.contains('close-btn')) {
                this.hide();
                this.targetInput.blur();
            }
        });

        // Make keyboard draggable
        const keyboardHeader = this.keyboardContainer.querySelector('.keyboard-header');
        keyboardHeader.addEventListener('mousedown', (e) => this.startDrag(e));
        keyboardHeader.addEventListener('touchstart', (e) => this.startDrag(e.touches[0]), { passive: false });

        document.addEventListener('mousemove', (e) => this.drag(e));
        document.addEventListener('touchmove', (e) => this.drag(e.touches[0]), { passive: false });

        document.addEventListener('mouseup', () => this.stopDrag());
        document.addEventListener('touchend', () => this.stopDrag());

        // Prevent default touch behavior to avoid scrolling while dragging
        keyboardHeader.addEventListener('touchmove', (e) => {
            if (this.isDragging) {
                e.preventDefault();
            }
        }, { passive: false });

        // Add document click event to hide keyboard when clicking outside
        document.addEventListener('click', (e) => {
            // Only hide if keyboard is visible, not just opened, and click is outside both the input and keyboard
            if (this.isVisible &&
                !this.justOpened &&
                !this.keyboardContainer.contains(e.target) &&
                e.target !== this.targetInput) {
                this.hide();
                this.targetInput.blur();
            }
        });

        // Add document touch event to hide keyboard when touching outside
        document.addEventListener('touchstart', (e) => {
            // Only hide if keyboard is visible, not just opened, and touch is outside both the input and keyboard
            if (this.isVisible &&
                !this.justOpened &&
                !this.keyboardContainer.contains(e.target) &&
                e.target !== this.targetInput) {
                this.hide();
                this.targetInput.blur();
            }
        });
    }

    startDrag(e) {
        this.isDragging = true;
        this.dragStartX = e.clientX;
        this.dragStartY = e.clientY;

        // Get current position
        const rect = this.keyboardContainer.getBoundingClientRect();
        this.offsetX = this.dragStartX - rect.left;
        this.offsetY = this.dragStartY - rect.top;

        // Add dragging class
        this.keyboardContainer.classList.add('dragging');
    }

    drag(e) {
        if (!this.isDragging) return;

        const x = e.clientX - this.offsetX;
        const y = e.clientY - this.offsetY;

        // Ensure keyboard stays within viewport
        const maxX = window.innerWidth - this.keyboardContainer.offsetWidth;
        const maxY = window.innerHeight - this.keyboardContainer.offsetHeight;

        this.position.x = Math.max(0, Math.min(x, maxX));
        this.position.y = Math.max(0, Math.min(y, maxY));

        this.updatePosition();
    }

    stopDrag() {
        this.isDragging = false;
        this.keyboardContainer.classList.remove('dragging');
    }

    updatePosition() {
        this.keyboardContainer.style.left = `${this.position.x}px`;
        this.keyboardContainer.style.top = `${this.position.y}px`;
    }

    handleKeyPress(key) {
        const currentValue = this.targetInput.value;

        switch (key) {
            case 'Backspace':
                this.targetInput.value = currentValue.slice(0, -1);
                break;
            case 'Space':
                this.targetInput.value = currentValue + ' ';
                break;
            case 'Done':
                this.hide();
                this.targetInput.blur();
                break;
            case 'Shift':
                this.toggleShift();
                break;
            default:
                // Apply shift if active
                const charToAdd = this.shiftActive ? key.toUpperCase() : key;
                this.targetInput.value = currentValue + charToAdd;

                // Auto-disable shift after one character
                if (this.shiftActive) {
                    this.toggleShift();
                }
        }

        // Trigger input event to update UI
        const event = new Event('input');
        this.targetInput.dispatchEvent(event);
    }

    toggleShift() {
        this.shiftActive = !this.shiftActive;
        const shiftKey = this.keyboardContainer.querySelector('.shift-key');

        if (this.shiftActive) {
            shiftKey.classList.add('active');

            // Update letter keys to uppercase
            const letterKeys = this.keyboardContainer.querySelectorAll('.keyboard-key:not(.shift-key):not(.space-key):not(.backspace-key):not(.done-key)');
            letterKeys.forEach(key => {
                if (key.textContent.length === 1 && key.textContent.match(/[a-z]/i)) {
                    key.textContent = key.textContent.toUpperCase();
                }
            });
        } else {
            shiftKey.classList.remove('active');

            // Update letter keys to lowercase
            const letterKeys = this.keyboardContainer.querySelectorAll('.keyboard-key:not(.shift-key):not(.space-key):not(.backspace-key):not(.done-key)');
            letterKeys.forEach(key => {
                if (key.textContent.length === 1 && key.textContent.match(/[A-Z]/i)) {
                    key.textContent = key.textContent.toLowerCase();
                }
            });
        }
    }

    toggleMinimize() {
        if (this.isMinimized) {
            this.maximize();
        } else {
            this.minimize();
        }
    }

    minimize() {
        this.keyboardBody.style.display = 'none';
        this.isMinimized = true;
        this.minimizeButton.innerHTML = '+';
        this.minimizeButton.title = 'Maximize keyboard';
        this.keyboardContainer.classList.add('minimized');
    }

    maximize() {
        this.keyboardBody.style.display = 'block';
        this.isMinimized = false;
        this.minimizeButton.innerHTML = '−';
        this.minimizeButton.title = 'Minimize keyboard';
        this.keyboardContainer.classList.remove('minimized');
    }

    show() {
        this.keyboardContainer.style.display = 'block';
        this.isVisible = true;

        // Position keyboard using stored position
        this.keyboardContainer.style.position = 'fixed';
        this.updatePosition();

        // If it was minimized before, maximize it
        if (this.isMinimized) {
            this.maximize();
        }

        // Add a small delay before allowing the keyboard to be closed by outside clicks
        // This prevents the keyboard from immediately closing when clicking on the input field
        this.justOpened = true;
        setTimeout(() => {
            this.justOpened = false;
        }, 300);
    }

    hide() {
        this.keyboardContainer.style.display = 'none';
        this.isVisible = false;
    }
}