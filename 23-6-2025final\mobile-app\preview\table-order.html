<!DOCTYPE html>
<html>
<head>
    <title>Paradiz App Preview - In Store</title>
    <style>
        body { font-family: Arial, sans-serif; display: flex; justify-content: center; padding: 20px; background-color: #f5f5f5; }
        .phone { width: 375px; height: 667px; background: white; border-radius: 30px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15); position: relative; }
        .header { background: #6B46C1; color: white; padding: 16px; display: flex; justify-content: space-between; align-items: center; }
        .table-info { font-size: 18px; font-weight: bold; }
        .order-info { font-size: 16px; }
        .content { height: calc(100% - 60px); overflow-y: auto; }
        .menu-section { padding: 16px; }
        .cart-section { padding: 16px; background-color: #F7FAFC; border-top: 1px solid #E2E8F0; }
        .categories { display: flex; margin-bottom: 16px; overflow-x: auto; }
        .category { background-color: white; border: 1px solid #6B46C1; color: #6B46C1; padding: 8px 16px; border-radius: 20px; margin-right: 8px; font-size: 14px; white-space: nowrap; cursor: pointer; }
        .category.active { background-color: #6B46C1; color: white; }
        .menu-item { background-color: white; border-radius: 8px; padding: 16px; margin-bottom: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
        .item-info { flex: 1; }
        .item-name { font-size: 16px; font-weight: bold; color: #2D3748; margin-bottom: 4px; }
        .item-price { color: #6B46C1; font-weight: bold; }
        .item-description { color: #4A5568; font-size: 14px; margin-top: 4px; }
        .add-button { background-color: #6B46C1; color: white; border: none; padding: 8px 16px; border-radius: 8px; font-weight: bold; font-size: 12px; cursor: pointer; margin-left: 12px; }
        .cart-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; }
        .cart-title { font-size: 18px; font-weight: bold; color: #2D3748; }
        .clear-cart { color: #E53E3E; font-size: 14px; cursor: pointer; }
        .cart-item { display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid #E2E8F0; }
        .cart-item-name { font-weight: 500; color: #2D3748; }
        .cart-item-price { color: #6B46C1; font-weight: bold; }
        .quantity-controls { display: flex; align-items: center; }
        .quantity-button { width: 24px; height: 24px; border-radius: 12px; background-color: #EDF2F7; display: flex; justify-content: center; align-items: center; cursor: pointer; }
        .quantity-text { margin: 0 8px; font-weight: bold; }
        .divider { height: 1px; background-color: #E2E8F0; margin: 16px 0; }
        .total-container { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; }
        .total-label { font-size: 16px; font-weight: bold; color: #2D3748; }
        .total-amount { font-size: 18px; font-weight: bold; color: #6B46C1; }
        .submit-button { background-color: #6B46C1; color: white; border: none; padding: 12px 20px; border-radius: 8px; font-weight: bold; display: block; width: 100%; cursor: pointer; }
        .status-banner { background-color: #38A169; color: white; text-align: center; padding: 8px; font-size: 14px; }
        .demo-controls { margin-top: 20px; text-align: center; }
        .button { background: #6B46C1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-weight: bold; cursor: pointer; }
        .section-title { font-size: 18px; font-weight: bold; margin-bottom: 16px; color: #2D3748; }

        /* Notification styles */
        .notification-bell { position: absolute; top: 15px; right: 20px; font-size: 24px; color: white; cursor: pointer; }
        .notification-badge { position: absolute; top: -5px; right: -5px; background-color: #E53E3E; color: white; border-radius: 50%; width: 18px; height: 18px; font-size: 12px; display: flex; align-items: center; justify-content: center; }
        .notification-toast { position: absolute; top: 70px; right: 10px; width: 300px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 100; overflow: hidden; display: none; }
        .toast-header { background-color: #6B46C1; color: white; padding: 12px 16px; display: flex; justify-content: space-between; align-items: center; }
        .toast-title { font-weight: bold; }
        .toast-close { cursor: pointer; }
        .toast-body { padding: 12px 16px; }
        .toast-message { margin-bottom: 12px; }
        .toast-actions { display: flex; justify-content: flex-end; }
        .toast-button { background-color: #6B46C1; color: white; border: none; padding: 8px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; margin-left: 8px; }
        .toast-button.secondary { background-color: transparent; color: #6B46C1; border: 1px solid #6B46C1; }

        /* Live performance notification */
        .live-notification { position: absolute; bottom: 70px; left: 0; right: 0; background-color: rgba(0,0,0,0.8); color: white; padding: 12px; display: flex; align-items: center; justify-content: space-between; }
        .live-icon { color: #E53E3E; margin-right: 8px; animation: pulse 2s infinite; }
        .live-info { flex: 1; }
        .live-title { font-weight: bold; margin-bottom: 4px; }
        .live-desc { font-size: 12px; opacity: 0.8; }
        .live-action { background-color: #E53E3E; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; }

        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
    </style>
</head>
<body>
    <div>
        <div class="phone">
            <div class="status-banner">
                You are viewing the app as an in-store customer (Table 15)
            </div>
            <div class="header">
                <div class="table-info">Table 15</div>
                <div class="order-info">Order #456</div>
                <div class="notification-bell" onclick="showNotification()">
                    🔔
                    <div class="notification-badge">1</div>
                </div>
            </div>

            <div class="notification-toast" id="notificationToast">
                <div class="toast-header">
                    <div class="toast-title">Special Event Tonight</div>
                    <div class="toast-close" onclick="hideNotification()">×</div>
                </div>
                <div class="toast-body">
                    <div class="toast-message">Live music starts at 9:00 PM tonight. Would you like to extend your reservation to enjoy the show?</div>
                    <div class="toast-actions">
                        <button class="toast-button secondary" onclick="hideNotification()">No Thanks</button>
                        <button class="toast-button" onclick="hideNotification()">Extend Reservation</button>
                    </div>
                </div>
            </div>

            <div class="content" id="mainContent">
                <div class="menu-section">
                    <div class="section-title">Menu Categories</div>
                    <div class="categories">
                        <div class="category active">All</div>
                        <div class="category">Hookahs</div>
                        <div class="category">Food</div>
                        <div class="category">Drinks</div>
                    </div>

                    <div class="section-title">Popular Items</div>
                    <div class="menu-item">
                        <div class="item-info">
                            <div class="item-name">Double Apple Hookah</div>
                            <div class="item-description">Classic double apple flavor</div>
                            <div class="item-price">$25.99</div>
                        </div>
                        <button class="add-button">Add</button>
                    </div>

                    <div class="menu-item">
                        <div class="item-info">
                            <div class="item-name">Mint Hookah</div>
                            <div class="item-description">Refreshing mint flavor</div>
                            <div class="item-price">$25.99</div>
                        </div>
                        <button class="add-button">Add</button>
                    </div>

                    <div class="menu-item">
                        <div class="item-info">
                            <div class="item-name">Hummus Plate</div>
                            <div class="item-description">Served with warm pita bread</div>
                            <div class="item-price">$8.99</div>
                        </div>
                        <button class="add-button">Add</button>
                    </div>

                    <div class="menu-item">
                        <div class="item-info">
                            <div class="item-name">Turkish Coffee</div>
                            <div class="item-description">Traditional Turkish coffee</div>
                            <div class="item-price">$4.99</div>
                        </div>
                        <button class="add-button">Add</button>
                    </div>
                </div>

                <div class="cart-section">
                    <div class="cart-header">
                        <div class="cart-title">Your Order</div>
                        <div class="clear-cart">Clear</div>
                    </div>

                    <div class="cart-item">
                        <div class="cart-item-name">Double Apple Hookah</div>
                        <div style="display: flex; align-items: center;">
                            <div class="cart-item-price">$25.99</div>
                            <div class="quantity-controls" style="margin-left: 12px;">
                                <div class="quantity-button">-</div>
                                <div class="quantity-text">1</div>
                                <div class="quantity-button">+</div>
                            </div>
                        </div>
                    </div>

                    <div class="cart-item">
                        <div class="cart-item-name">Turkish Coffee</div>
                        <div style="display: flex; align-items: center;">
                            <div class="cart-item-price">$9.98</div>
                            <div class="quantity-controls" style="margin-left: 12px;">
                                <div class="quantity-button">-</div>
                                <div class="quantity-text">2</div>
                                <div class="quantity-button">+</div>
                            </div>
                        </div>
                    </div>

                    <div class="divider"></div>

                    <div class="total-container">
                        <div class="total-label">Total:</div>
                        <div class="total-amount">$35.97</div>
                    </div>

                    <button class="submit-button">Place Order</button>
                </div>
            </div>

            <div class="live-notification" id="liveNotification">
                <div class="live-icon">●</div>
                <div class="live-info">
                    <div class="live-title">Live Music Starting Soon</div>
                    <div class="live-desc">Alex Rivera will be performing in 30 minutes</div>
                </div>
                <button class="live-action" onclick="toggleLiveNotification()">Hide</button>
            </div>

            <div class="nav">
                <div class="nav-item active">Home</div>
                <div class="nav-item">Menu</div>
                <div class="nav-item">Reserve</div>
                <div class="nav-item">Profile</div>
            </div>
        </div>

        <div class="demo-controls">
            <p>Switch between experiences:</p>
            <button class="button" onclick="window.location.href='index.html'">Out-of-Store View</button>
            <button class="button" style="background-color: #38A169;" disabled>In-Store View (After QR Scan)</button>
        </div>
    </div>

    <script>
        function showNotification() {
            document.getElementById('notificationToast').style.display = 'block';
        }

        function hideNotification() {
            document.getElementById('notificationToast').style.display = 'none';
        }

        function toggleLiveNotification() {
            const notification = document.getElementById('liveNotification');
            if (notification.style.display === 'none') {
                notification.style.display = 'flex';
            } else {
                notification.style.display = 'none';
            }
        }

        // Auto-show notification after 3 seconds
        setTimeout(function() {
            showNotification();
        }, 3000);
    </script>
</body>
</html>
