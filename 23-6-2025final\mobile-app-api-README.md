# Paradiz Hookah Lounge Mobile App API

This document describes the API endpoints for the Paradiz Hookah Lounge mobile app.

## Overview

The mobile app API provides endpoints for:

1. User authentication (login/register)
2. Menu browsing
3. Reservation management
4. In-house ordering
5. User profile management

## Setup

The API is integrated into the main POS server. To set it up:

1. Make sure the `mobile-app-api.js` file is in the root directory of your POS system
2. Ensure the file is required and initialized in `server.js`
3. Install required dependencies:
   ```
   npm install bcrypt jsonwebtoken
   ```
4. Run the server as usual with `node server.js`

## Database Schema

The API requires additional tables in the SQLite database. These are created automatically when the server starts, using the SQL statements in `mobile-app-schema.sql`.

## API Endpoints

### Authentication

#### Register a new user
- **URL**: `/api/auth/register`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
  ```

#### Login
- **URL**: `/api/auth/login`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
  ```

### Menu

#### Get menu categories
- **URL**: `/api/menu/categories`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "success": true,
    "categories": [
      {
        "id": 1,
        "name": "Hookahs"
      },
      {
        "id": 2,
        "name": "Food"
      }
    ]
  }
  ```

#### Get menu items
- **URL**: `/api/menu/items`
- **Method**: `GET`
- **Query Parameters**:
  - `category` (optional): Filter by category ID
- **Response**:
  ```json
  {
    "success": true,
    "items": [
      {
        "id": 1,
        "name": "Double Apple Hookah",
        "description": "Classic double apple flavor",
        "price": 25.99,
        "category_id": 1,
        "image": "url_to_image"
      }
    ]
  }
  ```

#### Get menu item details
- **URL**: `/api/menu/items/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "success": true,
    "item": {
      "id": 1,
      "name": "Double Apple Hookah",
      "description": "Classic double apple flavor",
      "price": 25.99,
      "category_id": 1,
      "image": "url_to_image"
    }
  }
  ```

#### Search menu items
- **URL**: `/api/menu/search`
- **Method**: `GET`
- **Query Parameters**:
  - `q`: Search query
- **Response**:
  ```json
  {
    "success": true,
    "items": [
      {
        "id": 1,
        "name": "Double Apple Hookah",
        "description": "Classic double apple flavor",
        "price": 25.99,
        "category_id": 1,
        "image": "url_to_image"
      }
    ]
  }
  ```

### Reservations

#### Get availability
- **URL**: `/api/reservations/availability`
- **Method**: `GET`
- **Query Parameters**:
  - `date`: Date in YYYY-MM-DD format
- **Response**:
  ```json
  {
    "success": true,
    "availableSlots": [
      "17:00",
      "17:30",
      "18:00"
    ]
  }
  ```

#### Create reservation
- **URL**: `/api/reservations`
- **Method**: `POST`
- **Authentication**: Required
- **Body**:
  ```json
  {
    "date": "2023-05-15",
    "time": "18:00",
    "partySize": 4,
    "specialRequests": "Window seat if possible"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "reservation": {
      "id": 1,
      "date": "2023-05-15",
      "time": "18:00",
      "partySize": 4,
      "specialRequests": "Window seat if possible",
      "status": "confirmed"
    }
  }
  ```

#### Get user reservations
- **URL**: `/api/reservations/user`
- **Method**: `GET`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "success": true,
    "reservations": [
      {
        "id": 1,
        "date": "2023-05-15",
        "time": "18:00",
        "partySize": 4,
        "specialRequests": "Window seat if possible",
        "status": "confirmed"
      }
    ]
  }
  ```

#### Cancel reservation
- **URL**: `/api/reservations/:id`
- **Method**: `DELETE`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "success": true,
    "message": "Reservation cancelled successfully"
  }
  ```

### Orders

#### Create order
- **URL**: `/api/orders`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "tableId": "15",
    "sessionId": "abc123xyz",
    "items": [
      {
        "id": 1,
        "quantity": 2,
        "price": 25.99,
        "customizations": []
      }
    ]
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "order": {
      "id": 1,
      "table_number": "15",
      "status": "new",
      "created_at": "2023-05-15T18:30:00.000Z"
    }
  }
  ```

#### Get table order
- **URL**: `/api/orders/table/:tableId`
- **Method**: `GET`
- **Query Parameters**:
  - `sessionId`: Session ID for security
- **Response**:
  ```json
  {
    "success": true,
    "order": {
      "id": 1,
      "table_number": "15",
      "status": "in_progress",
      "created_at": "2023-05-15T18:30:00.000Z"
    }
  }
  ```

#### Add items to order
- **URL**: `/api/orders/:id/items`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "items": [
      {
        "id": 2,
        "quantity": 1,
        "price": 8.99,
        "customizations": []
      }
    ]
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "order": {
      "id": 1,
      "table_number": "15",
      "status": "in_progress",
      "created_at": "2023-05-15T18:30:00.000Z"
    }
  }
  ```

#### Get user orders
- **URL**: `/api/orders/user`
- **Method**: `GET`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "success": true,
    "orders": [
      {
        "id": 1,
        "table_number": "15",
        "status": "completed",
        "created_at": "2023-05-15T18:30:00.000Z"
      }
    ]
  }
  ```

#### Get order details
- **URL**: `/api/orders/:id`
- **Method**: `GET`
- **Response**:
  ```json
  {
    "success": true,
    "order": {
      "id": 1,
      "table_number": "15",
      "status": "completed",
      "created_at": "2023-05-15T18:30:00.000Z",
      "items": [
        {
          "id": 1,
          "item_id": 1,
          "quantity": 2,
          "price": 25.99,
          "name": "Double Apple Hookah",
          "description": "Classic double apple flavor"
        }
      ]
    }
  }
  ```

#### Pay for order
- **URL**: `/api/orders/:id/pay`
- **Method**: `POST`
- **Authentication**: Required
- **Body**:
  ```json
  {
    "paymentMethod": "credit_card",
    "amount": 60.97,
    "tip": 10.00
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Payment processed successfully"
  }
  ```

### User Profile

#### Get user profile
- **URL**: `/api/users/profile`
- **Method**: `GET`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "success": true,
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2023-05-01T12:00:00.000Z"
    }
  }
  ```

#### Update user profile
- **URL**: `/api/users/profile`
- **Method**: `PUT`
- **Authentication**: Required
- **Body**:
  ```json
  {
    "name": "John Smith",
    "email": "<EMAIL>"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "user": {
      "id": 1,
      "name": "John Smith",
      "email": "<EMAIL>"
    }
  }
  ```

### Favorites

#### Get user favorites
- **URL**: `/api/users/favorites`
- **Method**: `GET`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "success": true,
    "favorites": [
      {
        "id": 1,
        "name": "Double Apple Hookah",
        "description": "Classic double apple flavor",
        "price": 25.99,
        "category_id": 1,
        "image": "url_to_image"
      }
    ]
  }
  ```

#### Add favorite
- **URL**: `/api/users/favorites`
- **Method**: `POST`
- **Authentication**: Required
- **Body**:
  ```json
  {
    "itemId": 1
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Item added to favorites"
  }
  ```

#### Remove favorite
- **URL**: `/api/users/favorites/:itemId`
- **Method**: `DELETE`
- **Authentication**: Required
- **Response**:
  ```json
  {
    "success": true,
    "message": "Item removed from favorites"
  }
  ```

## QR Code System

The API includes functionality to generate QR codes for each table in the establishment. These QR codes contain:

1. Table ID
2. Session ID (for security)
3. Timestamp

When scanned with the mobile app, these QR codes allow customers to place orders directly from their table.

The QR codes are generated automatically when the server starts and are saved in the `public/table-qr-codes` directory.

## Authentication

The API uses JSON Web Tokens (JWT) for authentication. When a user logs in or registers, they receive a token that should be included in the `Authorization` header of subsequent requests:

```
Authorization: Bearer <token>
```

## Error Handling

All endpoints return a consistent error format:

```json
{
  "success": false,
  "message": "Error message here"
}
```

HTTP status codes are also used appropriately:
- 200: Success
- 400: Bad request (invalid input)
- 401: Unauthorized (missing or invalid token)
- 403: Forbidden (valid token but insufficient permissions)
- 404: Not found
- 500: Server error
