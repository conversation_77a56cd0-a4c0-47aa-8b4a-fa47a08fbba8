document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const backBtn = document.getElementById('back-btn');
    const printerEnabled = document.getElementById('printer-enabled');
    const printerTypeRadios = document.querySelectorAll('input[name="printer-type"]');
    const usbPrinterConfig = document.getElementById('usb-printer-config');
    const networkPrinterConfig = document.getElementById('network-printer-config');
    const serialPrinterConfig = document.getElementById('serial-printer-config');
    const listUsbPrintersBtn = document.getElementById('list-usb-printers');
    const usbPrintersList = document.getElementById('usb-printers-list');
    const usbPrinterSelect = document.getElementById('usb-printer-select');
    const usbVendorId = document.getElementById('usb-vendor-id');
    const usbProductId = document.getElementById('usb-product-id');
    const networkPrinterIp = document.getElementById('network-printer-ip');
    const networkPrinterPort = document.getElementById('network-printer-port');
    const serialPortPath = document.getElementById('serial-port-path');
    const characterSet = document.getElementById('character-set');
    const savePrinterSettingsBtn = document.getElementById('save-printer-settings');
    const testKitchenPrinterBtn = document.getElementById('test-kitchen-printer');
    const testHookahPrinterBtn = document.getElementById('test-hookah-printer');
    const printerStatus = document.getElementById('printer-status');

    // Initialize
    loadPrinterSettings();

    // Event Listeners
    backBtn.addEventListener('click', function() {
        window.location.href = '../index.html';
    });

    // Printer type selection
    printerTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updatePrinterConfigVisibility(this.value);
        });
    });

    // List USB printers
    listUsbPrintersBtn.addEventListener('click', function() {
        listUsbPrinters();
    });

    // USB printer selection
    usbPrinterSelect.addEventListener('change', function() {
        if (this.value) {
            const [vendorId, productId] = this.value.split(':');
            usbVendorId.value = vendorId;
            usbProductId.value = productId;
        }
    });

    // Save printer settings
    savePrinterSettingsBtn.addEventListener('click', function() {
        savePrinterSettings();
    });

    // Test printers
    testKitchenPrinterBtn.addEventListener('click', function() {
        testPrinter('kitchen');
    });

    testHookahPrinterBtn.addEventListener('click', function() {
        testPrinter('hookah');
    });

    /**
     * Update printer configuration visibility based on selected type
     * @param {string} type - Printer type
     */
    function updatePrinterConfigVisibility(type) {
        // Hide all printer configs
        usbPrinterConfig.classList.remove('active');
        networkPrinterConfig.classList.remove('active');
        serialPrinterConfig.classList.remove('active');

        // Show selected printer config
        if (type === 'usb') {
            usbPrinterConfig.classList.add('active');
        } else if (type === 'network') {
            networkPrinterConfig.classList.add('active');
        } else if (type === 'serial') {
            serialPrinterConfig.classList.add('active');
        }
    }

    /**
     * Load printer settings from server
     */
    function loadPrinterSettings() {
        fetch('http://localhost:5000/api/printer-settings')
            .then(response => response.json())
            .then(settings => {
                if (settings && settings.success) {
                    const config = settings.config;

                    // Set enabled state
                    printerEnabled.checked = config.enabled;

                    // Set printer type
                    const printerTypeRadio = document.querySelector(`input[name="printer-type"][value="${config.printerType}"]`);
                    if (printerTypeRadio) {
                        printerTypeRadio.checked = true;
                        updatePrinterConfigVisibility(config.printerType);
                    }

                    // Set USB printer settings
                    if (config.usbVendorId) usbVendorId.value = config.usbVendorId;
                    if (config.usbProductId) usbProductId.value = config.usbProductId;

                    // Set network printer settings
                    networkPrinterIp.value = config.networkPrinterIP || '';
                    networkPrinterPort.value = config.networkPrinterPort || 9100;

                    // Set serial printer settings
                    serialPortPath.value = config.serialPortPath || '';

                    // Set character set
                    if (config.characterSet) {
                        const option = document.querySelector(`#character-set option[value="${config.characterSet}"]`);
                        if (option) option.selected = true;
                    }

                    showStatus('Printer settings loaded successfully', 'success');
                } else {
                    console.log('No printer settings found or error loading settings');
                }
            })
            .catch(error => {
                console.error('Error loading printer settings:', error);
                showStatus('Error loading printer settings', 'error');
            });
    }

    /**
     * Save printer settings to server
     */
    function savePrinterSettings() {
        const printerType = document.querySelector('input[name="printer-type"]:checked').value;

        const settings = {
            enabled: printerEnabled.checked,
            printerType: printerType,
            characterSet: characterSet.value
        };

        // Add type-specific settings
        if (printerType === 'usb') {
            settings.usbVendorId = usbVendorId.value;
            settings.usbProductId = usbProductId.value;
        } else if (printerType === 'network') {
            settings.networkPrinterIP = networkPrinterIp.value;
            settings.networkPrinterPort = parseInt(networkPrinterPort.value);
        } else if (printerType === 'serial') {
            settings.serialPortPath = serialPortPath.value;
        }

        fetch('http://localhost:5000/api/printer-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatus('Printer settings saved successfully', 'success');
            } else {
                showStatus(`Error saving printer settings: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error saving printer settings:', error);
            showStatus('Error saving printer settings', 'error');
        });
    }

    /**
     * List available USB printers
     */
    function listUsbPrinters() {
        showStatus('Searching for USB printers...', 'info');

        fetch('http://localhost:5000/api/printer-list')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.printers && data.printers.length > 0) {
                    // Clear existing options
                    usbPrinterSelect.innerHTML = '<option value="">Select a printer...</option>';

                    // Add printers to select
                    data.printers.forEach(printer => {
                        const option = document.createElement('option');
                        option.value = `${printer.vendorId}:${printer.productId}`;
                        option.textContent = `${printer.manufacturer || 'Unknown'} (VID:${printer.vendorId} PID:${printer.productId})`;
                        usbPrinterSelect.appendChild(option);
                    });

                    // Show the select
                    usbPrintersList.style.display = 'block';

                    showStatus(`Found ${data.printers.length} USB printer(s)`, 'success');
                } else {
                    showStatus('No USB printers found', 'error');
                    usbPrintersList.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error listing USB printers:', error);
                showStatus('Error listing USB printers', 'error');
                usbPrintersList.style.display = 'none';
            });
    }

    /**
     * Test a printer
     * @param {string} type - 'kitchen' or 'hookah'
     */
    function testPrinter(type) {
        showStatus(`Testing ${type} printer...`, 'info');

        fetch(`http://localhost:5000/api/printer-test?type=${type}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus(`${type} printer test successful`, 'success');
                } else {
                    showStatus(`${type} printer test failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error(`Error testing ${type} printer:`, error);
                showStatus(`Error testing ${type} printer`, 'error');
            });
    }

    /**
     * Show status message
     * @param {string} message - Status message
     * @param {string} type - 'success', 'error', or 'info'
     */
    function showStatus(message, type = 'info') {
        printerStatus.textContent = message;
        printerStatus.className = 'printer-status';

        if (type === 'success') {
            printerStatus.classList.add('status-success');
        } else if (type === 'error') {
            printerStatus.classList.add('status-error');
        } else {
            printerStatus.style.display = 'block';
            printerStatus.style.backgroundColor = '#e2f3fd';
            printerStatus.style.color = '#0c5460';
            printerStatus.style.border = '1px solid #bee5eb';
        }

        // Hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                printerStatus.style.display = 'none';
            }, 5000);
        }
    }
});
