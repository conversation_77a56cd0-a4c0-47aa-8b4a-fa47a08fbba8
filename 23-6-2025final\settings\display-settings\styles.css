/* Display Settings Styles */
:root {
    /* Light theme variables */
    --background-color: #f4f4f4;
    --container-bg: #ffffff;
    --text-color: #333333;
    --heading-color: #2c3e50;
    --border-color: #e0e0e0;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    --button-primary-bg: #3498db;
    --button-primary-color: #ffffff;
    --button-secondary-bg: #e0e0e0;
    --button-secondary-color: #333333;
    --input-bg: #ffffff;
    --input-border: #cccccc;
    --input-color: #333333;
}

/* Dark theme variables */
body.dark-theme {
    --background-color: #1a1a1a;
    --container-bg: #2c2c2c;
    --text-color: #f0f0f0;
    --heading-color: #f8f9fa;
    --border-color: #444444;
    --card-bg: #343a40;
    --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --button-primary-bg: #2980b9;
    --button-primary-color: #ffffff;
    --button-secondary-bg: #495057;
    --button-secondary-color: #f0f0f0;
    --input-bg: #3a3a3a;
    --input-border: #555555;
    --input-color: #f0f0f0;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    background-color: var(--container-bg);
    color: var(--text-color);
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

.settings-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 20px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.settings-section h2 {
    color: var(--heading-color);
    margin-top: 0;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.settings-description {
    margin-bottom: 20px;
    color: var(--text-color);
    font-size: 14px;
}

.theme-toggle-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.theme-option {
    position: relative;
}

.theme-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.theme-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.theme-preview.light {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #333;
}

.theme-preview.dark {
    background-color: #343a40;
    border: 2px solid #495057;
    color: white;
}

.theme-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.theme-name {
    font-weight: bold;
}

/* Selected state */
.theme-option input[type="radio"]:checked + label .theme-preview.light {
    border-color: var(--button-primary-bg);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.theme-option input[type="radio"]:checked + label .theme-preview.dark {
    border-color: #9b59b6;
    box-shadow: 0 0 0 2px rgba(155, 89, 182, 0.2);
}

/* Hover effects */
.theme-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Theme Preview Container */
.theme-preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.preview-item {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: var(--card-shadow);
}

.preview-item h3 {
    color: var(--heading-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.preview-header {
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    padding: 10px 15px;
    border-radius: 4px;
    font-weight: bold;
}

.preview-buttons {
    display: flex;
    gap: 10px;
}

.preview-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.preview-button.primary {
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
}

.preview-button.secondary {
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-color);
}

.preview-card {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.preview-card-header {
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-color);
    padding: 10px;
    font-weight: bold;
}

.preview-card-body {
    padding: 10px;
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* Back button styles */
.back-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    padding: 12px 25px;
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    text-decoration: none;
    font-weight: bold;
    font-size: 16px;
}

.back-btn i {
    margin-right: 8px;
}

.back-btn:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.back-btn:active {
    transform: translateY(0);
}

