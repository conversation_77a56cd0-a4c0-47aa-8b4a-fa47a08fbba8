<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Printer Settings - <PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles.css">
    <script src="../../theme.js"></script>
    <style>
        .header-with-back {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .back-button {
            background-color: transparent;
            border: none;
            font-size: 24px;
            cursor: pointer;
            margin-right: 15px;
            color: var(--text-color);
        }

        .printer-settings-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .printer-type-selector {
            margin-bottom: 20px;
        }

        .printer-type-selector label {
            margin-right: 15px;
            cursor: pointer;
        }

        .printer-config-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: var(--card-shadow);
        }

        .printer-config-section h3 {
            margin-top: 0;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }

        .printer-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .printer-actions button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .save-btn {
            background-color: var(--button-primary-bg);
            color: var(--button-primary-color);
        }

        .test-btn {
            background-color: var(--button-secondary-bg);
            color: var(--button-secondary-color);
        }

        .printer-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            display: block;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            display: block;
        }

        .printer-config {
            display: none;
        }

        .printer-config.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-with-back">
            <button class="back-button" id="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>Printer Settings</h1>
        </div>

        <div class="printer-settings-container">
            <div class="settings-section">
                <h2>Thermal Printer Configuration</h2>
                <p class="settings-description">Configure your thermal printers for kitchen and hookah tickets.</p>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="printer-enabled" checked>
                        Enable Thermal Printer Support
                    </label>
                </div>

                <div class="printer-type-selector">
                    <h3>Printer Connection Type</h3>
                    <label>
                        <input type="radio" name="printer-type" value="usb" checked> USB Printer
                    </label>
                    <label>
                        <input type="radio" name="printer-type" value="network"> Network Printer
                    </label>
                    <label>
                        <input type="radio" name="printer-type" value="serial"> Serial Printer
                    </label>
                </div>

                <!-- USB Printer Configuration -->
                <div id="usb-printer-config" class="printer-config printer-config-section active">
                    <h3>USB Printer Settings</h3>
                    <p>Connect your thermal printer via USB and configure it below.</p>

                    <div class="form-group">
                        <button id="list-usb-printers" class="test-btn">
                            <i class="fas fa-search"></i> Find USB Printers
                        </button>
                    </div>

                    <div id="usb-printers-list" class="form-group" style="display: none;">
                        <label>Available USB Printers:</label>
                        <select id="usb-printer-select">
                            <option value="">Select a printer...</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="usb-vendor-id">Vendor ID (optional):</label>
                        <input type="text" id="usb-vendor-id" placeholder="e.g., 0x0416">
                    </div>

                    <div class="form-group">
                        <label for="usb-product-id">Product ID (optional):</label>
                        <input type="text" id="usb-product-id" placeholder="e.g., 0x5011">
                    </div>
                </div>

                <!-- Network Printer Configuration -->
                <div id="network-printer-config" class="printer-config printer-config-section">
                    <h3>Network Printer Settings</h3>
                    <p>Configure your network-connected thermal printer.</p>

                    <div class="form-group">
                        <label for="network-printer-ip">Printer IP Address:</label>
                        <input type="text" id="network-printer-ip" placeholder="e.g., *************">
                    </div>

                    <div class="form-group">
                        <label for="network-printer-port">Printer Port:</label>
                        <input type="number" id="network-printer-port" value="9100">
                    </div>
                </div>

                <!-- Serial Printer Configuration -->
                <div id="serial-printer-config" class="printer-config printer-config-section">
                    <h3>Serial Printer Settings</h3>
                    <p>Configure your serial-connected thermal printer.</p>

                    <div class="form-group">
                        <label for="serial-port-path">Serial Port Path:</label>
                        <input type="text" id="serial-port-path" placeholder="e.g., /dev/usb/lp0 or COM1">
                    </div>
                </div>

                <!-- Common Printer Settings -->
                <div class="printer-config-section">
                    <h3>General Printer Settings</h3>

                    <div class="form-group">
                        <label for="character-set">Character Set:</label>
                        <select id="character-set">
                            <option value="PC437_USA">PC437 (USA, Standard Europe)</option>
                            <option value="PC850_MULTILINGUAL">PC850 (Multilingual)</option>
                            <option value="PC860_PORTUGUESE">PC860 (Portuguese)</option>
                            <option value="PC863_CANADIAN_FRENCH">PC863 (Canadian French)</option>
                            <option value="PC865_NORDIC">PC865 (Nordic)</option>
                            <option value="PC851_GREEK">PC851 (Greek)</option>
                            <option value="PC857_TURKISH">PC857 (Turkish)</option>
                            <option value="PC737_GREEK">PC737 (Greek)</option>
                            <option value="ISO8859_7_GREEK">ISO8859-7 (Greek)</option>
                            <option value="WPC1252">WPC1252</option>
                            <option value="PC866_CYRILLIC2">PC866 (Cyrillic #2)</option>
                            <option value="PC852_LATIN2">PC852 (Latin 2)</option>
                            <option value="SLOVENIA">Slovenia</option>
                            <option value="PC858_EURO">PC858 (Euro)</option>
                            <option value="WPC775_BALTIC_RIM">WPC775 (Baltic Rim)</option>
                            <option value="PC855_CYRILLIC">PC855 (Cyrillic)</option>
                            <option value="PC861_ICELANDIC">PC861 (Icelandic)</option>
                            <option value="PC862_HEBREW">PC862 (Hebrew)</option>
                            <option value="PC864_ARABIC">PC864 (Arabic)</option>
                            <option value="PC869_GREEK">PC869 (Greek)</option>
                            <option value="ISO8859_2_LATIN2">ISO8859-2 (Latin 2)</option>
                            <option value="ISO8859_15_LATIN9">ISO8859-15 (Latin 9)</option>
                            <option value="PC1125_UKRANIAN">PC1125 (Ukrainian)</option>
                            <option value="WPC1250_LATIN2">WPC1250 (Latin 2)</option>
                            <option value="WPC1251_CYRILLIC">WPC1251 (Cyrillic)</option>
                            <option value="WPC1253_GREEK">WPC1253 (Greek)</option>
                            <option value="WPC1254_TURKISH">WPC1254 (Turkish)</option>
                            <option value="WPC1255_HEBREW">WPC1255 (Hebrew)</option>
                            <option value="WPC1256_ARABIC">WPC1256 (Arabic)</option>
                            <option value="WPC1257_BALTIC_RIM">WPC1257 (Baltic Rim)</option>
                            <option value="WPC1258_VIETNAMESE">WPC1258 (Vietnamese)</option>
                            <option value="KZ1048_KAZAKHSTAN">KZ-1048 (Kazakhstan)</option>
                        </select>
                    </div>
                </div>

                <div class="printer-actions">
                    <button id="save-printer-settings" class="save-btn">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                    <button id="test-kitchen-printer" class="test-btn">
                        <i class="fas fa-utensils"></i> Test Kitchen Printer
                    </button>
                    <button id="test-hookah-printer" class="test-btn">
                        <i class="fas fa-smoking"></i> Test Hookah Printer
                    </button>
                </div>

                <div id="printer-status" class="printer-status"></div>
            </div>
        </div>
    </div>

    <script src="printer.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</body>
</html>
