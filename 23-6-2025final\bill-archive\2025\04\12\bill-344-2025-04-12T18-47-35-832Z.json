{"id": 344, "tableNumber": "2", "customerName": "<PERSON>i<PERSON><PERSON>", "items": [{"name": "Double apple", "price": 25, "quantity": 1, "notes": ""}, {"name": "Wings", "price": 9.99, "quantity": 1, "notes": ""}], "coverCharge": "[object Object]", "paid": 1, "status": "pending", "kitchenStatus": "pending", "report": null, "timestamp": "2025-04-12T18:47:35.832Z", "paymentMethod": "card", "transactionId": null, "payment": {"paymentMethod": "card", "tip": 0, "subtotal": 34.99, "tax": 1.75, "taxRate": 0.05001428979708488, "gst": 1.75, "gstRate": 0.05001428979708488, "pst": 0, "pstRate": 0, "total": 36.74, "transactionId": null}, "archiveTimestamp": "2025-04-12T18:47:55.851Z"}