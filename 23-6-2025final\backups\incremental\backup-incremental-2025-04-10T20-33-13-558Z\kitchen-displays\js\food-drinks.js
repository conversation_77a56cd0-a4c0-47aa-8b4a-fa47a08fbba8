document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM fully loaded');
    initializeDisplay();
});

function initializeDisplay() {
    // First immediate update
    updateOrders();

    // Set up regular polling
    setInterval(updateOrders, 5000);
    setInterval(updateTimers, 1000);

    console.log('Display initialized with auto-refresh');
}

async function updateOrders() {
    console.log('Attempting to fetch orders...');
    const container = document.getElementById('orders-container');

    try {
        const response = await fetch('http://localhost:5000/orders');

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const orders = await response.json();
        console.log('Received orders:', orders);

        if (!Array.isArray(orders)) {
            throw new Error('Invalid orders data format');
        }

        renderOrders(orders);
    } catch (error) {
        console.error('Failed to update orders:', error);
        container.innerHTML = `<div class="no-orders">Error: ${error.message}</div>`;
    }
}

function renderOrders(orders) {
    const container = document.getElementById('orders-container');
    const foodOrders = filterFoodOrders(orders);

    if (foodOrders.length === 0) {
        console.log('No food orders to display');
        container.innerHTML = '<div class="no-orders">No food/drink orders at this time</div>';
        return;
    }

    console.log(`Rendering ${foodOrders.length} food orders`);
    const orderElements = foodOrders.map(order => createOrderCard(order));
    container.innerHTML = orderElements.join('');

    // Add event listeners after adding HTML
    foodOrders.forEach(order => {
        const button = container.querySelector(`.order-card[data-order-id="${order.id}"] .complete-btn`);
        if (button) {
            button.addEventListener('click', () => completeOrder(order.id));
        }
    });
}

function createOrderCard(order) {
    console.log('Creating order card for order:', order.id);
    const foodItems = filterFoodItems(order.items);

    return `
        <div class="order-card" data-order-id="${order.id}" data-timestamp="${order.timestamp}">
            <div class="order-info">
                <div class="table-number">${order.tableNumber || 'N/A'}</div>
                <div class="customer-name">${order.customerName || ''}</div>
            </div>
            <div class="order-items">
                ${foodItems.map(item => `
                    <div class="order-item">
                        ${item.name} ${item.notes ? `(${item.notes})` : ''}
                    </div>
                `).join('')}
            </div>
            <div class="order-footer">
                <span class="order-timer">${formatTime(order.timestamp)}</span>
                <button class="complete-btn" type="button">Complete</button>
            </div>
        </div>
    `;
}

function filterFoodOrders(orders) {
    return orders.filter(order => {
        try {
            const hasFoodItems = filterFoodItems(order.items).length > 0;
            const kitchenStatus = order.kitchenStatus || {};
            return hasFoodItems && kitchenStatus.food !== 'completed';
        } catch (e) {
            console.warn('Error filtering order:', e);
            return false;
        }
    });
}

function filterFoodItems(items) {
    if (!Array.isArray(items)) return [];

    // First, check if there's a hookah in the order
    const hasHookah = items.some(item =>
        item?.name?.toLowerCase() === 'hookah' ||
        item?.name?.toLowerCase() === 'shisha'
    );

    // Define categories to exclude
    const excludeCategories = ['hookah', 'shisha', 'cover charge', 'entry fee'];

    // Define specific items to exclude (exact matches only)
    const excludeExactItems = [
        'hookah', 'shisha', 'cover', 'entry'
    ];

    // Define hookah flavors to exclude (exact matches only)
    const hookahFlavors = [
        'mint', 'grape', 'watermelon', 'double apple', 'midnight',
        'blueberry', 'blueberry1', 'mango', 'pineapple', 'orange-mint', 'lemon',
        'cherry', 'vanilla', 'bluemist', 'blue mist', 'coffee', 'coconut',
        'peach', 'raspberry', 'blackberry', 'kiwi', 'banana',
        'melon', 'pomegranate', 'guava', 'lychee', 'rose'
    ];

    // Add debug logging
    console.log('Filtering food items from:', items);

    return items.filter(item => {
        if (!item?.name) return false;

        const name = item.name.toLowerCase();
        const price = parseFloat(item.price);

        // Check if this is a hookah flavor
        const isHookahFlavor = hookahFlavors.some(flavor => name.includes(flavor));

        // If it's a hookah flavor, exclude it
        // We'll exclude it regardless of whether there's a hookah in the order
        // This ensures all hookah flavors go to the hookah display
        if (isHookahFlavor) {
            console.log('Excluding hookah flavor from food display:', item.name);
            return false;
        }

        // Also check for flavor combinations (e.g., "Mint + Blueberry")
        if (name.includes('+') &&
            hookahFlavors.some(flavor => name.includes(flavor))) {
            console.log('Excluding hookah flavor combination from food display:', item.name);
            return false;
        }

        // Check if this is an exact match for excluded items
        if (excludeExactItems.includes(name)) {
            return false;
        }

        // Check if this item belongs to an excluded category
        if (excludeCategories.some(category => name.startsWith(category))) {
            return false;
        }

        // Special case for items that have the same name as hookah flavors but are actually food/drinks
        // For example, "Mint Tea" or "Lemon Cake" should be included
        // We'll check if the item name contains a hookah flavor but also contains other words
        // AND the item has a price (since hookah flavors often have zero price)
        const isHookahFlavorInFoodItem = hookahFlavors.some(flavor => {
            // Check if the name contains the flavor but is not exactly the flavor
            return name.includes(flavor) && name !== flavor;
        });

        // If it's a food item that happens to contain a hookah flavor name, include it
        // But only if it has a price and there's no hookah in the order
        if (isHookahFlavorInFoodItem && price > 0 && !hasHookah) {
            return true;
        }

        // Check if this is a standalone hookah flavor (exact match)
        if (hookahFlavors.includes(name)) {
            return false;
        }

        // Additional check for hookah orders: if there's a hookah in the order,
        // and this item has a common hookah flavor word in it, exclude it
        if (hasHookah) {
            // Common words used in hookah flavor descriptions
            const hookahFlavorWords = ['flavor', 'flavour', 'mix', 'blend', 'tobacco'];
            if (hookahFlavorWords.some(word => name.includes(word))) {
                return false;
            }
        }

        // Include all other items
        return true;
    });
}

function getKitchenStatus(order) {
    return order.kitchenStatus || {};
}

function formatTime(timestamp) {
    if (!timestamp) return "0:00";
    try {
        const now = new Date();
        const start = new Date(timestamp);

        // Validate the dates
        if (isNaN(start.getTime()) || isNaN(now.getTime())) {
            console.warn('Invalid date:', { timestamp, now });
            return "0:00";
        }

        // Calculate time difference in seconds
        const diff = Math.max(0, Math.floor((now - start) / 1000));
        const minutes = Math.floor(diff / 60);
        const seconds = diff % 60;

        // Format with leading zeros
        const formattedTime = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        console.log('Formatted time:', { timestamp, now, diff, formattedTime });
        return formattedTime;
    } catch (e) {
        console.warn('Time format error:', e);
        return "0:00";
    }
}

function updateTimers() {
    document.querySelectorAll('.order-timer').forEach(timer => {
        const card = timer.closest('.order-card');
        if (card) {
            const timestamp = card.getAttribute('data-timestamp');
            if (timestamp) {
                timer.textContent = formatTime(timestamp);
            }
        }
    });
}

async function completeOrder(orderId) {
    console.log('Completing order:', orderId);

    // Show confirmation dialog
    const isConfirmed = confirm('Are you sure you want to mark this order as complete?');
    if (!isConfirmed) return;

    // Find and disable the button immediately
    const button = document.querySelector(`.order-card[data-order-id="${orderId}"] .complete-btn`);
    if (button) {
        button.disabled = true;
        button.textContent = 'Completing...';
    }

    try {
        const response = await fetch(`http://localhost:5000/orders/${orderId}/kitchen`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                status: 'completed',
                type: 'food'
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Remove the completed order card
        const orderCard = document.querySelector(`.order-card[data-order-id="${orderId}"]`);
        if (orderCard) {
            orderCard.remove();
        }

        // Update the display
        await updateOrders();
    } catch (error) {
        console.error('Error completing order:', error);
        // Re-enable the button if there was an error
        if (button) {
            button.disabled = false;
            button.textContent = 'Complete';
        }
        alert('Failed to complete order. Please try again.');
    }
}