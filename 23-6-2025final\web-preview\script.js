document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation items
    const navItems = document.querySelectorAll('.nav-item');
    
    // Get all screens
    const homeScreen = document.querySelector('.home-screen');
    const menuScreen = document.querySelector('.menu-screen');
    const reservationScreen = document.querySelector('.reservation-screen');
    const profileScreen = document.querySelector('.profile-screen');
    
    // Add click event to navigation items
    navItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            // Remove active class from all nav items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked nav item
            this.classList.add('active');
            
            // Hide all screens
            homeScreen.classList.add('hidden');
            menuScreen.classList.add('hidden');
            reservationScreen.classList.add('hidden');
            profileScreen.classList.add('hidden');
            
            // Show the selected screen
            if (index === 0) {
                homeScreen.classList.remove('hidden');
            } else if (index === 1) {
                menuScreen.classList.remove('hidden');
            } else if (index === 2) {
                reservationScreen.classList.remove('hidden');
            } else if (index === 3) {
                profileScreen.classList.remove('hidden');
            }
        });
    });
    
    // Quick links in home screen
    const quickLinks = document.querySelectorAll('.quick-link');
    quickLinks.forEach((link, index) => {
        link.addEventListener('click', function() {
            // Remove active class from all nav items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to corresponding nav item
            if (index === 0) {
                navItems[1].classList.add('active'); // Menu
                homeScreen.classList.add('hidden');
                menuScreen.classList.remove('hidden');
                reservationScreen.classList.add('hidden');
                profileScreen.classList.add('hidden');
            } else if (index === 1) {
                navItems[2].classList.add('active'); // Reservations
                homeScreen.classList.add('hidden');
                menuScreen.classList.add('hidden');
                reservationScreen.classList.remove('hidden');
                profileScreen.classList.add('hidden');
            } else if (index === 2) {
                navItems[3].classList.add('active'); // Profile
                homeScreen.classList.add('hidden');
                menuScreen.classList.add('hidden');
                reservationScreen.classList.add('hidden');
                profileScreen.classList.remove('hidden');
            }
        });
    });
    
    // Category selection in menu screen
    const categories = document.querySelectorAll('.category');
    categories.forEach(category => {
        category.addEventListener('click', function() {
            categories.forEach(cat => cat.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Time slot selection in reservation screen
    const timeSlots = document.querySelectorAll('.time-slot');
    timeSlots.forEach(slot => {
        slot.addEventListener('click', function() {
            timeSlots.forEach(s => s.style.backgroundColor = 'white');
            this.style.backgroundColor = '#EBF4FF';
        });
    });
    
    // Create a placeholder logo
    const createPlaceholderLogo = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 300;
        canvas.height = 150;
        const ctx = canvas.getContext('2d');
        
        // Background
        ctx.fillStyle = '#6B46C1';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Text
        ctx.fillStyle = 'white';
        ctx.font = 'bold 30px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('PARADIZ', canvas.width / 2, canvas.height / 2 - 15);
        ctx.font = '20px Arial';
        ctx.fillText('HOOKAH LOUNGE', canvas.width / 2, canvas.height / 2 + 15);
        
        return canvas.toDataURL();
    };
    
    // Set the logo placeholder
    const logoImg = document.querySelector('.logo');
    logoImg.src = createPlaceholderLogo();
    
    // Scan QR Code button
    const scanButton = document.querySelector('.scan-card .primary-btn');
    scanButton.addEventListener('click', function() {
        alert('In the real app, this would open the camera to scan a QR code.');
    });
    
    // Reserve Table button
    const reserveButton = document.querySelector('.reservation-form .primary-btn');
    reserveButton.addEventListener('click', function() {
        alert('Your reservation has been submitted!');
    });
    
    // Sign Out button
    const signOutButton = document.querySelector('.profile-screen .outline-btn.danger');
    signOutButton.addEventListener('click', function() {
        if (confirm('Are you sure you want to sign out?')) {
            alert('You have been signed out.');
        }
    });
});
