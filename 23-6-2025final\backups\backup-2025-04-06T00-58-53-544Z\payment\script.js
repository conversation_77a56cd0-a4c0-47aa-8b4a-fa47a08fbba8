// Global variables
let orderData = null;
let customTotal = null;

// Get order data from URL parameters
const urlParams = new URLSearchParams(window.location.search);
const orderDataParam = urlParams.get('orderData');

// DOM Elements
const tableNumberElement = document.getElementById('table-number');
const customerNameElement = document.getElementById('customer-name');
const itemsListElement = document.getElementById('items-list');
const subtotalElement = document.getElementById('subtotal');
const gstElement = document.getElementById('gst');
const pstElement = document.getElementById('pst');
const totalElement = document.getElementById('total');
const whatsappModal = document.getElementById('whatsapp-modal');
const whatsappNumberInput = document.getElementById('whatsapp-number');
const closeModal = document.querySelector('.close');
const keypadButtons = document.querySelectorAll('.key');
const sendWhatsappButton = document.getElementById('send-whatsapp');
const backupButton = document.getElementById('backup-button');

// Payment buttons
const payCashButton = document.getElementById('pay-cash');
const payCardButton = document.getElementById('pay-card');
const printReceiptButton = document.getElementById('print-receipt');
const whatsappReceiptButton = document.getElementById('whatsapp-receipt');

// Get the custom amount modal elements
const customAmountModal = document.getElementById('custom-amount-modal');
const customAmountInput = document.getElementById('custom-amount');
const applyCustomAmountBtn = document.getElementById('apply-custom-amount');
const editBillBtn = document.getElementById('edit-bill');

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Get order data from URL
    if (orderDataParam) {
        try {
            orderData = JSON.parse(decodeURIComponent(orderDataParam));
            
            // Filter out any null or invalid items
            orderData.items = orderData.items.filter(item => 
                item && 
                item.name && 
                item.price !== undefined && 
                item.price !== null && 
                item.price > 0
            );
            
            // Display order information
            document.getElementById('table-number').textContent = `Table: ${orderData.tableNumber}`;
            document.getElementById('customer-name').textContent = `Customer: ${orderData.customerName}`;

            // Display items
            const itemsList = document.getElementById('items-list');
            itemsList.innerHTML = '';
            orderData.items.forEach(item => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <span>${item.name} x ${item.quantity || 1}</span>
                    <span>$${formatPrice(item.price * (item.quantity || 1))}</span>
                `;
                itemsList.appendChild(li);
            });

            // Calculate and display totals
            const subtotal = orderData.items.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);
            const gst = subtotal * 0.05;
            const pst = subtotal * 0.07;
            const total = subtotal + gst + pst;

            document.getElementById('subtotal').textContent = `$${formatPrice(subtotal)}`;
            document.getElementById('gst').textContent = `$${formatPrice(gst)}`;
            document.getElementById('pst').textContent = `$${formatPrice(pst)}`;
            document.getElementById('total').textContent = `$${formatPrice(total)}`;

        } catch (error) {
            console.error('Error parsing order data:', error);
            showError('Invalid order data');
        }
    } else {
        showError('No order data found');
    }

    // Add backup button event listener
    if (backupButton) {
        backupButton.addEventListener('click', handleBackup);
    }
});

// Format price to 2 decimal places
function formatPrice(price) {
    return Number(price).toFixed(2);
}

// Show error message
function showError(message) {
    alert(message);
}

// Handle WhatsApp modal
function openWhatsappModal() {
    whatsappModal.style.display = 'block';
    whatsappNumberInput.value = '';
}

function closeWhatsappModal() {
    whatsappModal.style.display = 'none';
}

// Handle keypad input
keypadButtons.forEach(button => {
    button.addEventListener('click', () => {
        const value = button.textContent;
        if (value === '*' || value === '#') return;
        whatsappNumberInput.value += value;
    });
});

// Handle WhatsApp send
async function sendWhatsappReceipt() {
    const phoneNumber = whatsappNumberInput.value;
    if (!phoneNumber) {
        showError('Please enter a phone number');
        return;
    }

    try {
        // Format the receipt message
        const receiptMessage = formatReceiptMessage();
        
        // Send via WhatsApp API
        const response = await fetch('http://localhost:5000/send-whatsapp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phoneNumber,
                message: receiptMessage
            })
        });

        if (response.ok) {
            alert('Receipt sent successfully!');
            closeWhatsappModal();
        } else {
            throw new Error('Failed to send receipt');
        }
    } catch (error) {
        console.error('Error sending WhatsApp:', error);
        showError('Failed to send receipt. Please try again.');
    }
}

// Format receipt message
function formatReceiptMessage() {
    let message = `Receipt for Table ${orderData.tableNumber}\n\n`;
    orderData.items.forEach(item => {
        message += `${item.name} x ${item.quantity} - $${formatPrice(item.price * item.quantity)}\n`;
    });
    message += `\nSubtotal: $${subtotalElement.textContent.replace('$', '')}\n`;
    message += `GST (5%): $${gstElement.textContent.replace('$', '')}\n`;
    message += `PST (7%): $${pstElement.textContent.replace('$', '')}\n`;
    message += `Total: $${totalElement.textContent.replace('$', '')}\n`;
    return message;
}

// Handle payment methods
async function handlePayment(method) {
    try {
        if (!orderData) {
            showError('No order data available');
            return;
        }

        // Process payment through the server
        const response = await fetch('http://localhost:5000/process-payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                orderData,
                paymentMethod: method
            })
        });

        const result = await response.json();

        if (response.ok) {
            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'success-message';
            
            if (result.message === 'Order already paid') {
                successMessage.innerHTML = `
                    <h3>Order Already Paid</h3>
                    <p>This order was already paid via ${result.paymentMethod}</p>
                    <div class="success-options">
                        <button onclick="window.location.href='../pos/index.html'" class="action-btn">Return to POS</button>
                        <button onclick="this.parentElement.parentElement.remove()" class="action-btn">Close</button>
                    </div>
                `;
            } else {
                successMessage.innerHTML = `
                    <h3>Payment Successful!</h3>
                    <p>Payment processed via ${method}</p>
                    <div class="success-options">
                        <button onclick="window.location.href='../pos/index.html'" class="action-btn">Return to POS</button>
                        <button onclick="this.parentElement.parentElement.remove()" class="action-btn">Close</button>
                    </div>
                `;
            }
            
            document.body.appendChild(successMessage);
        } else {
            throw new Error(result.error || 'Payment failed');
        }
    } catch (error) {
        console.error('Payment error:', error);
        showError('Payment failed: ' + error.message);
    }
}

// Show receipt processing modal
function showReceiptProcessingModal() {
    const receiptModal = document.getElementById('receipt-processing-modal');
    if (receiptModal) {
        receiptModal.style.display = 'block';
    }
}

// Close receipt processing modal
function closeReceiptProcessingModal() {
    const receiptModal = document.getElementById('receipt-processing-modal');
    if (receiptModal) {
        receiptModal.style.display = 'none';
    }
}

// Return to POS page
function returnToPOS() {
    window.location.href = '../pos/index.html';
}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Get order data from URL
    if (orderDataParam) {
        orderData = JSON.parse(decodeURIComponent(orderDataParam));
        displayOrderDetails();
    } else {
        showError('No order data found');
    }

    // Add event listeners for receipt processing modal
    const closeReceiptModal = document.querySelector('.close-receipt-modal');
    if (closeReceiptModal) {
        closeReceiptModal.addEventListener('click', closeReceiptProcessingModal);
    }

    const returnToPOSBtn = document.getElementById('return-to-pos');
    if (returnToPOSBtn) {
        returnToPOSBtn.addEventListener('click', returnToPOS);
    }
});

closeModal.addEventListener('click', closeWhatsappModal);
window.addEventListener('click', (event) => {
    if (event.target === whatsappModal) {
        closeWhatsappModal();
    }
});

whatsappReceiptButton.addEventListener('click', openWhatsappModal);
sendWhatsappButton.addEventListener('click', sendWhatsappReceipt);

payCashButton.addEventListener('click', () => handlePayment('cash'));
payCardButton.addEventListener('click', () => handlePayment('card'));

printReceiptButton.addEventListener('click', () => {
    window.print();
});

// Add event listener for edit button
editBillBtn.addEventListener('click', () => {
    customAmountModal.style.display = 'block';
    customAmountInput.value = totalElement.textContent.replace('$', '');
});

// Close custom amount modal
customAmountModal.querySelector('.close').addEventListener('click', () => {
    customAmountModal.style.display = 'none';
});

// Handle custom amount keypad
const customAmountKeypad = customAmountModal.querySelector('.keypad');
customAmountKeypad.addEventListener('click', (e) => {
    if (e.target.classList.contains('key')) {
        const key = e.target.textContent;
        if (key === 'C') {
            customAmountInput.value = '';
        } else if (key === '.') {
            if (!customAmountInput.value.includes('.')) {
                customAmountInput.value += key;
            }
        } else {
            customAmountInput.value += key;
        }
    }
});

// Apply custom amount
applyCustomAmountBtn.addEventListener('click', () => {
    const customAmount = parseFloat(customAmountInput.value);
    if (!isNaN(customAmount) && customAmount >= 0) {
        customTotal = customAmount;
        totalElement.textContent = `$${customAmount.toFixed(2)}`;
        customAmountModal.style.display = 'none';
        showMessage('Custom amount applied successfully');
    } else {
        showMessage('Please enter a valid amount');
    }
});

// Update the updateTotals function to handle custom amount
function updateTotals() {
    const subtotal = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const gst = subtotal * 0.05;
    const pst = subtotal * 0.07;
    const calculatedTotal = subtotal + gst + pst;
    
    subtotalElement.textContent = `$${subtotal.toFixed(2)}`;
    gstElement.textContent = `$${gst.toFixed(2)}`;
    pstElement.textContent = `$${pst.toFixed(2)}`;
    
    // Only update total if it hasn't been manually set
    if (!customTotal) {
        totalElement.textContent = `$${calculatedTotal.toFixed(2)}`;
    }
}

// Function to display orders
function displayOrders() {
    const ordersList = document.getElementById('ordersList');
    ordersList.innerHTML = '';

    // Group orders by table number and customer name
    const groupedOrders = {};
    orders.forEach(order => {
        const key = `${order.tableNumber}-${order.customerName}`;
        if (!groupedOrders[key]) {
            groupedOrders[key] = {
                tableNumber: order.tableNumber,
                customerName: order.customerName,
                items: [],
                paid: order.paid
            };
        }

        // Add items to the grouped order, combining duplicates
        order.items.forEach(item => {
            const existingItem = groupedOrders[key].items.find(i => 
                i.name === item.name && 
                Math.abs(i.price - item.price) < 0.01
            );
            
            if (existingItem) {
                existingItem.quantity = (existingItem.quantity || 1) + (item.quantity || 1);
            } else {
                groupedOrders[key].items.push({
                    ...item,
                    quantity: item.quantity || 1
                });
            }
        });
    });

    // Display grouped orders
    Object.values(groupedOrders).forEach(order => {
        const orderDiv = document.createElement('div');
        orderDiv.className = 'order';
        orderDiv.innerHTML = `
            <h3>Table ${order.tableNumber} - ${order.customerName}</h3>
            <ul>
                ${order.items.map(item => `
                    <li>
                        <span class="item-name">${item.name}</span>
                        <span class="item-quantity">x${item.quantity || 1}</span>
                        <span class="item-price">$${formatPrice(item.price * (item.quantity || 1))}</span>
                    </li>
                `).join('')}
            </ul>
            <div class="order-total">
                <span>Total:</span>
                <span>$${formatPrice(order.items.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0))}</span>
            </div>
        `;
        ordersList.appendChild(orderDiv);
    });

    // Update the total amount
    const totalAmount = Object.values(groupedOrders).reduce((total, order) => {
        return total + order.items.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);
    }, 0);
    document.getElementById('totalAmount').textContent = `$${formatPrice(totalAmount)}`;
}

// Handle backup
async function handleBackup() {
    try {
        const response = await fetch('http://localhost:5000/api/backup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        
        if (response.ok) {
            alert('Backup created successfully!');
            console.log('Backup created at:', result.backupPath);
        } else {
            throw new Error(result.error || 'Failed to create backup');
        }
    } catch (error) {
        console.error('Backup error:', error);
        alert('Failed to create backup: ' + error.message);
    }
}

// Add event listener for back to POS button
const backToPosButton = document.getElementById('back-to-pos');
if (backToPosButton) {
    backToPosButton.addEventListener('click', () => {
        window.location.href = '../pos/index.html';
    });
} 