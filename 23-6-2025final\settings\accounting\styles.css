/* Accounting Page Styles */

/* Override global button hover styles to prevent disappearing buttons */
.container button:hover {
    opacity: 1;
    color: inherit;
}

.header-with-back {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.back-button {
    background-color: transparent;
    border: none;
    font-size: 24px;
    cursor: pointer;
    margin-right: 15px;
    color: var(--text-color);
}

.back-button:hover {
    opacity: 0.8; /* Slightly dim on hover */
    transform: translateX(-2px); /* Slight movement to indicate interactivity */
    background-color: transparent; /* Maintain transparent background */
    color: var(--text-color); /* Maintain text color */
}

.settings-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: var(--card-shadow);
}

.settings-section h2 {
    margin-top: 0;
    color: var(--heading-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.settings-description {
    margin-bottom: 20px;
    color: var(--text-muted);
}

/* Accounting Summary Cards */
.accounting-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.summary-card {
    background-color: var(--container-bg);
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    text-align: center;
}

.summary-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--text-color);
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    padding: 15px;
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.action-btn:hover {
    background-color: var(--button-primary-hover);
    transform: translateY(-2px);
    opacity: 1; /* Override global opacity */
    color: var(--button-primary-color); /* Maintain text color */
}

.action-btn i {
    margin-right: 10px;
    font-size: 18px;
}

/* Transactions Table */
.transactions-container {
    overflow-x: auto;
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.transactions-table th {
    background-color: var(--table-header-bg);
    color: var(--table-header-color);
    padding: 12px;
    text-align: left;
    font-weight: bold;
}

.transactions-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
}

.transactions-table tr:last-child td {
    border-bottom: none;
}

.transactions-table .loading {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 20px;
}

/* View All Button */
.view-all {
    text-align: center;
    margin-top: 15px;
}

.secondary-btn {
    padding: 10px 20px;
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.secondary-btn:hover {
    background-color: var(--button-secondary-hover);
    opacity: 1; /* Override global opacity */
    color: var(--button-secondary-color); /* Maintain text color */
}

.secondary-btn i {
    margin-right: 8px;
}

/* Transaction History Modal Styles */
.large-modal {
    width: 95%;
    max-width: 1000px;
}

.transaction-modal .modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.date-range {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.type-filter {
    display: flex;
    align-items: center;
    gap: 15px;
}

.type-filter label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.filter-btn {
    padding: 8px 15px;
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: auto;
}

.filter-btn:hover {
    background-color: var(--button-primary-hover);
    opacity: 1;
}

.transaction-table-container {
    margin-bottom: 20px;
    overflow-x: auto;
}

.full-width {
    width: 100%;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.pagination-btn {
    padding: 8px 15px;
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--button-secondary-hover);
    opacity: 1;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#page-info {
    font-weight: bold;
}

.export-section {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.export-btn {
    padding: 10px 20px;
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
}

.export-btn:hover {
    background-color: var(--button-primary-hover);
    opacity: 1;
}

.error {
    color: #e74c3c;
    text-align: center;
    padding: 15px;
    font-style: italic;
}

.empty {
    color: var(--text-muted);
    text-align: center;
    padding: 15px;
    font-style: italic;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--container-bg);
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 600px;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    color: var(--heading-color);
}

.close-modal {
    color: var(--text-muted);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 16px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
}

.cancel-btn {
    padding: 10px 20px;
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.save-btn {
    padding: 10px 20px;
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.cancel-btn:hover {
    background-color: var(--button-secondary-hover);
    opacity: 1; /* Override global opacity */
    color: var(--button-secondary-color); /* Maintain text color */
    transform: translateY(-2px);
}

.save-btn:hover {
    background-color: var(--button-primary-hover);
    opacity: 1; /* Override global opacity */
    color: var(--button-primary-color); /* Maintain text color */
    transform: translateY(-2px);
}

/* Transaction Type Colors */
.income {
    color: #27ae60; /* Green */
}

.expense {
    color: #e74c3c; /* Red */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .accounting-summary,
    .action-buttons {
        grid-template-columns: 1fr;
    }

    .action-btn {
        padding: 12px;
    }

    .modal-content {
        margin: 20% auto;
        width: 95%;
    }

    .form-actions {
        flex-direction: column;
    }

    .cancel-btn,
    .save-btn {
        width: 100%;
    }
}
