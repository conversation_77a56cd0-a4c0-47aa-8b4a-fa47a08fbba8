@echo off
setlocal

:: Get current date and time for folder name
set year=%date:~10,4%
set month=%date:~4,2%
set day=%date:~7,2%
set hour=%time:~0,2%
if "%hour:~0,1%" == " " set hour=0%hour:~1,1%
set min=%time:~3,2%
set sec=%time:~6,2%

:: Create backup directory
set backup_dir=backup_%year%-%month%-%day%_%hour%-%min%-%sec%
mkdir "%backup_dir%"

:: Create necessary subdirectories
mkdir "%backup_dir%\config"
mkdir "%backup_dir%\kitchen-displays"
mkdir "%backup_dir%\kitchen-displays\css"
mkdir "%backup_dir%\kitchen-displays\js"
mkdir "%backup_dir%\pos"
mkdir "%backup_dir%\split"
mkdir "%backup_dir%\static"
mkdir "%backup_dir%\static\css"
mkdir "%backup_dir%\static\js"
mkdir "%backup_dir%\tablet"
mkdir "%backup_dir%\templates"
mkdir "%backup_dir%\payment"

:: Copy all important files (excluding node_modules and temporary files)
echo Creating backup in %backup_dir%...

:: Copy root files
copy "app.py" "%backup_dir%\"
copy "database.js" "%backup_dir%\"
copy "generate_icon.html" "%backup_dir%\"
copy "index.html" "%backup_dir%\"
copy "items.json" "%backup_dir%\"
copy "package.json" "%backup_dir%\"
copy "script.js" "%backup_dir%\"
copy "server.js" "%backup_dir%\"
copy "start_pos.bat" "%backup_dir%\"
copy "styles.css" "%backup_dir%\"

:: Copy config files
copy "config\*.*" "%backup_dir%\config\"

:: Copy kitchen-displays files
copy "kitchen-displays\*.html" "%backup_dir%\kitchen-displays\"
copy "kitchen-displays\css\*.*" "%backup_dir%\kitchen-displays\css\"
copy "kitchen-displays\js\*.*" "%backup_dir%\kitchen-displays\js\"

:: Copy POS files
copy "pos\*.*" "%backup_dir%\pos\"

:: Copy split files
copy "split\*.*" "%backup_dir%\split\"

:: Copy static files
copy "static\css\*.*" "%backup_dir%\static\css\"
copy "static\js\*.*" "%backup_dir%\static\js\"
copy "static\*.*" "%backup_dir%\static\"

:: Copy tablet files
copy "tablet\*.*" "%backup_dir%\tablet\"

:: Copy templates
copy "templates\*.*" "%backup_dir%\templates\"

:: Copy payment files
copy "payment\*.*" "%backup_dir%\payment\"

:: Copy database (if it exists)
if exist "orders.db" copy "orders.db" "%backup_dir%\"

echo Backup completed in %backup_dir%
pause
