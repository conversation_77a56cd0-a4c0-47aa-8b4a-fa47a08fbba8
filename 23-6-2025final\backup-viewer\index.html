<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup Viewer - Paradiz <PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-left">
                <button id="backBtn" class="btn btn-secondary">
                    <span class="back-icon">←</span> Back to Settings
                </button>
            </div>
            <h1>Backup Viewer</h1>
            <div class="header-actions">
                <button id="refreshBtn" class="btn btn-secondary">Refresh</button>
                <button id="createBackupBtn" class="btn btn-primary">Backup</button>
            </div>
        </header>

        <main>
            <div class="backup-list">
                <table>
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Time</th>
                            <th>File Count</th>
                            <th>Size</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="backupTableBody">
                        <!-- Backup entries will be inserted here -->
                    </tbody>
                </table>
        </div>
        </main>
    </div>

    <!-- Modal for backup details -->
    <div id="backupModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                    <h2>Backup Details</h2>
                <button id="closeModal" class="btn-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="details-section">
                    <h3>Backup Information</h3>
                    <div id="backupInfo"></div>
                </div>
                <div class="details-section">
                    <h3>Files Included</h3>
                    <div id="backupFiles" class="files-list"></div>
                </div>
                <div class="details-section">
                    <h3>Orders</h3>
                    <div class="table-container">
                        <table class="orders-table">
                            <thead>
                                <tr>
                                    <th>Table</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                <!-- Orders will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                                    </div>
                                            </div>
                                    </div>
                                </div>

    <script src="script.js"></script>
</body>
</html>