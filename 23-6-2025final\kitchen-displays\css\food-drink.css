body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.header {
    background-color: #2e7d32;
    color: white;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.orders-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.order-card {
    background: white;
    border-left: 4px solid #2e7d32;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.order-info {
    display: flex;
    align-items: center;
    width: 250px;
}

.table-number {
    font-weight: bold;
    width: 100px;
}

.customer-name {
    color: #666;
}

.order-items {
    flex: 1;
    display: flex;
    gap: 5px;
    overflow-x: auto;
    padding: 5px 0;
}

.order-item {
    background-color: #f8f8f8;
    border-radius: 4px;
    padding: 5px 8px;
    white-space: nowrap;
}

.order-footer {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 150px;
}

.order-timer {
    font-weight: bold;
    color: #666;
    width: 60px;
}

.complete-btn {
    background-color: #2e7d32;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

.complete-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.no-orders {
    color: #666;
    padding: 20px;
    text-align: center;
}