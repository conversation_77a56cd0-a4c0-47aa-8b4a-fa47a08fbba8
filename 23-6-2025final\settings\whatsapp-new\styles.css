/* WhatsApp Settings Styles */

/* Header styles */
.header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.header h1 {
    margin: 0;
    font-size: 24px;
}

.header h1 i {
    color: #25D366; /* WhatsApp green */
    margin-right: 10px;
}

.back-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    margin-right: 15px;
    color: var(--text-color);
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-button:hover {
    color: var(--primary-color);
}

/* Content styles */
.content {
    max-width: 800px;
    margin: 0 auto;
}

/* Settings card styles */
.settings-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: var(--card-shadow);
}

.settings-card h2 {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    color: var(--heading-color);
    font-size: 18px;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input[type="text"],
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 14px;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: var(--text-muted);
    font-size: 12px;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 10px;
    cursor: pointer;
}

/* Preview styles */
.preview-container {
    background-color: #f0f0f0;
    border-radius: 8px;
    padding: 15px;
}

.whatsapp-preview {
    background-color: #E5DDD5; /* WhatsApp chat background */
    border-radius: 8px;
    padding: 15px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #000;
    max-width: 400px;
    margin: 0 auto;
    position: relative;
}

.whatsapp-preview .loading {
    text-align: center;
    color: #888;
    padding: 20px;
}

.whatsapp-message {
    background-color: #DCF8C6; /* WhatsApp message bubble */
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    position: relative;
    box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
}

.whatsapp-message::after {
    content: "";
    position: absolute;
    top: 0;
    right: -8px;
    width: 0;
    height: 0;
    border-top: 8px solid #DCF8C6;
    border-right: 8px solid transparent;
}

.business-logo {
    text-align: center;
    margin-bottom: 10px;
}

.business-logo i {
    font-size: 40px;
    color: #25D366;
}

.business-name {
    font-weight: bold;
    font-size: 18px;
    text-align: center;
    margin-bottom: 5px;
}

.business-tagline {
    text-align: center;
    font-style: italic;
    margin-bottom: 10px;
    color: #555;
}

.receipt-header {
    border-top: 1px dashed #999;
    border-bottom: 1px dashed #999;
    padding: 10px 0;
    margin: 10px 0;
}

.receipt-items {
    margin: 10px 0;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.receipt-total {
    font-weight: bold;
    text-align: right;
    margin: 10px 0;
}

.receipt-footer {
    text-align: center;
    font-size: 12px;
    margin-top: 15px;
    color: #555;
}

.qr-code {
    text-align: center;
    margin-top: 10px;
}

.qr-code i {
    font-size: 80px;
    color: #000;
}

/* Button styles */
.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 30px;
}

.primary-btn, .whatsapp-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.primary-btn {
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
}

.primary-btn:hover {
    background-color: var(--button-primary-hover);
}

.whatsapp-btn {
    background-color: #25D366; /* WhatsApp green */
    color: white;
}

.whatsapp-btn:hover {
    background-color: #128C7E; /* Darker WhatsApp green */
}

.whatsapp-btn i, .primary-btn i {
    font-size: 18px;
}

/* Status message styles */
.status-message {
    padding: 15px;
    border-radius: 4px;
    text-align: center;
    margin-bottom: 30px;
    display: none;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

/* Dark theme adjustments */
.dark-theme .whatsapp-preview {
    background-color: #0D1418;
}

.dark-theme .whatsapp-message {
    background-color: #056162;
    color: #fff;
}

.dark-theme .whatsapp-message::after {
    border-top-color: #056162;
}

.dark-theme .business-tagline,
.dark-theme .receipt-footer {
    color: #aaa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .content {
        padding: 0 15px;
    }
    
    .settings-card {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 20px;
    }
}
