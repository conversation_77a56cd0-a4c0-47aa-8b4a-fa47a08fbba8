// Global variables
let currentItemId = null;
let currentItemIndex = -1;
let categories = [];
let allItems = [];
let filteredItems = [];

document.addEventListener('DOMContentLoaded', function() {
    // Apply saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }

    // Load categories first, then items
    loadCategories().then(() => {
        loadItems();
    });

    // Back button
    const backBtn = document.getElementById('back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            window.location.href = '../settings/index.html';
        });
    }

    // Add item button
    const addItemBtn = document.getElementById('add-item-btn');
    const addItemModal = document.getElementById('add-item-modal');
    const closeBtn = document.querySelector('.close');
    const cancelItemBtn = document.getElementById('cancel-item');

    if (addItemBtn) {
        addItemBtn.addEventListener('click', function() {
            // Reset form for new item
            resetItemForm();
            document.getElementById('modal-title').textContent = 'Add New Item';
            addItemModal.style.display = 'block';
        });
    }

    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            addItemModal.style.display = 'none';
        });
    }

    if (cancelItemBtn) {
        cancelItemBtn.addEventListener('click', function() {
            addItemModal.style.display = 'none';
        });
    }

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === addItemModal) {
            addItemModal.style.display = 'none';
        }

        const confirmDeleteModal = document.getElementById('confirm-delete-modal');
        if (event.target === confirmDeleteModal) {
            confirmDeleteModal.style.display = 'none';
            currentItemId = null;
            currentItemIndex = -1;
        }
    });

    // Item form submission (for both add and edit)
    const itemForm = document.getElementById('item-form');
    if (itemForm) {
        itemForm.addEventListener('submit', function(event) {
            event.preventDefault();

            const itemData = {
                name: document.getElementById('item-name').value,
                category: document.getElementById('item-category').value,
                price: parseFloat(document.getElementById('item-price').value),
                description: document.getElementById('item-description').value || ''
            };

            // Check if we're adding or editing
            const itemId = document.getElementById('item-id').value;

            if (itemId) {
                // Update existing item
                updateItem(itemId, itemData);
            } else {
                // Add new item
                addItem(itemData);
            }
        });
    }

    // Search functionality
    const searchBtn = document.getElementById('search-btn');
    const searchInput = document.getElementById('search-input');

    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', function() {
            filterItems();
        });

        searchInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                filterItems();
            }
        });
    }

    // Category filter
    const categorySelect = document.getElementById('category-select');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            filterItems();
        });
    }

    // Delete confirmation
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    const cancelDeleteBtn = document.getElementById('cancel-delete-btn');

    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentItemId) {
                deleteItem(currentItemId);
                document.getElementById('confirm-delete-modal').style.display = 'none';
                currentItemId = null;
                currentItemIndex = -1;
            } else if (currentItemIndex >= 0 && currentItemIndex < filteredItems.length) {
                // Fallback to using the item at the current index
                const item = filteredItems[currentItemIndex];
                if (item && (item.id || item.name)) {
                    deleteItem(item.id || item.name);
                }
                document.getElementById('confirm-delete-modal').style.display = 'none';
                currentItemId = null;
                currentItemIndex = -1;
            } else {
                alert('Error: Cannot identify the item to delete. Please try again.');
                document.getElementById('confirm-delete-modal').style.display = 'none';
            }
        });
    }

    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            document.getElementById('confirm-delete-modal').style.display = 'none';
            currentItemId = null;
            currentItemIndex = -1;
        });
    }
});

// Function to load categories from the server
async function loadCategories() {
    try {
        const response = await fetch('http://localhost:5000/api/categories');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        categories = await response.json();

        // Populate category dropdowns
        const categorySelect = document.getElementById('category-select');
        const itemCategorySelect = document.getElementById('item-category');

        // Clear existing options except the first one
        while (categorySelect.options.length > 1) {
            categorySelect.remove(1);
        }

        while (itemCategorySelect.options.length > 1) {
            itemCategorySelect.remove(1);
        }

        // Add categories to dropdowns
        categories.forEach(category => {
            // Add to filter dropdown
            const filterOption = document.createElement('option');
            filterOption.value = category.id;
            filterOption.textContent = category.name;
            categorySelect.appendChild(filterOption);

            // Add to form dropdown
            const formOption = document.createElement('option');
            formOption.value = category.id;
            formOption.textContent = category.name;
            itemCategorySelect.appendChild(formOption);
        });

    } catch (error) {
        console.error('Error loading categories:', error);
        alert('Failed to load categories. Please try again.');
    }
}

// Function to load items from the server
async function loadItems() {
    const itemsList = document.getElementById('items-list');

    if (!itemsList) return;

    // Clear current items
    itemsList.innerHTML = '<tr><td colspan="5">Loading items...</td></tr>';

    try {
        const response = await fetch('http://localhost:5000/api/items');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        allItems = await response.json();

        // Apply initial filtering
        filterItems();

    } catch (error) {
        console.error('Error loading items:', error);
        itemsList.innerHTML = '<tr><td colspan="5">Error loading items. Please try again.</td></tr>';
    }
}

// Function to filter and display items
function filterItems() {
    const searchTerm = document.getElementById('search-input').value.trim().toLowerCase();
    const categoryFilter = document.getElementById('category-select').value;
    const itemsList = document.getElementById('items-list');

    if (!itemsList) return;

    // Filter items based on search term and category
    filteredItems = allItems.filter(item => {
        const matchesSearch = !searchTerm ||
            item.name.toLowerCase().includes(searchTerm) ||
            (item.description && item.description.toLowerCase().includes(searchTerm)) ||
            item.categoryDisplayName.toLowerCase().includes(searchTerm);

        const matchesCategory = !categoryFilter || item.category === categoryFilter;

        return matchesSearch && matchesCategory;
    });

    // Display filtered items
    if (filteredItems.length === 0) {
        itemsList.innerHTML = '<tr><td colspan="5">No items found</td></tr>';
        return;
    }

    // Clear the list
    itemsList.innerHTML = '';

    // Add each item to the table
    filteredItems.forEach((item, index) => {
        const row = document.createElement('tr');

        // Use the item name as the ID if no ID is available
        const itemIdentifier = item.id || item.name;

        row.innerHTML = `
            <td>${item.name}</td>
            <td>${item.categoryDisplayName}</td>
            <td class="price-column">$${parseFloat(item.price).toFixed(2)}</td>
            <td class="truncate">${item.description || ''}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${itemIdentifier}" data-index="${index}" title="Click to edit this item's name, price, category, and description">✏️ Edit</button>
                <button class="action-btn delete-btn" data-id="${itemIdentifier}" data-index="${index}">🗑️ Delete</button>
            </td>
        `;

        itemsList.appendChild(row);
    });

    // Log the items for debugging
    console.log('Items being displayed:', filteredItems);

    // Add event listeners to action buttons
    addActionButtonListeners();
}

// Function to add a new item
async function addItem(itemData) {
    try {
        const response = await fetch('http://localhost:5000/api/items', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(itemData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to add item');
        }

        const data = await response.json();

        // Close the modal and reset form
        document.getElementById('add-item-modal').style.display = 'none';
        document.getElementById('item-form').reset();

        // Reload items
        await loadItems();

        alert('Item added successfully!');
    } catch (error) {
        console.error('Error adding item:', error);
        alert(`Failed to add item: ${error.message}`);
    }
}

// Function to update an item
async function updateItem(itemId, itemData) {
    try {
        console.log('Updating item with ID:', itemId);
        console.log('Update data:', itemData);

        // Encode the item ID in case it contains special characters
        const encodedItemId = encodeURIComponent(itemId);

        const response = await fetch(`http://localhost:5000/api/items/${encodedItemId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(itemData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to update item');
        }

        const data = await response.json();
        console.log('Update successful, response:', data);

        // Close the modal and reset form
        document.getElementById('add-item-modal').style.display = 'none';
        document.getElementById('item-form').reset();

        // Reload items
        await loadItems();

        alert('Item updated successfully!');
    } catch (error) {
        console.error('Error updating item:', error);
        alert(`Failed to update item: ${error.message}`);
    }
}

// Function to delete an item
async function deleteItem(itemId) {
    try {
        console.log('Deleting item with ID:', itemId);

        // Encode the item ID in case it contains special characters
        const encodedItemId = encodeURIComponent(itemId);

        const response = await fetch(`http://localhost:5000/api/items/${encodedItemId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete item');
        }

        const data = await response.json();
        console.log('Delete successful, response:', data);

        // Reload items
        await loadItems();

        alert('Item deleted successfully!');
    } catch (error) {
        console.error('Error deleting item:', error);
        alert(`Failed to delete item: ${error.message}`);
    }
}

// Function to add event listeners to action buttons
function addActionButtonListeners() {
    // Edit buttons
    const editButtons = document.querySelectorAll('.edit-btn');
    console.log('Edit buttons found:', editButtons.length);

    editButtons.forEach(button => {
        const itemId = button.getAttribute('data-id');
        console.log('Button with item ID:', itemId);

        button.addEventListener('click', function() {
            const itemIndex = parseInt(this.getAttribute('data-index'));
            console.log('Edit button clicked for item index:', itemIndex);

            if (isNaN(itemIndex) || itemIndex < 0 || itemIndex >= filteredItems.length) {
                console.error('Invalid item index:', itemIndex);
                alert('Error: Cannot edit this item. Please try refreshing the page.');
                return;
            }

            editItemByIndex(itemIndex);
        });
    });

    // Delete buttons
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const itemIndex = parseInt(this.getAttribute('data-index'));
            console.log('Delete button clicked for item index:', itemIndex);

            if (isNaN(itemIndex) || itemIndex < 0 || itemIndex >= filteredItems.length) {
                console.error('Invalid item index for deletion:', itemIndex);
                alert('Error: Cannot delete this item. Please try refreshing the page.');
                return;
            }

            const itemId = this.getAttribute('data-id');
            showDeleteConfirmation(itemId, itemIndex);
        });
    });
}

// Function to show the edit form for an item
async function editItem(itemId) {
    try {
        console.log('Editing item with ID:', itemId);

        // Find the item in our filtered items first
        let item = filteredItems.find(item => {
            // Check for exact match or if the ID is undefined/null
            return (item.id === itemId) ||
                   (!item.id && item.name === itemId);
        });

        console.log('Found item in filtered items:', item);

        // If not found, try to find by name instead of ID
        if (!item) {
            item = filteredItems.find(item => item.name === itemId);
            console.log('Found item by name:', item);
        }

        // If still not found, fetch from server
        if (!item) {
            console.log('Item not found in filtered items, fetching from server...');
            try {
                const response = await fetch(`http://localhost:5000/api/items/${encodeURIComponent(itemId)}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                item = await response.json();
                console.log('Item fetched from server:', item);
            } catch (fetchError) {
                console.error('Error fetching from server:', fetchError);
                // As a fallback, try to find the item by name in all items
                item = allItems.find(i => i.name === itemId);
                console.log('Fallback: found item by name in all items:', item);
            }
        }

        // Check if we have a valid item
        if (!item) {
            throw new Error('Could not find the item to edit');
        }

        // Populate the form
        document.getElementById('item-id').value = item.id || item.name; // Use name as fallback ID
        document.getElementById('item-name').value = item.name;
        document.getElementById('item-category').value = item.category || '';
        document.getElementById('item-price').value = parseFloat(item.price || 0).toFixed(2);
        document.getElementById('item-description').value = item.description || '';

        // Update modal title and add item name
        document.getElementById('modal-title').innerHTML = `Edit Item: <span class="item-name-highlight">${item.name}</span>`;

        // Show the modal with a slight delay to ensure styles are applied
        setTimeout(() => {
            document.getElementById('add-item-modal').style.display = 'block';
            // Force repaint to ensure flex styles are applied
            window.getComputedStyle(document.getElementById('add-item-modal')).opacity;
        }, 50);

        console.log('Edit form populated successfully');
    } catch (error) {
        console.error('Error loading item for edit:', error);
        alert('Failed to load item details. Please try again.');
    }
}

// Function to edit an item by its index in the filteredItems array
function editItemByIndex(index) {
    try {
        console.log('Editing item at index:', index);

        // Get the item directly from the filteredItems array
        const item = filteredItems[index];
        console.log('Item to edit:', item);

        if (!item) {
            throw new Error(`No item found at index ${index}`);
        }

        // Populate the form
        document.getElementById('item-id').value = item.id || item.name; // Use name as fallback ID
        document.getElementById('item-name').value = item.name;
        document.getElementById('item-category').value = item.category || '';
        document.getElementById('item-price').value = parseFloat(item.price || 0).toFixed(2);
        document.getElementById('item-description').value = item.description || '';

        // Update modal title and add item name
        document.getElementById('modal-title').innerHTML = `Edit Item: <span class="item-name-highlight">${item.name}</span>`;

        // Show the modal with a slight delay to ensure styles are applied
        setTimeout(() => {
            document.getElementById('add-item-modal').style.display = 'block';
            // Force repaint to ensure flex styles are applied
            window.getComputedStyle(document.getElementById('add-item-modal')).opacity;
        }, 50);

        console.log('Edit form populated successfully');
    } catch (error) {
        console.error('Error loading item for edit:', error);
        alert('Failed to load item details. Please try again.');
    }
}

// Function to show delete confirmation
function showDeleteConfirmation(itemId, itemIndex) {
    currentItemId = itemId;
    // Store the index as well for fallback
    currentItemIndex = itemIndex;
    // Show the modal with a slight delay to ensure styles are applied
    setTimeout(() => {
        document.getElementById('confirm-delete-modal').style.display = 'block';
        // Force repaint to ensure flex styles are applied
        window.getComputedStyle(document.getElementById('confirm-delete-modal')).opacity;
    }, 50);
}

// Function to reset the item form
function resetItemForm() {
    const form = document.getElementById('item-form');
    if (form) {
        form.reset();
        document.getElementById('item-id').value = '';
    }
}
