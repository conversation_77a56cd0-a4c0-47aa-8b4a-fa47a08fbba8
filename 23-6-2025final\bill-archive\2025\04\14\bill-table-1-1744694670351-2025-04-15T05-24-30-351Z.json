{"id": "table-1-1744694670351", "tableNumber": "1", "customerName": "Table 1 (Combined)", "timestamp": "2025-04-15T05:24:30.351Z", "items": [{"name": "<PERSON><PERSON>", "price": 1, "quantity": 1, "notes": ""}, {"name": "1/2 <PERSON>ies", "price": 3.995, "quantity": 2, "notes": ""}, {"name": "Watermelon + Coconut", "price": 25, "quantity": 1, "notes": ""}, {"name": "<PERSON><PERSON>", "price": 7.99, "quantity": 1, "notes": ""}, {"name": "Double apple", "price": 25, "quantity": 1, "notes": ""}, {"name": "Barbican", "price": 4.99, "quantity": 1, "notes": ""}], "payment": {"paymentMethod": "card", "tip": 0, "subtotal": 71.97, "tax": 5.35, "taxRate": 0.07433652910935112, "gst": 3.6, "gstRate": 0.0500208420175073, "pst": 1.75, "pstRate": 0.024315687091843823, "total": 77.32, "transactionId": null}, "archiveTimestamp": "2025-04-15T05:24:30.354Z"}