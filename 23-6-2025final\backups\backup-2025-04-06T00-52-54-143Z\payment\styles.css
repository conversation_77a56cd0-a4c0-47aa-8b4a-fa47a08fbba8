* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.payment-container {
    display: flex;
    gap: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.bill-details {
    flex: 1;
    padding: 20px;
    border-right: 1px solid #eee;
}

.payment-options {
    flex: 1;
    padding: 20px;
}

h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
}

.customer-info {
    margin-bottom: 20px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 5px;
}

.items-list {
    margin-bottom: 20px;
}

.items-list h3 {
    margin-bottom: 10px;
    color: #555;
}

#items-list {
    list-style: none;
}

#items-list li {
    padding: 10px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
}

.bill-summary {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 5px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px 0;
}

.summary-row.total {
    font-weight: bold;
    font-size: 18px;
    border-top: 2px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.payment-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.payment-btn {
    padding: 15px 25px;
    margin: 5px;
    border: none;
    border-radius: 5px;
    background-color: #4CAF50;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    flex: 1;
    min-width: 120px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-btn-row {
    display: flex;
    gap: 10px;
    width: 100%;
}

.payment-btn-row .payment-btn {
    width: 50%;
}

#pay-cash {
    background-color: #4CAF50;
    color: white;
}

#pay-card {
    background-color: #2196F3;
    color: white;
}

#print-receipt {
    background-color: #FF9800;
    color: white;
}

#whatsapp-receipt {
    background-color: #25D366;
    color: white;
}

#edit-bill {
    background-color: #9C27B0;
    color: white;
}

#mis-report {
    background-color: #607D8B;
    color: white;
}

#backup-button {
    background-color: #607D8B;
    color: white;
    width: 100%;
    margin-top: 10px;
}

#backup-button:hover {
    background-color: #455A64;
}

#back-to-pos {
    background-color: #FF9800;
    height: 50px;
}

.payment-btn:hover {
    background-color: #45a049;
}

#back-to-pos:hover {
    background-color: #F57C00;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 500px;
    border-radius: 8px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}

/* WhatsApp Modal */
.whatsapp-input {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.keypad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 15px 0;
}

.key {
    padding: 15px;
    font-size: 18px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 4px;
}

.key:hover {
    background: #f0f0f0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-container {
        flex-direction: column;
    }

    .bill-details {
        border-right: none;
        border-bottom: 1px solid #eee;
    }
}

.backup-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.backup-message.success {
    background-color: #4CAF50;
}

.backup-message.error {
    background-color: #f44336;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Success Message */
.success-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fefefe;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.success-message h3 {
    color: #4CAF50;
    margin-bottom: 10px;
}

.success-message p {
    margin-bottom: 20px;
    color: #333;
}

.success-options {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.success-options .action-btn {
    padding: 10px 20px;
    font-size: 16px;
} 