/**
 * Data Export Service
 * Handles exporting business data to various formats and destinations
 */

const fs = require('fs');
const path = require('path');

// Try to load optional dependencies
let ExcelJ<PERSON>, nodemailer, google;
try {
    ExcelJS = require('exceljs');
} catch (e) {
    console.log('ExcelJS not available. Excel export functionality will be limited.');
}

try {
    nodemailer = require('nodemailer');
} catch (e) {
    console.log('Nodemailer not available. Email functionality will be limited.');
}

try {
    google = require('googleapis').google;
} catch (e) {
    console.log('Google APIs not available. Google Sheets functionality will be limited.');
}

const billArchiveService = require('./bill-archive-service');

class DataExportService {
    constructor(options = {}) {
        this.options = {
            localBackupDir: path.join(__dirname, 'data-exports'),
            emailConfig: options.emailConfig || null,
            googleConfig: options.googleConfig || null,
            ...options
        };

        // Create export directory if it doesn't exist
        if (!fs.existsSync(this.options.localBackupDir)) {
            fs.mkdirSync(this.options.localBackupDir, { recursive: true });
        }
    }

    /**
     * Generate a comprehensive sales report
     * @param {string} startDate - Start date in YYYY-MM-DD format
     * @param {string} endDate - End date in YYYY-MM-DD format
     * @returns {Object} Report data
     */
    async generateSalesReport(startDate, endDate) {
        console.log(`Generating sales report from ${startDate} to ${endDate}`);

        // Get all bills in date range
        const bills = this.getBillsInDateRange(startDate, endDate);

        // Initialize report data
        const reportData = {
            summary: {
                totalSales: 0,
                totalTax: 0,
                totalCoverCharge: 0,
                totalTips: 0,
                billCount: bills.length,
                paymentMethods: {
                    cash: 0,
                    card: 0,
                    other: 0
                }
            },
            itemSales: {},
            dailySales: {},
            bills: bills
        };

        // Process each bill
        bills.forEach(bill => {
            // Extract date (YYYY-MM-DD) from timestamp
            const billDate = bill.timestamp.split('T')[0];

            // Initialize daily sales if not exists
            if (!reportData.dailySales[billDate]) {
                reportData.dailySales[billDate] = {
                    sales: 0,
                    tax: 0,
                    tips: 0,
                    billCount: 0
                };
            }

            // Calculate bill totals
            const subtotal = bill.payment?.subtotal || this.calculateSubtotal(bill.items);
            const tax = bill.payment?.tax || 0;
            const tip = bill.payment?.tip || 0;

            // Update summary
            reportData.summary.totalSales += subtotal;
            reportData.summary.totalTax += tax;
            reportData.summary.totalTips += tip;

            // Update payment method counts
            const paymentMethod = (bill.payment?.paymentMethod || 'other').toLowerCase();
            if (paymentMethod === 'cash' || paymentMethod === 'card') {
                reportData.summary.paymentMethods[paymentMethod]++;
            } else {
                reportData.summary.paymentMethods.other++;
            }

            // Update daily sales
            reportData.dailySales[billDate].sales += subtotal;
            reportData.dailySales[billDate].tax += tax;
            reportData.dailySales[billDate].tips += tip;
            reportData.dailySales[billDate].billCount++;

            // Process items
            bill.items.forEach(item => {
                const itemName = item.name;
                const quantity = parseInt(item.quantity) || 1;
                const price = parseFloat(item.price) || 0;
                const itemTotal = price * quantity;

                // Initialize item if not exists
                if (!reportData.itemSales[itemName]) {
                    reportData.itemSales[itemName] = {
                        quantity: 0,
                        revenue: 0
                    };
                }

                // Update item sales
                reportData.itemSales[itemName].quantity += quantity;
                reportData.itemSales[itemName].revenue += itemTotal;
            });

            // Check for cover charge (assuming it's an item named "Cover Charge")
            const coverChargeItem = bill.items.find(item =>
                item.name.toLowerCase().includes('cover') ||
                item.name.toLowerCase().includes('entrance')
            );

            if (coverChargeItem) {
                const quantity = parseInt(coverChargeItem.quantity) || 1;
                const price = parseFloat(coverChargeItem.price) || 0;
                reportData.summary.totalCoverCharge += price * quantity;
            }
        });

        return reportData;
    }

    /**
     * Get all bills in a date range
     * @param {string} startDate - Start date in YYYY-MM-DD format
     * @param {string} endDate - End date in YYYY-MM-DD format
     * @returns {Array} Bills in date range
     */
    getBillsInDateRange(startDate, endDate) {
        const bills = [];

        // Convert dates to Date objects for comparison
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999); // Set to end of day

        // Get all bills in date range
        const dateRange = this.getDateRange(start, end);

        dateRange.forEach(date => {
            const dailyBills = billArchiveService.getBillsByDate(date);
            if (dailyBills && dailyBills.length > 0) {
                bills.push(...dailyBills);
            }
        });

        return bills;
    }

    /**
     * Get array of dates in YYYY-MM-DD format between start and end dates
     * @param {Date} startDate - Start date
     * @param {Date} endDate - End date
     * @returns {Array} Array of dates in YYYY-MM-DD format
     */
    getDateRange(startDate, endDate) {
        const dates = [];
        const currentDate = new Date(startDate);

        while (currentDate <= endDate) {
            dates.push(currentDate.toISOString().split('T')[0]);
            currentDate.setDate(currentDate.getDate() + 1);
        }

        return dates;
    }

    /**
     * Calculate subtotal from items
     * @param {Array} items - Order items
     * @returns {number} Subtotal
     */
    calculateSubtotal(items) {
        if (!items || !Array.isArray(items)) return 0;

        return items.reduce((sum, item) => {
            const price = parseFloat(item.price) || 0;
            const quantity = parseInt(item.quantity) || 1;
            return sum + (price * quantity);
        }, 0);
    }

    /**
     * Export sales report to Excel
     * @param {Object} reportData - Report data
     * @param {string} filename - Output filename
     * @returns {string} Path to exported file
     */
    async exportToExcel(reportData, filename) {
        if (!ExcelJS) {
            console.error('ExcelJS is not available. Please install it with: npm install exceljs');
            throw new Error('ExcelJS is not available');
        }

        const workbook = new ExcelJS.Workbook();

        // Add Summary worksheet
        const summarySheet = workbook.addWorksheet('Summary');
        summarySheet.columns = [
            { header: 'Metric', key: 'metric', width: 20 },
            { header: 'Value', key: 'value', width: 15 }
        ];

        // Add summary data
        summarySheet.addRow({ metric: 'Total Sales', value: reportData.summary.totalSales.toFixed(2) });
        summarySheet.addRow({ metric: 'Total Tax', value: reportData.summary.totalTax.toFixed(2) });
        summarySheet.addRow({ metric: 'Total Tips', value: reportData.summary.totalTips.toFixed(2) });
        summarySheet.addRow({ metric: 'Total Cover Charge', value: reportData.summary.totalCoverCharge.toFixed(2) });
        summarySheet.addRow({ metric: 'Total Revenue', value: (reportData.summary.totalSales + reportData.summary.totalTax + reportData.summary.totalTips).toFixed(2) });
        summarySheet.addRow({ metric: 'Total Bills', value: reportData.summary.billCount });
        summarySheet.addRow({ metric: 'Cash Payments', value: reportData.summary.paymentMethods.cash });
        summarySheet.addRow({ metric: 'Card Payments', value: reportData.summary.paymentMethods.card });
        summarySheet.addRow({ metric: 'Other Payments', value: reportData.summary.paymentMethods.other });

        // Format summary sheet
        summarySheet.getColumn('value').numFmt = '$#,##0.00';

        // Add Daily Sales worksheet
        const dailySheet = workbook.addWorksheet('Daily Sales');
        dailySheet.columns = [
            { header: 'Date', key: 'date', width: 15 },
            { header: 'Sales', key: 'sales', width: 15 },
            { header: 'Tax', key: 'tax', width: 15 },
            { header: 'Tips', key: 'tips', width: 15 },
            { header: 'Total', key: 'total', width: 15 },
            { header: 'Bill Count', key: 'billCount', width: 15 }
        ];

        // Add daily sales data
        Object.entries(reportData.dailySales).forEach(([date, data]) => {
            const total = data.sales + data.tax + data.tips;
            dailySheet.addRow({
                date,
                sales: data.sales.toFixed(2),
                tax: data.tax.toFixed(2),
                tips: data.tips.toFixed(2),
                total: total.toFixed(2),
                billCount: data.billCount
            });
        });

        // Format daily sheet
        dailySheet.getColumn('sales').numFmt = '$#,##0.00';
        dailySheet.getColumn('tax').numFmt = '$#,##0.00';
        dailySheet.getColumn('tips').numFmt = '$#,##0.00';
        dailySheet.getColumn('total').numFmt = '$#,##0.00';

        // Add Item Sales worksheet
        const itemsSheet = workbook.addWorksheet('Item Sales');
        itemsSheet.columns = [
            { header: 'Item', key: 'item', width: 30 },
            { header: 'Quantity Sold', key: 'quantity', width: 15 },
            { header: 'Revenue', key: 'revenue', width: 15 }
        ];

        // Add item sales data
        Object.entries(reportData.itemSales).forEach(([item, data]) => {
            itemsSheet.addRow({
                item,
                quantity: data.quantity,
                revenue: data.revenue.toFixed(2)
            });
        });

        // Format items sheet
        itemsSheet.getColumn('revenue').numFmt = '$#,##0.00';

        // Add Bills worksheet
        const billsSheet = workbook.addWorksheet('Bills');
        billsSheet.columns = [
            { header: 'Bill ID', key: 'id', width: 15 },
            { header: 'Date', key: 'date', width: 20 },
            { header: 'Customer', key: 'customer', width: 20 },
            { header: 'Table', key: 'table', width: 10 },
            { header: 'Subtotal', key: 'subtotal', width: 15 },
            { header: 'Tax', key: 'tax', width: 15 },
            { header: 'Tip', key: 'tip', width: 15 },
            { header: 'Total', key: 'total', width: 15 },
            { header: 'Payment Method', key: 'paymentMethod', width: 15 }
        ];

        // Add bills data
        reportData.bills.forEach(bill => {
            const subtotal = bill.payment?.subtotal || this.calculateSubtotal(bill.items);
            const tax = bill.payment?.tax || 0;
            const tip = bill.payment?.tip || 0;
            const total = bill.payment?.total || (subtotal + tax + tip);

            billsSheet.addRow({
                id: bill.id,
                date: new Date(bill.timestamp).toLocaleString(),
                customer: bill.customerName,
                table: bill.tableNumber,
                subtotal: subtotal.toFixed(2),
                tax: tax.toFixed(2),
                tip: tip.toFixed(2),
                total: total.toFixed(2),
                paymentMethod: bill.payment?.paymentMethod || 'Unknown'
            });
        });

        // Format bills sheet
        billsSheet.getColumn('subtotal').numFmt = '$#,##0.00';
        billsSheet.getColumn('tax').numFmt = '$#,##0.00';
        billsSheet.getColumn('tip').numFmt = '$#,##0.00';
        billsSheet.getColumn('total').numFmt = '$#,##0.00';

        // Save workbook
        const filePath = path.join(this.options.localBackupDir, filename);
        await workbook.xlsx.writeFile(filePath);
        console.log(`Excel report saved to ${filePath}`);

        return filePath;
    }

    /**
     * Send report via email
     * @param {string} filePath - Path to report file
     * @param {string} recipient - Email recipient
     * @param {string} subject - Email subject
     * @param {string} body - Email body
     * @returns {boolean} Success status
     */
    async sendEmail(filePath, recipient, subject, body) {
        if (!nodemailer) {
            console.error('Nodemailer is not available. Please install it with: npm install nodemailer');
            return false;
        }

        if (!this.options.emailConfig) {
            console.error('Email configuration not provided');
            return false;
        }

        try {
            const transporter = nodemailer.createTransport(this.options.emailConfig);

            const info = await transporter.sendMail({
                from: this.options.emailConfig.auth.user,
                to: recipient,
                subject: subject,
                text: body,
                attachments: [
                    {
                        filename: path.basename(filePath),
                        path: filePath
                    }
                ]
            });

            console.log(`Email sent: ${info.messageId}`);
            return true;
        } catch (error) {
            console.error('Error sending email:', error);
            return false;
        }
    }

    /**
     * Upload report to Google Sheets
     * @param {string} filePath - Path to report file
     * @param {string} spreadsheetId - Google Sheets spreadsheet ID
     * @returns {boolean} Success status
     */
    async uploadToGoogleSheets(filePath, spreadsheetId) {
        if (!google) {
            console.error('Google APIs are not available. Please install them with: npm install googleapis');
            return false;
        }

        if (!this.options.googleConfig) {
            console.error('Google API configuration not provided');
            return false;
        }

        try {
            // Authenticate with Google
            const auth = new google.auth.JWT(
                this.options.googleConfig.client_email,
                null,
                this.options.googleConfig.private_key,
                ['https://www.googleapis.com/auth/spreadsheets']
            );

            const sheets = google.sheets({ version: 'v4', auth });

            // Read Excel file
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(filePath);

            // Process each worksheet
            for (const worksheet of workbook.worksheets) {
                const sheetName = worksheet.name;
                const rows = [];

                // Get headers
                const headers = [];
                worksheet.getRow(1).eachCell(cell => {
                    headers.push(cell.value);
                });
                rows.push(headers);

                // Get data rows
                worksheet.eachRow((row, rowNumber) => {
                    if (rowNumber > 1) { // Skip header row
                        const rowData = [];
                        row.eachCell(cell => {
                            rowData.push(cell.value);
                        });
                        rows.push(rowData);
                    }
                });

                // Check if sheet exists, create if not
                const sheetsInfo = await sheets.spreadsheets.get({
                    spreadsheetId: spreadsheetId
                });

                const existingSheet = sheetsInfo.data.sheets.find(
                    s => s.properties.title === sheetName
                );

                if (!existingSheet) {
                    // Create new sheet
                    await sheets.spreadsheets.batchUpdate({
                        spreadsheetId: spreadsheetId,
                        resource: {
                            requests: [
                                {
                                    addSheet: {
                                        properties: {
                                            title: sheetName
                                        }
                                    }
                                }
                            ]
                        }
                    });
                }

                // Clear existing data
                await sheets.spreadsheets.values.clear({
                    spreadsheetId: spreadsheetId,
                    range: sheetName
                });

                // Update sheet with data
                await sheets.spreadsheets.values.update({
                    spreadsheetId: spreadsheetId,
                    range: sheetName,
                    valueInputOption: 'USER_ENTERED',
                    resource: {
                        values: rows
                    }
                });

                console.log(`Updated sheet: ${sheetName}`);
            }

            console.log(`Report uploaded to Google Sheets: ${spreadsheetId}`);
            return true;
        } catch (error) {
            console.error('Error uploading to Google Sheets:', error);
            return false;
        }
    }

    /**
     * Generate and export sales report with multiple destinations
     * @param {Object} options - Export options
     * @returns {Object} Export results
     */
    async exportSalesReport(options) {
        const {
            startDate,
            endDate,
            exportToLocal = true,
            exportToEmail = false,
            exportToGoogleSheets = false,
            emailRecipient = '',
            spreadsheetId = '',
            printReport = false
        } = options;

        // Generate report data
        const reportData = await this.generateSalesReport(startDate, endDate);

        // Create filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `sales-report-${startDate}-to-${endDate}-${timestamp}.xlsx`;

        const results = {
            success: true,
            localPath: null,
            emailSent: false,
            googleSheetsUpdated: false,
            printed: false,
            errors: []
        };

        try {
            // Export to local file
            if (exportToLocal) {
                results.localPath = await this.exportToExcel(reportData, filename);
            }

            // Send email
            if (exportToEmail && emailRecipient) {
                const subject = `Sales Report: ${startDate} to ${endDate}`;
                const body = `Please find attached the sales report for the period ${startDate} to ${endDate}.`;

                results.emailSent = await this.sendEmail(
                    results.localPath,
                    emailRecipient,
                    subject,
                    body
                );

                if (!results.emailSent) {
                    results.errors.push('Failed to send email');
                }
            }

            // Upload to Google Sheets
            if (exportToGoogleSheets && spreadsheetId) {
                results.googleSheetsUpdated = await this.uploadToGoogleSheets(
                    results.localPath,
                    spreadsheetId
                );

                if (!results.googleSheetsUpdated) {
                    results.errors.push('Failed to upload to Google Sheets');
                }
            }

            // Print report
            if (printReport && results.localPath) {
                // Implement printing functionality
                // This would typically involve opening the file with a suitable application
                // or using a printing library
                console.log(`Print functionality not implemented yet`);
                results.errors.push('Print functionality not implemented yet');
            }

            // Set overall success status
            results.success = results.errors.length === 0;

            return results;
        } catch (error) {
            console.error('Error exporting sales report:', error);
            results.success = false;
            results.errors.push(error.message);
            return results;
        }
    }
}

module.exports = DataExportService;
