<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Theme system -->
    <script src="../theme.js"></script>
</head>
<body>
    <div class="main-container">
        <!-- Left Side: Bill <PERSON>ails -->
        <div class="bill-side">
            <div class="bill-header">
                <h2><i class="fas fa-receipt"></i> Bill <PERSON></h2>
                <div class="customer-info">
                    <span id="table-number"><i class="fas fa-table"></i> Table: </span>
                    <span id="customer-name"><i class="fas fa-user"></i> Customer: </span>
                </div>
            </div>

            <div class="items-container">
                <h3><i class="fas fa-shopping-cart"></i> Items</h3>
                <ul id="items-list"></ul>
            </div>

            <div class="bill-summary">
                <div class="summary-row">
                    <span><i class="fas fa-calculator"></i> Subtotal:</span>
                    <span id="subtotal">$0.00</span>
                </div>
                <div class="summary-row">
                    <span><i class="fas fa-percentage"></i> GST (5%):<span class="tax-note">*card payments only</span></span>
                    <span id="gst">$0.00</span>
                </div>
                <div class="summary-row">
                    <span><i class="fas fa-percentage"></i> PST (7%):<span class="tax-note">*card payments only, applies to cold drinks, hookah & cover charges</span></span>
                    <span id="pst">$0.00</span>
                </div>
                <div class="summary-row total">
                    <span><i class="fas fa-dollar-sign"></i> Total:</span>
                    <span id="total">$0.00</span>
                </div>
                <div class="tax-info-note">
                    <i class="fas fa-info-circle"></i> Cash payments are tax-free
                </div>
            </div>
        </div>

        <!-- Right Side: Payment Options -->
        <div class="payment-side">
            <div class="payment-header">
                <h2><i class="fas fa-credit-card"></i> Payment Options</h2>
            </div>
            <div class="payment-buttons">
                <div class="payment-btn-row">
                    <button id="pay-cash" class="payment-btn"><i class="fas fa-money-bill-wave"></i> Pay Cash (No Tax)</button>
                    <button id="pay-card" class="payment-btn"><i class="fas fa-credit-card"></i> Pay Card</button>
                </div>
                <div class="payment-btn-row">
                    <button id="print-receipt" class="payment-btn"><i class="fas fa-print"></i> Print Receipt</button>
                    <button id="whatsapp-receipt" class="payment-btn"><i class="fab fa-whatsapp"></i> WhatsApp Receipt</button>
                </div>
                <div class="payment-btn-row">
                    <button id="edit-bill" class="payment-btn"><i class="fas fa-edit"></i> Edit</button>
                    <button id="pending-bill" class="payment-btn"><i class="fas fa-clock"></i> Pending</button>
                </div>
                <div class="payment-btn-row">
                    <button id="backup-button" class="payment-btn"><i class="fas fa-save"></i> Backup</button>
                    <button id="back-to-pos" class="payment-btn"><i class="fas fa-arrow-left"></i> Back to POS</button>
                </div>
            </div>
        </div>
    </div>

    <!-- WhatsApp Modal -->
    <div id="whatsapp-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Send Receipt via WhatsApp</h2>
            <div class="whatsapp-input">
                <input type="text" id="whatsapp-number" placeholder="Enter phone number" readonly>
                <div class="keypad">
                    <button class="key">1</button>
                    <button class="key">2</button>
                    <button class="key">3</button>
                    <button class="key">4</button>
                    <button class="key">5</button>
                    <button class="key">6</button>
                    <button class="key">7</button>
                    <button class="key">8</button>
                    <button class="key">9</button>
                    <button class="key">*</button>
                    <button class="key">0</button>
                    <button class="key">#</button>
                </div>
                <button id="send-whatsapp" class="action-btn">Send</button>
            </div>
        </div>
    </div>

    <!-- Custom Amount Modal -->
    <div id="custom-amount-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Enter Custom Amount</h3>
            <div class="amount-input">
                <input type="number" id="custom-amount" placeholder="Enter amount" step="0.01" min="0">
            </div>
            <div class="keypad">
                <button class="key">1</button>
                <button class="key">2</button>
                <button class="key">3</button>
                <button class="key">4</button>
                <button class="key">5</button>
                <button class="key">6</button>
                <button class="key">7</button>
                <button class="key">8</button>
                <button class="key">9</button>
                <button class="key">.</button>
                <button class="key">0</button>
                <button class="key">C</button>
            </div>
            <button id="apply-custom-amount" class="send-btn">Apply Amount</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>