<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Reports & Data Export - Paradiz <PERSON></title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles.css">
    <script src="../theme.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js for graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-chart-line"></i> Sales Reports & Data Export</h1>
            <div class="actions">
                <button id="back-btn" class="action-btn">
                    <i class="fas fa-arrow-left"></i> Back to Settings
                </button>
                <button id="print-btn" class="action-btn">
                    <i class="fas fa-print"></i> Print Report
                </button>
                <button id="export-btn" class="action-btn">
                    <i class="fas fa-file-export"></i> Export CSV
                </button>
            </div>
        </header>

        <div class="report-controls">
            <div class="date-range">
                <label for="date-range-select">Date Range:</label>
                <select id="date-range-select">
                    <option value="today">Today</option>
                    <option value="yesterday">Yesterday</option>
                    <option value="this-week" selected>This Week</option>
                    <option value="last-week">Last Week</option>
                    <option value="this-month">This Month</option>
                    <option value="last-month">Last Month</option>
                    <option value="custom">Custom Range</option>
                </select>
            </div>

            <div class="custom-date-range" id="custom-date-container" style="display: none;">
                <div class="date-input">
                    <label for="start-date">From:</label>
                    <input type="date" id="start-date">
                </div>
                <div class="date-input">
                    <label for="end-date">To:</label>
                    <input type="date" id="end-date">
                </div>
                <button id="apply-dates" class="apply-btn">Apply</button>
            </div>
        </div>

        <div class="report-summary">
            <div class="summary-card">
                <h3>Total Sales</h3>
                <div class="summary-value" id="total-sales">$0.00</div>
            </div>
            <div class="summary-card">
                <h3>Orders</h3>
                <div class="summary-value" id="total-orders">0</div>
            </div>
            <div class="summary-card">
                <h3>Average Order</h3>
                <div class="summary-value" id="average-order">$0.00</div>
            </div>
            <div class="summary-card">
                <h3>Most Popular Item</h3>
                <div class="summary-value" id="popular-item">-</div>
            </div>
        </div>

        <div class="report-charts">
            <div class="chart-container">
                <h3>Sales by Day</h3>
                <canvas id="sales-chart"></canvas>
            </div>
            <div class="chart-container">
                <h3>Sales by Category</h3>
                <canvas id="category-chart"></canvas>
            </div>
        </div>

        <div class="report-tables">
            <div class="table-container">
                <h3>Top Items</h3>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Quantity</th>
                            <th>Revenue</th>
                        </tr>
                    </thead>
                    <tbody id="top-items-body">
                        <tr>
                            <td colspan="3" class="loading">Loading data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="table-container">
                <h3>Sales by Payment Method</h3>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Orders</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody id="payment-methods-body">
                        <tr>
                            <td colspan="3" class="loading">Loading data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="report-details">
            <h3>Order Details</h3>
            <div class="table-controls">
                <div class="search-box">
                    <input type="text" id="order-search" placeholder="Search orders...">
                </div>
                <div class="table-pagination">
                    <button id="prev-page" disabled><i class="fas fa-chevron-left"></i></button>
                    <span id="page-info">Page 1 of 1</span>
                    <button id="next-page" disabled><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
            <table class="report-table orders-table">
                <thead>
                    <tr>
                        <th>Date/Time</th>
                        <th>Table</th>
                        <th>Customer</th>
                        <th>Items</th>
                        <th>Total</th>
                        <th>Payment</th>
                    </tr>
                </thead>
                <tbody id="orders-table-body">
                    <tr>
                        <td colspan="6" class="loading">Loading orders...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Data Export Section -->
        <div class="data-export-section">
            <h2><i class="fas fa-file-export"></i> Data Export</h2>
            <p class="section-description">Export your sales data, items, cover charges, and taxes in various formats.</p>

            <div class="export-options-container">
                <div class="export-form">
                    <div class="form-group">
                        <label for="export-start-date">Start Date</label>
                        <input type="date" id="export-start-date" class="date-input">
                    </div>
                    <div class="form-group">
                        <label for="export-end-date">End Date</label>
                        <input type="date" id="export-end-date" class="date-input">
                    </div>

                    <div class="export-destinations">
                        <h3>Export Destinations</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="export-to-excel" checked>
                                <label for="export-to-excel">Excel Spreadsheet</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="export-to-email">
                                <label for="export-to-email">Email</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="export-to-google">
                                <label for="export-to-google">Google Sheets</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="export-to-print">
                                <label for="export-to-print">Print</label>
                            </div>
                        </div>
                    </div>

                    <div id="email-export-options" class="conditional-options" style="display: none;">
                        <div class="form-group">
                            <label for="email-recipient">Email Recipient</label>
                            <input type="email" id="email-recipient" placeholder="Enter email address">
                        </div>
                    </div>

                    <div id="google-export-options" class="conditional-options" style="display: none;">
                        <div class="form-group">
                            <label for="google-spreadsheet-id">Google Spreadsheet ID</label>
                            <input type="text" id="google-spreadsheet-id" placeholder="Enter spreadsheet ID">
                        </div>
                        <div class="help-text">
                            <p><i class="fas fa-info-circle"></i> The spreadsheet ID is the long string in the URL of your Google Sheet.</p>
                        </div>
                    </div>

                    <div class="export-actions">
                        <button id="generate-export-btn" class="action-btn export-btn">
                            <i class="fas fa-file-export"></i> Generate Export
                        </button>
                    </div>
                </div>

                <div class="export-info">
                    <h3>What's Included in the Export</h3>
                    <ul class="export-contents">
                        <li><i class="fas fa-check"></i> <strong>Sales Summary:</strong> Total sales, tax, tips, and cover charges</li>
                        <li><i class="fas fa-check"></i> <strong>Daily Sales:</strong> Breakdown of sales by day</li>
                        <li><i class="fas fa-check"></i> <strong>Item Sales:</strong> Detailed list of items sold with quantities and revenue</li>
                        <li><i class="fas fa-check"></i> <strong>Payment Methods:</strong> Sales by payment method (cash, card, etc.)</li>
                        <li><i class="fas fa-check"></i> <strong>Bill Details:</strong> Complete list of all bills in the selected period</li>
                    </ul>

                    <div class="auto-export-settings">
                        <h3>Automatic Exports</h3>
                        <div class="form-group">
                            <label for="auto-export-enabled">Enable Daily Exports</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="auto-export-enabled">
                                <span class="slider"></span>
                            </div>
                        </div>
                        <div id="auto-export-options" style="display: none;">
                            <div class="form-group">
                                <label for="auto-export-time">Export Time</label>
                                <input type="time" id="auto-export-time" value="23:59">
                            </div>
                            <div class="form-group">
                                <label for="auto-export-email">Send to Email</label>
                                <input type="email" id="auto-export-email" placeholder="Enter email address">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="success-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <h2>Success!</h2>
                <p id="success-message-text">Your export has been generated successfully.</p>
                <div id="success-actions">
                    <!-- Actions will be added dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div id="error-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <h2>Error</h2>
                <p id="error-message-text">An error occurred while generating your export.</p>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
