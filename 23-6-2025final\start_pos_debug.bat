@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo        Starting Paradiz POS System (Debug)
echo ===================================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH.
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Display Node.js version
echo Node.js version:
node -v
echo.

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: npm is not installed or not in PATH.
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Display npm version
echo npm version:
npm -v
echo.

REM Check if node_modules exists
if not exist node_modules (
    echo Installing dependencies...
    call npm install --no-optional
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install dependencies.
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )
    echo Dependencies installed successfully.
    echo.
) else (
    echo Dependencies already installed.
    echo.
)

REM Install required dependencies for data export
echo Checking for required dependencies...

REM Check if exceljs is installed
node -e "try { require('exceljs'); console.log('ExcelJS is installed.'); } catch(e) { console.log('ExcelJS is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing ExcelJS...
    call npm install exceljs
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install ExcelJS.
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )
    echo ExcelJS installed successfully.
    echo.
) else (
    echo ExcelJS already installed.
    echo.
)

REM Check if nodemailer is installed
node -e "try { require('nodemailer'); console.log('Nodemailer is installed.'); } catch(e) { console.log('Nodemailer is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Nodemailer...
    call npm install nodemailer
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install Nodemailer.
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )
    echo Nodemailer installed successfully.
    echo.
) else (
    echo Nodemailer already installed.
    echo.
)

REM Check if googleapis is installed
node -e "try { require('googleapis'); console.log('Google APIs is installed.'); } catch(e) { console.log('Google APIs is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Google APIs...
    call npm install googleapis
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install Google APIs.
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )
    echo Google APIs installed successfully.
    echo.
) else (
    echo Google APIs already installed.
    echo.
)

REM Check if server.js exists
if not exist server.js (
    echo ERROR: server.js file not found.
    echo Please make sure you are running this script from the correct directory.
    echo.
    pause
    exit /b 1
)

REM Check server.js for syntax errors
echo Checking server.js for syntax errors...
node --check server.js
if %ERRORLEVEL% neq 0 (
    echo ERROR: server.js contains syntax errors.
    echo Please fix the errors before starting the server.
    echo.
    pause
    exit /b 1
)
echo Server file syntax check passed.
echo.

REM Start the server in a new window with error handling
echo Starting server...
start "Paradiz POS Server" cmd /k "echo Server starting... && echo. && node server.js || (echo. && echo SERVER ERROR: The server crashed or failed to start. && echo. && pause)"

REM Wait for the server to start
echo Waiting for server to start...
timeout /t 5 /nobreak > nul

REM Check if the server is running by trying to connect to it
echo Checking if server is running...
curl -s http://localhost:5000 >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo WARNING: Could not connect to the server.
    echo The server might still be starting or may have failed to start.
    echo Check the server window for error messages.
    echo.
    echo Press any key to try opening the browser anyway...
    pause >nul
) else (
    echo Server is running successfully.
)
echo.

REM Open the main page in the default browser
echo Opening application in browser...
start http://localhost:5000

echo.
echo ===================================================
echo POS system startup complete!
echo ===================================================
echo.
echo IMPORTANT:
echo - The server is running in a separate window.
echo - DO NOT close that window while using the application.
echo - If you encounter any issues, check the server window for error messages.
echo.
echo This window can be closed safely.
echo Press any key to exit this window...
pause > nul
