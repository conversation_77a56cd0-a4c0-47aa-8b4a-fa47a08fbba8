@echo off
echo Starting Paradiz POS System...

REM Check if node_modules exists
if not exist node_modules (
    echo Installing dependencies...
    call npm install --no-optional
    if %ERRORLEVEL% neq 0 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Install required dependencies for data export
echo Checking for required dependencies...

REM Check if exceljs is installed
node -e "try { require('exceljs'); console.log('ExcelJS is installed.'); } catch(e) { console.log('ExcelJS is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing ExcelJS...
    call npm install exceljs
    if %ERRORLEVEL% neq 0 (
        echo Failed to install ExcelJS
        pause
        exit /b 1
    )
    echo ExcelJS installed successfully.
)

REM Check if nodemailer is installed
node -e "try { require('nodemailer'); console.log('Nodemailer is installed.'); } catch(e) { console.log('Nodemailer is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Nodemailer...
    call npm install nodemailer
    if %ERRORLEVEL% neq 0 (
        echo Failed to install <PERSON>demailer
        pause
        exit /b 1
    )
    echo Nodemailer installed successfully.
)

REM Check if googleapis is installed
node -e "try { require('googleapis'); console.log('Google APIs is installed.'); } catch(e) { console.log('Google APIs is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Google APIs...
    call npm install googleapis
    if %ERRORLEVEL% neq 0 (
        echo Failed to install Google APIs
        pause
        exit /b 1
    )
    echo Google APIs installed successfully.
)

REM Check if bcrypt is installed (required for mobile app)
node -e "try { require('bcrypt'); console.log('bcrypt is installed.'); } catch(e) { console.log('bcrypt is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing bcrypt...
    call npm install bcrypt
    if %ERRORLEVEL% neq 0 (
        echo Failed to install bcrypt
        pause
        exit /b 1
    )
    echo bcrypt installed successfully.
)

REM Check if jsonwebtoken is installed (required for mobile app)
node -e "try { require('jsonwebtoken'); console.log('jsonwebtoken is installed.'); } catch(e) { console.log('jsonwebtoken is not installed.'); process.exit(1); }" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing jsonwebtoken...
    call npm install jsonwebtoken
    if %ERRORLEVEL% neq 0 (
        echo Failed to install jsonwebtoken
        pause
        exit /b 1
    )
    echo jsonwebtoken installed successfully.
)

REM Start the server in the same window to see any errors
echo Starting server...
start "Paradiz POS Server" cmd /k "node server.js || (echo Server failed to start. Press any key to exit. && pause)"

REM Wait a moment for the server to start
timeout /t 3 /nobreak > nul

REM Open the main page in the default browser
echo Opening application in browser...
start http://localhost:5000

echo POS system started successfully!
echo The server is running in a separate window. Do not close it while using the application.
echo.
echo Press any key to exit this window...
pause > nul
