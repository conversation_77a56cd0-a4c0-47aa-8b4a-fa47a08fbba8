// Import the data export service
let dataExportService;

// Check if we're running in a Node.js environment or browser
if (typeof require !== 'undefined') {
    try {
        dataExportService = require('../data-export-service');
    } catch (e) {
        console.log('Running in browser mode, data export service will be loaded via API');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const dateRangeSelect = document.getElementById('date-range-select');
    const customDateContainer = document.getElementById('custom-date-container');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const applyDatesBtn = document.getElementById('apply-dates');
    const backBtn = document.getElementById('back-btn');
    const printBtn = document.getElementById('print-btn');
    const exportBtn = document.getElementById('export-btn');
    const orderSearch = document.getElementById('order-search');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const pageInfo = document.getElementById('page-info');

    // Summary elements
    const totalSalesElement = document.getElementById('total-sales');
    const totalOrdersElement = document.getElementById('total-orders');
    const averageOrderElement = document.getElementById('average-order');
    const popularItemElement = document.getElementById('popular-item');

    // Table bodies
    const topItemsBody = document.getElementById('top-items-body');
    const paymentMethodsBody = document.getElementById('payment-methods-body');
    const ordersTableBody = document.getElementById('orders-table-body');

    // Chart canvases
    const salesChart = document.getElementById('sales-chart');
    const categoryChart = document.getElementById('category-chart');

    // State
    let allOrders = [];
    let filteredOrders = [];
    let currentPage = 1;
    let itemsPerPage = 10;
    let salesChartInstance = null;
    let categoryChartInstance = null;

    // Initialize date inputs with default values
    const today = new Date();
    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(today.getDate() - 7);

    startDateInput.value = formatDateForInput(oneWeekAgo);
    endDateInput.value = formatDateForInput(today);

    // Initialize the report with default date range (This Week)
    loadReport();

    // Data Export Elements
    const exportStartDateInput = document.getElementById('export-start-date');
    const exportEndDateInput = document.getElementById('export-end-date');
    const exportToExcelCheckbox = document.getElementById('export-to-excel');
    const exportToEmailCheckbox = document.getElementById('export-to-email');
    const exportToGoogleCheckbox = document.getElementById('export-to-google');
    const exportToPrintCheckbox = document.getElementById('export-to-print');
    const emailExportOptions = document.getElementById('email-export-options');
    const googleExportOptions = document.getElementById('google-export-options');
    const emailRecipientInput = document.getElementById('email-recipient');
    const googleSpreadsheetIdInput = document.getElementById('google-spreadsheet-id');
    const generateExportBtn = document.getElementById('generate-export-btn');
    const autoExportEnabledCheckbox = document.getElementById('auto-export-enabled');
    const autoExportOptions = document.getElementById('auto-export-options');
    const autoExportTimeInput = document.getElementById('auto-export-time');
    const autoExportEmailInput = document.getElementById('auto-export-email');

    // Modal Elements
    const successModal = document.getElementById('success-modal');
    const errorModal = document.getElementById('error-modal');
    const successMessageText = document.getElementById('success-message-text');
    const errorMessageText = document.getElementById('error-message-text');
    const successActions = document.getElementById('success-actions');
    const closeModalButtons = document.querySelectorAll('.close-modal');

    // Initialize export date inputs with default values
    exportStartDateInput.value = formatDateForInput(oneWeekAgo);
    exportEndDateInput.value = formatDateForInput(today);

    // Event Listeners
    dateRangeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDateContainer.style.display = 'flex';
        } else {
            customDateContainer.style.display = 'none';
            updateDateRange(this.value);
            loadReport();
        }
    });

    applyDatesBtn.addEventListener('click', function() {
        loadReport();
    });

    backBtn.addEventListener('click', function() {
        window.location.href = '../settings/index.html';
    });

    printBtn.addEventListener('click', function() {
        window.print();
    });

    exportBtn.addEventListener('click', function() {
        exportToCSV();
    });

    orderSearch.addEventListener('input', function() {
        filterOrders();
        renderOrdersTable();
    });

    prevPageBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            renderOrdersTable();
        }
    });

    nextPageBtn.addEventListener('click', function() {
        const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderOrdersTable();
        }
    });

    // Functions

    // Update date inputs based on selected range
    function updateDateRange(range) {
        const today = new Date();
        let startDate = new Date(today);
        let endDate = new Date(today);

        switch(range) {
            case 'today':
                // Start and end are both today
                break;

            case 'yesterday':
                startDate.setDate(today.getDate() - 1);
                endDate.setDate(today.getDate() - 1);
                break;

            case 'this-week':
                // Start date is the beginning of the current week (Sunday)
                const dayOfWeek = today.getDay(); // 0 is Sunday
                startDate.setDate(today.getDate() - dayOfWeek);
                break;

            case 'last-week':
                // Start date is the beginning of last week
                const lastWeekDay = today.getDay();
                startDate.setDate(today.getDate() - lastWeekDay - 7);
                endDate.setDate(today.getDate() - lastWeekDay - 1);
                break;

            case 'this-month':
                // Start date is the 1st of the current month
                startDate.setDate(1);
                break;

            case 'last-month':
                // Start date is the 1st of the previous month
                startDate.setMonth(today.getMonth() - 1);
                startDate.setDate(1);
                endDate.setDate(0); // Last day of previous month
                break;
        }

        startDateInput.value = formatDateForInput(startDate);
        endDateInput.value = formatDateForInput(endDate);
    }

    // Format date for input field (YYYY-MM-DD)
    function formatDateForInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // Format date for display (MM/DD/YYYY)
    function formatDateForDisplay(dateString) {
        const date = new Date(dateString);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const year = date.getFullYear();
        return `${month}/${day}/${year}`;
    }

    // Format time for display (HH:MM AM/PM)
    function formatTimeForDisplay(dateString) {
        const date = new Date(dateString);
        let hours = date.getHours();
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12;
        hours = hours ? hours : 12; // Convert 0 to 12
        return `${hours}:${minutes} ${ampm}`;
    }

    // Format currency
    function formatCurrency(amount) {
        return '$' + parseFloat(amount).toFixed(2);
    }

    // Load report data
    async function loadReport() {
        try {
            showLoading();

            const startDate = startDateInput.value;
            const endDate = endDateInput.value;

            // Fetch orders from the server
            const response = await fetch(`http://localhost:5000/api/orders/report?startDate=${startDate}&endDate=${endDate}`);
            const data = await response.json();

            if (data.success) {
                allOrders = data.orders;
                processReportData(allOrders);
            } else {
                showError('Failed to load report data');
            }
        } catch (error) {
            console.error('Error loading report:', error);
            showError('Error loading report data');
        }
    }

    // Process the report data
    function processReportData(orders) {
        if (!orders || orders.length === 0) {
            showNoData();
            return;
        }

        // Calculate summary metrics
        const totalSales = orders.reduce((sum, order) => sum + calculateOrderTotal(order), 0);
        const totalOrders = orders.length;
        const averageOrder = totalOrders > 0 ? totalSales / totalOrders : 0;

        // Find most popular item
        const itemCounts = {};
        orders.forEach(order => {
            const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
            items.forEach(item => {
                const itemName = item.name;
                itemCounts[itemName] = (itemCounts[itemName] || 0) + (item.quantity || 1);
            });
        });

        let popularItem = '-';
        let maxCount = 0;
        for (const [item, count] of Object.entries(itemCounts)) {
            if (count > maxCount) {
                maxCount = count;
                popularItem = item;
            }
        }

        // Update summary elements
        totalSalesElement.textContent = formatCurrency(totalSales);
        totalOrdersElement.textContent = totalOrders;
        averageOrderElement.textContent = formatCurrency(averageOrder);
        popularItemElement.textContent = popularItem;

        // Generate top items table
        generateTopItemsTable(orders);

        // Generate payment methods table
        generatePaymentMethodsTable(orders);

        // Generate charts
        generateSalesChart(orders);
        generateCategoryChart(orders);

        // Set up orders table
        filteredOrders = [...orders];
        renderOrdersTable();
    }

    // Calculate order total
    function calculateOrderTotal(order) {
        let total = 0;
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');

        items.forEach(item => {
            const quantity = item.quantity || 1;
            const price = parseFloat(item.price) || 0;
            total += quantity * price;
        });

        return total;
    }

    // Generate top items table
    function generateTopItemsTable(orders) {
        // Collect item data
        const itemData = {};

        orders.forEach(order => {
            const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
            items.forEach(item => {
                const itemName = item.name;
                const quantity = item.quantity || 1;
                const price = parseFloat(item.price) || 0;

                if (!itemData[itemName]) {
                    itemData[itemName] = {
                        quantity: 0,
                        revenue: 0
                    };
                }

                itemData[itemName].quantity += quantity;
                itemData[itemName].revenue += quantity * price;
            });
        });

        // Convert to array and sort by revenue
        const sortedItems = Object.entries(itemData)
            .map(([name, data]) => ({
                name,
                quantity: data.quantity,
                revenue: data.revenue
            }))
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 5); // Top 5 items

        // Generate table rows
        if (sortedItems.length === 0) {
            topItemsBody.innerHTML = '<tr><td colspan="3" class="no-data">No data available</td></tr>';
            return;
        }

        topItemsBody.innerHTML = sortedItems.map(item => `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${formatCurrency(item.revenue)}</td>
            </tr>
        `).join('');
    }

    // Generate payment methods table
    function generatePaymentMethodsTable(orders) {
        // Collect payment method data
        const paymentData = {};

        orders.forEach(order => {
            const method = order.paymentMethod || 'Unknown';
            const total = calculateOrderTotal(order);

            if (!paymentData[method]) {
                paymentData[method] = {
                    count: 0,
                    amount: 0
                };
            }

            paymentData[method].count += 1;
            paymentData[method].amount += total;
        });

        // Convert to array
        const paymentMethods = Object.entries(paymentData)
            .map(([method, data]) => ({
                method,
                count: data.count,
                amount: data.amount
            }))
            .sort((a, b) => b.amount - a.amount);

        // Generate table rows
        if (paymentMethods.length === 0) {
            paymentMethodsBody.innerHTML = '<tr><td colspan="3" class="no-data">No data available</td></tr>';
            return;
        }

        paymentMethodsBody.innerHTML = paymentMethods.map(item => `
            <tr>
                <td>${item.method}</td>
                <td>${item.count}</td>
                <td>${formatCurrency(item.amount)}</td>
            </tr>
        `).join('');
    }

    // Generate sales chart
    function generateSalesChart(orders) {
        // Group orders by day
        const salesByDay = {};

        orders.forEach(order => {
            const date = new Date(order.timestamp);
            const dateString = formatDateForDisplay(date);
            const total = calculateOrderTotal(order);

            if (!salesByDay[dateString]) {
                salesByDay[dateString] = 0;
            }

            salesByDay[dateString] += total;
        });

        // Sort dates
        const sortedDates = Object.keys(salesByDay).sort((a, b) => {
            const dateA = new Date(a.split('/').reverse().join('-'));
            const dateB = new Date(b.split('/').reverse().join('-'));
            return dateA - dateB;
        });

        const salesData = sortedDates.map(date => salesByDay[date]);

        // Destroy existing chart if it exists
        if (salesChartInstance) {
            salesChartInstance.destroy();
        }

        // Create new chart
        const ctx = salesChart.getContext('2d');
        salesChartInstance = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sortedDates,
                datasets: [{
                    label: 'Sales',
                    data: salesData,
                    backgroundColor: 'rgba(52, 152, 219, 0.7)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Sales: ' + formatCurrency(context.raw);
                            }
                        }
                    }
                }
            }
        });
    }

    // Generate category chart
    function generateCategoryChart(orders) {
        // Extract categories from items
        const categoryData = {};

        orders.forEach(order => {
            const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
            items.forEach(item => {
                // For this example, we'll extract category from item name
                // In a real system, you'd have category data in your items
                let category = 'Other';

                if (item.name.toLowerCase().includes('hookah')) {
                    category = 'Hookah';
                } else if (item.name.toLowerCase().includes('tea') ||
                           item.name.toLowerCase().includes('coffee') ||
                           item.name.toLowerCase().includes('juice') ||
                           item.name.toLowerCase().includes('water')) {
                    category = 'Drinks';
                } else if (item.name.toLowerCase().includes('pizza') ||
                           item.name.toLowerCase().includes('burger') ||
                           item.name.toLowerCase().includes('fries')) {
                    category = 'Food';
                } else if (item.name.toLowerCase().includes('cover charge')) {
                    category = 'Cover Charge';
                }

                const quantity = item.quantity || 1;
                const price = parseFloat(item.price) || 0;
                const total = quantity * price;

                if (!categoryData[category]) {
                    categoryData[category] = 0;
                }

                categoryData[category] += total;
            });
        });

        const categories = Object.keys(categoryData);
        const amounts = Object.values(categoryData);

        // Color palette
        const colors = [
            'rgba(52, 152, 219, 0.7)',  // Blue
            'rgba(46, 204, 113, 0.7)',  // Green
            'rgba(155, 89, 182, 0.7)',  // Purple
            'rgba(241, 196, 15, 0.7)',  // Yellow
            'rgba(231, 76, 60, 0.7)'    // Red
        ];

        // Destroy existing chart if it exists
        if (categoryChartInstance) {
            categoryChartInstance.destroy();
        }

        // Create new chart
        const ctx = categoryChart.getContext('2d');
        categoryChartInstance = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: categories,
                datasets: [{
                    data: amounts,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace('0.7', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.raw);
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((context.raw / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Filter orders based on search input
    function filterOrders() {
        const searchTerm = orderSearch.value.toLowerCase();

        if (!searchTerm) {
            filteredOrders = [...allOrders];
        } else {
            filteredOrders = allOrders.filter(order => {
                // Search in table number, customer name, and items
                return (
                    order.tableNumber?.toString().includes(searchTerm) ||
                    order.customerName?.toLowerCase().includes(searchTerm) ||
                    JSON.stringify(order.items).toLowerCase().includes(searchTerm)
                );
            });
        }

        currentPage = 1; // Reset to first page when filtering
    }

    // Render orders table with pagination
    function renderOrdersTable() {
        if (filteredOrders.length === 0) {
            ordersTableBody.innerHTML = '<tr><td colspan="6" class="no-data">No orders found</td></tr>';
            prevPageBtn.disabled = true;
            nextPageBtn.disabled = true;
            pageInfo.textContent = 'Page 0 of 0';
            return;
        }

        // Calculate pagination
        const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, filteredOrders.length);
        const currentOrders = filteredOrders.slice(startIndex, endIndex);

        // Update pagination controls
        prevPageBtn.disabled = currentPage === 1;
        nextPageBtn.disabled = currentPage === totalPages;
        pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

        // Generate table rows
        ordersTableBody.innerHTML = currentOrders.map(order => {
            const date = new Date(order.timestamp);
            const dateString = formatDateForDisplay(date);
            const timeString = formatTimeForDisplay(date);
            const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
            const total = calculateOrderTotal(order);

            return `
                <tr>
                    <td>${dateString}<br>${timeString}</td>
                    <td>${order.tableNumber || 'N/A'}</td>
                    <td>${order.customerName || 'N/A'}</td>
                    <td>
                        <ul class="item-list">
                            ${items.map(item => `
                                <li>${item.quantity || 1}x ${item.name}</li>
                            `).join('')}
                        </ul>
                    </td>
                    <td>${formatCurrency(total)}</td>
                    <td>${order.paymentMethod || 'Not Paid'}</td>
                </tr>
            `;
        }).join('');
    }

    // Export data to CSV
    function exportToCSV() {
        if (allOrders.length === 0) {
            alert('No data to export');
            return;
        }

        // Prepare CSV content
        let csvContent = 'Date,Time,Table,Customer,Items,Total,Payment Method\n';

        allOrders.forEach(order => {
            const date = new Date(order.timestamp);
            const dateString = formatDateForDisplay(date);
            const timeString = formatTimeForDisplay(date);
            const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
            const itemsText = items.map(item => `${item.quantity || 1}x ${item.name}`).join('; ');
            const total = calculateOrderTotal(order);

            // Escape fields that might contain commas
            const escapedItems = `"${itemsText.replace(/"/g, '""')}"`;
            const escapedCustomer = `"${(order.customerName || 'N/A').replace(/"/g, '""')}"`;

            csvContent += `${dateString},${timeString},${order.tableNumber || 'N/A'},${escapedCustomer},${escapedItems},${formatCurrency(total)},${order.paymentMethod || 'Not Paid'}\n`;
        });

        // Create download link
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', 'sales_report.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Show loading state
    function showLoading() {
        topItemsBody.innerHTML = '<tr><td colspan="3" class="loading">Loading data...</td></tr>';
        paymentMethodsBody.innerHTML = '<tr><td colspan="3" class="loading">Loading data...</td></tr>';
        ordersTableBody.innerHTML = '<tr><td colspan="6" class="loading">Loading orders...</td></tr>';
    }

    // Show error message
    function showError(message) {
        topItemsBody.innerHTML = `<tr><td colspan="3" class="error">${message}</td></tr>`;
        paymentMethodsBody.innerHTML = `<tr><td colspan="3" class="error">${message}</td></tr>`;
        ordersTableBody.innerHTML = `<tr><td colspan="6" class="error">${message}</td></tr>`;
    }

    // Show no data message
    function showNoData() {
        totalSalesElement.textContent = '$0.00';
        totalOrdersElement.textContent = '0';
        averageOrderElement.textContent = '$0.00';
        popularItemElement.textContent = '-';

        topItemsBody.innerHTML = '<tr><td colspan="3" class="no-data">No data available</td></tr>';
        paymentMethodsBody.innerHTML = '<tr><td colspan="3" class="no-data">No data available</td></tr>';
        ordersTableBody.innerHTML = '<tr><td colspan="6" class="no-data">No orders found</td></tr>';

        // Create empty charts
        if (salesChartInstance) salesChartInstance.destroy();
        if (categoryChartInstance) categoryChartInstance.destroy();

        const salesCtx = salesChart.getContext('2d');
        salesChartInstance = new Chart(salesCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Sales',
                    data: [],
                    backgroundColor: 'rgba(52, 152, 219, 0.7)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        const categoryCtx = categoryChart.getContext('2d');
        categoryChartInstance = new Chart(categoryCtx, {
            type: 'pie',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: []
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // Data Export Event Listeners
    exportToEmailCheckbox.addEventListener('change', function() {
        emailExportOptions.style.display = this.checked ? 'block' : 'none';
    });

    exportToGoogleCheckbox.addEventListener('change', function() {
        googleExportOptions.style.display = this.checked ? 'block' : 'none';
    });

    autoExportEnabledCheckbox.addEventListener('change', function() {
        autoExportOptions.style.display = this.checked ? 'block' : 'none';
    });

    generateExportBtn.addEventListener('click', function() {
        generateDataExport();
    });

    // Close modals when clicking the close button
    closeModalButtons.forEach(button => {
        button.addEventListener('click', function() {
            successModal.style.display = 'none';
            errorModal.style.display = 'none';
        });
    });

    // Close modals when clicking outside the modal content
    window.addEventListener('click', function(event) {
        if (event.target === successModal) {
            successModal.style.display = 'none';
        }
        if (event.target === errorModal) {
            errorModal.style.display = 'none';
        }
    });

    // Generate and export data
    async function generateDataExport() {
        // Validate inputs
        const startDate = exportStartDateInput.value;
        const endDate = exportEndDateInput.value;

        if (!startDate || !endDate) {
            showError('Please select both start and end dates');
            return;
        }

        // Check if at least one export destination is selected
        if (!exportToExcelCheckbox.checked &&
            !exportToEmailCheckbox.checked &&
            !exportToGoogleCheckbox.checked &&
            !exportToPrintCheckbox.checked) {
            showError('Please select at least one export destination');
            return;
        }

        // Validate email if email export is selected
        if (exportToEmailCheckbox.checked && !validateEmail(emailRecipientInput.value)) {
            showError('Please enter a valid email address');
            return;
        }

        // Validate Google Spreadsheet ID if Google export is selected
        if (exportToGoogleCheckbox.checked && !googleSpreadsheetIdInput.value.trim()) {
            showError('Please enter a Google Spreadsheet ID');
            return;
        }

        // Show loading state
        generateExportBtn.disabled = true;
        generateExportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

        try {
            // Prepare export options
            const exportOptions = {
                startDate: startDate,
                endDate: endDate,
                exportToLocal: exportToExcelCheckbox.checked,
                exportToEmail: exportToEmailCheckbox.checked,
                exportToGoogleSheets: exportToGoogleCheckbox.checked,
                printReport: exportToPrintCheckbox.checked,
                emailRecipient: emailRecipientInput.value,
                spreadsheetId: googleSpreadsheetIdInput.value
            };

            // Call the API to generate the export
            const response = await fetch('/api/export-sales', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(exportOptions)
            });

            const result = await response.json();

            if (result.success) {
                // Show success message
                showSuccess(result);

                // Save auto-export settings if enabled
                if (autoExportEnabledCheckbox.checked) {
                    saveAutoExportSettings();
                }
            } else {
                showError(result.error || 'Failed to generate export');
            }
        } catch (error) {
            console.error('Error generating export:', error);
            showError('An error occurred while generating the export');
        } finally {
            // Reset button state
            generateExportBtn.disabled = false;
            generateExportBtn.innerHTML = '<i class="fas fa-file-export"></i> Generate Export';
        }
    }

    // Save auto-export settings
    async function saveAutoExportSettings() {
        try {
            const settings = {
                enabled: autoExportEnabledCheckbox.checked,
                time: autoExportTimeInput.value,
                email: autoExportEmailInput.value
            };

            await fetch('/api/settings/auto-export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(settings)
            });

            console.log('Auto-export settings saved');
        } catch (error) {
            console.error('Error saving auto-export settings:', error);
        }
    }

    // Show success modal
    function showSuccess(result) {
        successMessageText.textContent = 'Your export has been generated successfully!';

        // Clear previous actions
        successActions.innerHTML = '';

        // Add actions based on export results
        if (result.localPath) {
            const openButton = document.createElement('button');
            openButton.className = 'action-btn';
            openButton.innerHTML = '<i class="fas fa-folder-open"></i> Open File Location';
            openButton.onclick = function() {
                window.open(`file://${result.localPath.replace(/\\/g, '/')}`, '_blank');
            };
            successActions.appendChild(openButton);
        }

        if (result.emailSent) {
            const emailInfo = document.createElement('p');
            emailInfo.innerHTML = '<i class="fas fa-envelope"></i> Export sent to your email';
            successActions.appendChild(emailInfo);
        }

        if (result.googleSheetsUpdated) {
            const googleButton = document.createElement('button');
            googleButton.className = 'action-btn';
            googleButton.innerHTML = '<i class="fas fa-table"></i> Open Google Sheet';
            googleButton.onclick = function() {
                window.open(`https://docs.google.com/spreadsheets/d/${googleSpreadsheetIdInput.value}`, '_blank');
            };
            successActions.appendChild(googleButton);
        }

        // Show the modal
        successModal.style.display = 'block';
    }

    // Show error modal
    function showError(message) {
        errorMessageText.textContent = message;
        errorModal.style.display = 'block';
    }

    // Validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
});
