import React, { useState, useEffect, useContext } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, Image, ActivityIndicator } from 'react-native';
import { eventsApi } from '../api/api';
import NotificationService from '../utils/NotificationService';
import { Ionicons } from '@expo/vector-icons';
import { NotificationContext } from '../context/NotificationContext';

const EventDetailScreen = ({ route, navigation }) => {
  const { eventId } = route.params;
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [reminderSet, setReminderSet] = useState(false);
  const [reminderNotificationId, setReminderNotificationId] = useState(null);

  const { scheduleEventReminder } = useContext(NotificationContext);

  useEffect(() => {
    fetchEventDetails();
  }, []);

  const fetchEventDetails = async () => {
    try {
      setLoading(true);
      const response = await eventsApi.getEventDetails(eventId);
      setEvent(response.data);
      setReminderSet(response.data.reminderSet || false);
      setReminderNotificationId(response.data.reminderNotificationId || null);
    } catch (error) {
      console.error('Error fetching event details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSetReminder = async () => {
    try {
      // Toggle reminder status
      const newReminderStatus = !reminderSet;

      if (newReminderStatus) {
        // Schedule reminder notification
        const notificationId = await scheduleEventReminder(event);

        if (notificationId) {
          // Update on server
          await eventsApi.setEventReminder(eventId, {
            setReminder: true,
            notificationId
          });

          // Update local state
          setReminderSet(true);
          setReminderNotificationId(notificationId);
        }
      } else {
        // Cancel reminder notification
        if (reminderNotificationId) {
          await NotificationService.cancelScheduledNotification(reminderNotificationId);
        }

        // Update on server
        await eventsApi.setEventReminder(eventId, {
          setReminder: false
        });

        // Update local state
        setReminderSet(false);
        setReminderNotificationId(null);
      }
    } catch (error) {
      console.error('Error setting reminder:', error);
    }
  };

  const formatDate = (dateString) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const formatTime = (timeString) => {
    return timeString;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6B46C1" />
      </View>
    );
  }

  if (!event) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Could not load event details</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={fetchEventDetails}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.imageContainer}>
        {event.imageUrl ? (
          <Image source={{ uri: event.imageUrl }} style={styles.eventImage} />
        ) : (
          <View style={styles.placeholderImage}>
            <Ionicons name="calendar" size={64} color="#6B46C1" />
          </View>
        )}
      </View>

      <View style={styles.eventCard}>
        <Text style={styles.eventDate}>{formatDate(event.date)} • {formatTime(event.time)}</Text>
        <Text style={styles.eventTitle}>{event.title}</Text>
        <Text style={styles.eventDesc}>{event.description}</Text>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.rsvpButton}
            onPress={() => navigation.navigate('ReservationScreen', { eventId: event.id })}
          >
            <Text style={styles.rsvpButtonText}>RSVP NOW</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.reminderButton, reminderSet && styles.reminderButtonActive]}
            onPress={handleSetReminder}
          >
            <Ionicons
              name={reminderSet ? "notifications" : "notifications-outline"}
              size={20}
              color={reminderSet ? "white" : "#6B46C1"}
            />
            <Text style={[styles.reminderButtonText, reminderSet && styles.reminderButtonTextActive]}>
              {reminderSet ? "Reminder Set" : "Set Reminder"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.detailsCard}>
        <Text style={styles.sectionTitle}>Event Details</Text>
        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="calendar-outline" size={20} color="#6B46C1" />
          </View>
          <View style={styles.detailTextContainer}>
            <Text style={styles.detailLabel}>Date & Time</Text>
            <Text style={styles.detailValue}>{formatDate(event.date)} at {formatTime(event.time)}</Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailIconContainer}>
            <Ionicons name="location-outline" size={20} color="#6B46C1" />
          </View>
          <View style={styles.detailTextContainer}>
            <Text style={styles.detailLabel}>Location</Text>
            <Text style={styles.detailValue}>{event.location}</Text>
          </View>
        </View>

        {event.coverCharge && (
          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <Ionicons name="cash-outline" size={20} color="#6B46C1" />
            </View>
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Cover Charge</Text>
              <Text style={styles.detailValue}>{event.coverCharge}</Text>
            </View>
          </View>
        )}

        {event.specialMenu && (
          <View style={styles.detailRow}>
            <View style={styles.detailIconContainer}>
              <Ionicons name="restaurant-outline" size={20} color="#6B46C1" />
            </View>
            <View style={styles.detailTextContainer}>
              <Text style={styles.detailLabel}>Special Menu</Text>
              <Text style={styles.detailValue}>{event.specialMenu}</Text>
            </View>
          </View>
        )}
      </View>

      {event.performer && (
        <View style={styles.detailsCard}>
          <Text style={styles.sectionTitle}>About the Artist</Text>
          <Text style={styles.performerText}>{event.performer.description}</Text>
        </View>
      )}

      <View style={styles.detailsCard}>
        <Text style={styles.sectionTitle}>Reserve a Table</Text>
        <Text style={styles.reservationText}>Tables are filling up fast! Reserve yours now to guarantee seating.</Text>
        <TouchableOpacity
          style={styles.reserveButton}
          onPress={() => navigation.navigate('ReservationScreen', { eventId: event.id })}
        >
          <Ionicons name="calendar-outline" size={20} color="white" style={styles.buttonIcon} />
          <Text style={styles.reserveButtonText}>Reserve for this Event</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F7FAFC',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#F7FAFC',
  },
  errorText: {
    fontSize: 16,
    color: '#E53E3E',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#6B46C1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  imageContainer: {
    width: '100%',
    height: 200,
    backgroundColor: '#E2E8F0',
  },
  eventImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#EDF2F7',
  },
  eventCard: {
    backgroundColor: '#6B46C1',
    padding: 20,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    marginBottom: 16,
  },
  eventDate: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 8,
  },
  eventTitle: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  eventDesc: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    marginBottom: 16,
    lineHeight: 24,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rsvpButton: {
    backgroundColor: 'white',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  rsvpButtonText: {
    color: '#6B46C1',
    fontWeight: 'bold',
    fontSize: 14,
  },
  reminderButton: {
    backgroundColor: 'transparent',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginLeft: 8,
  },
  reminderButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  reminderButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 14,
  },
  reminderButtonTextActive: {
    fontWeight: 'bold',
  },
  detailsCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D3748',
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  detailIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EBF4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  detailTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#2D3748',
  },
  performerText: {
    fontSize: 16,
    color: '#4A5568',
    lineHeight: 24,
  },
  reservationText: {
    fontSize: 16,
    color: '#4A5568',
    marginBottom: 16,
    lineHeight: 24,
  },
  reserveButton: {
    backgroundColor: '#6B46C1',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  reserveButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default EventDetailScreen;
