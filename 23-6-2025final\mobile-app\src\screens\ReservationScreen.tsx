import React, { useState, useEffect, useContext } from 'react';
import { View, StyleSheet, ScrollView, Alert, TouchableOpacity } from 'react-native';
import { Text, Button, Card, Title, Paragraph, ActivityIndicator, TextInput, Divider } from 'react-native-paper';
import { Calendar } from 'react-native-calendars';
import { Ionicons } from '@expo/vector-icons';
import { AuthContext } from '../context/AuthContext';
import { reservationApi } from '../api/api';

const ReservationScreen = () => {
  const { user } = useContext(AuthContext);
  const [selectedDate, setSelectedDate] = useState('');
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedTime, setSelectedTime] = useState('');
  const [partySize, setPartySize] = useState('2');
  const [specialRequests, setSpecialRequests] = useState('');
  const [loading, setLoading] = useState(false);
  const [userReservations, setUserReservations] = useState([]);
  const [showReservationForm, setShowReservationForm] = useState(false);

  useEffect(() => {
    if (user) {
      loadUserReservations();
    }
  }, [user]);

  useEffect(() => {
    if (selectedDate) {
      loadAvailability();
    }
  }, [selectedDate]);

  const loadUserReservations = async () => {
    try {
      setLoading(true);
      const response = await reservationApi.getUserReservations();
      setUserReservations(response.data.reservations);
    } catch (error) {
      console.error('Error loading user reservations:', error);
      Alert.alert('Error', 'Failed to load your reservations');
    } finally {
      setLoading(false);
    }
  };

  const loadAvailability = async () => {
    try {
      setLoading(true);
      const response = await reservationApi.getAvailability(selectedDate);
      setAvailableSlots(response.data.availableSlots);
    } catch (error) {
      console.error('Error loading availability:', error);
      Alert.alert('Error', 'Failed to load available time slots');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReservation = async () => {
    if (!selectedDate || !selectedTime || !partySize) {
      Alert.alert('Missing Information', 'Please select a date, time, and party size');
      return;
    }

    try {
      setLoading(true);
      const response = await reservationApi.createReservation({
        date: selectedDate,
        time: selectedTime,
        partySize: parseInt(partySize),
        specialRequests,
      });

      if (response.data.success) {
        Alert.alert('Success', 'Your reservation has been created!');
        setSelectedDate('');
        setSelectedTime('');
        setPartySize('2');
        setSpecialRequests('');
        setShowReservationForm(false);
        loadUserReservations();
      } else {
        Alert.alert('Error', response.data.message || 'Failed to create reservation');
      }
    } catch (error) {
      console.error('Error creating reservation:', error);
      Alert.alert('Error', 'Failed to create reservation');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelReservation = async (reservationId) => {
    Alert.alert(
      'Cancel Reservation',
      'Are you sure you want to cancel this reservation?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes',
          onPress: async () => {
            try {
              setLoading(true);
              const response = await reservationApi.cancelReservation(reservationId);
              
              if (response.data.success) {
                Alert.alert('Success', 'Your reservation has been cancelled');
                loadUserReservations();
              } else {
                Alert.alert('Error', response.data.message || 'Failed to cancel reservation');
              }
            } catch (error) {
              console.error('Error cancelling reservation:', error);
              Alert.alert('Error', 'Failed to cancel reservation');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString) => {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0];

  return (
    <ScrollView style={styles.container}>
      {!user ? (
        <Card style={styles.loginCard}>
          <Card.Content>
            <Title>Login Required</Title>
            <Paragraph>Please login to make reservations</Paragraph>
          </Card.Content>
          <Card.Actions>
            <Button mode="contained" onPress={() => navigation.navigate('Login')}>
              Login
            </Button>
          </Card.Actions>
        </Card>
      ) : (
        <>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Reservations</Text>
          </View>

          {userReservations.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Your Upcoming Reservations</Text>
              {userReservations.map((reservation) => (
                <Card key={reservation.id} style={styles.reservationCard}>
                  <Card.Content>
                    <View style={styles.reservationHeader}>
                      <View>
                        <Title style={styles.reservationTitle}>
                          {formatDate(reservation.date)}
                        </Title>
                        <Paragraph style={styles.reservationTime}>
                          {reservation.time} • Party of {reservation.partySize}
                        </Paragraph>
                      </View>
                      <TouchableOpacity
                        onPress={() => handleCancelReservation(reservation.id)}
                        style={styles.cancelButton}
                      >
                        <Ionicons name="close-circle-outline" size={24} color="#E53E3E" />
                      </TouchableOpacity>
                    </View>
                    {reservation.specialRequests && (
                      <Paragraph style={styles.specialRequests}>
                        {reservation.specialRequests}
                      </Paragraph>
                    )}
                  </Card.Content>
                </Card>
              ))}
            </View>
          )}

          {!showReservationForm ? (
            <Button
              mode="contained"
              onPress={() => setShowReservationForm(true)}
              style={styles.newReservationButton}
              icon="plus"
            >
              Make a New Reservation
            </Button>
          ) : (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Make a Reservation</Text>
                <TouchableOpacity onPress={() => setShowReservationForm(false)}>
                  <Ionicons name="close" size={24} color="#4A5568" />
                </TouchableOpacity>
              </View>

              <Text style={styles.label}>Select a Date</Text>
              <Calendar
                minDate={today}
                onDayPress={(day) => setSelectedDate(day.dateString)}
                markedDates={{
                  [selectedDate]: { selected: true, selectedColor: '#6B46C1' },
                }}
                theme={{
                  todayTextColor: '#6B46C1',
                  selectedDayBackgroundColor: '#6B46C1',
                  arrowColor: '#6B46C1',
                }}
                style={styles.calendar}
              />

              {selectedDate && (
                <>
                  <Text style={styles.label}>Available Times</Text>
                  {loading ? (
                    <ActivityIndicator size="small" color="#6B46C1" />
                  ) : (
                    <View style={styles.timeSlots}>
                      {availableSlots.length > 0 ? (
                        availableSlots.map((slot) => (
                          <TouchableOpacity
                            key={slot}
                            style={[
                              styles.timeSlot,
                              selectedTime === slot && styles.selectedTimeSlot,
                            ]}
                            onPress={() => setSelectedTime(slot)}
                          >
                            <Text
                              style={[
                                styles.timeSlotText,
                                selectedTime === slot && styles.selectedTimeSlotText,
                              ]}
                            >
                              {slot}
                            </Text>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noTimesText}>No available times for this date</Text>
                      )}
                    </View>
                  )}

                  <Text style={styles.label}>Party Size</Text>
                  <TextInput
                    mode="outlined"
                    keyboardType="number-pad"
                    value={partySize}
                    onChangeText={setPartySize}
                    style={styles.input}
                  />

                  <Text style={styles.label}>Special Requests (Optional)</Text>
                  <TextInput
                    mode="outlined"
                    value={specialRequests}
                    onChangeText={setSpecialRequests}
                    multiline
                    numberOfLines={3}
                    style={styles.input}
                  />

                  <Button
                    mode="contained"
                    onPress={handleCreateReservation}
                    style={styles.reserveButton}
                    loading={loading}
                    disabled={!selectedDate || !selectedTime || !partySize || loading}
                  >
                    Reserve Table
                  </Button>
                </>
              )}
            </View>
          )}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  header: {
    padding: 20,
    backgroundColor: '#6B46C1',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  section: {
    padding: 16,
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D3748',
    marginBottom: 16,
  },
  loginCard: {
    margin: 16,
  },
  reservationCard: {
    marginBottom: 16,
    elevation: 2,
  },
  reservationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  reservationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  reservationTime: {
    color: '#4A5568',
  },
  specialRequests: {
    marginTop: 8,
    fontStyle: 'italic',
    color: '#718096',
  },
  cancelButton: {
    padding: 4,
  },
  newReservationButton: {
    margin: 16,
    backgroundColor: '#6B46C1',
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4A5568',
    marginTop: 16,
    marginBottom: 8,
  },
  calendar: {
    borderRadius: 8,
    elevation: 2,
    backgroundColor: 'white',
  },
  timeSlots: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 8,
  },
  timeSlot: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    margin: 4,
    minWidth: 80,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  selectedTimeSlot: {
    backgroundColor: '#6B46C1',
    borderColor: '#6B46C1',
  },
  timeSlotText: {
    color: '#4A5568',
    fontWeight: '500',
  },
  selectedTimeSlotText: {
    color: 'white',
  },
  noTimesText: {
    color: '#718096',
    fontStyle: 'italic',
    padding: 8,
  },
  input: {
    backgroundColor: 'white',
    marginBottom: 16,
  },
  reserveButton: {
    marginTop: 16,
    backgroundColor: '#6B46C1',
  },
});

export default ReservationScreen;
