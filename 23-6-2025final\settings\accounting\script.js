document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const backBtn = document.getElementById('back-btn');
    const exportBtn = document.getElementById('export-btn');
    const reconcileBtn = document.getElementById('reconcile-btn');
    const expensesBtn = document.getElementById('expenses-btn');
    const viewAllBtn = document.getElementById('view-all-btn');

    // Summary elements
    const todaySales = document.getElementById('today-sales');
    const monthSales = document.getElementById('month-sales');
    const outstandingAmount = document.getElementById('outstanding-amount');

    // Transactions table
    const transactionsBody = document.getElementById('transactions-body');

    // Event Listeners
    backBtn.addEventListener('click', function() {
        window.location.href = '../index.html';
    });

    exportBtn.addEventListener('click', function() {
        exportFinancialData();
    });

    reconcileBtn.addEventListener('click', function() {
        alert('Account reconciliation feature coming soon!');
    });

    expensesBtn.addEventListener('click', function() {
        showExpenseModal();
    });

    viewAllBtn.addEventListener('click', function() {
        showTransactionHistoryModal();
    });

    // Load financial data
    loadFinancialData();

    // Load recent transactions
    loadRecentTransactions();

    /**
     * Load financial summary data
     */
    async function loadFinancialData() {
        try {
            // Show loading state
            todaySales.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            monthSales.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            outstandingAmount.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Fetch financial summary from API
            const response = await fetch('http://localhost:5000/api/financial/summary');

            if (!response.ok) {
                throw new Error('Failed to fetch financial data');
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to load financial data');
            }

            // Format currency values
            const formatCurrency = (value) => {
                return '$' + parseFloat(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            };

            // Update summary elements with real data
            todaySales.textContent = formatCurrency(data.summary.todaySales);
            monthSales.textContent = formatCurrency(data.summary.monthSales);
            outstandingAmount.textContent = formatCurrency(data.summary.outstandingAmount);

            // If we have recent transactions, render them
            if (data.recentTransactions && data.recentTransactions.length > 0) {
                renderTransactions(data.recentTransactions);
            }

        } catch (error) {
            console.error('Error loading financial data:', error);
            todaySales.textContent = 'Error';
            monthSales.textContent = 'Error';
            outstandingAmount.textContent = 'Error';
            alert('Failed to load financial data: ' + error.message);
        }
    }

    /**
     * Load recent transactions
     */
    async function loadRecentTransactions() {
        try {
            // Show loading state
            transactionsBody.innerHTML = `
                <tr>
                    <td colspan="4" class="loading"><i class="fas fa-spinner fa-spin"></i> Loading transactions...</td>
                </tr>
            `;

            // Fetch recent transactions from API
            const response = await fetch('http://localhost:5000/api/financial/summary');

            if (!response.ok) {
                throw new Error('Failed to fetch transaction data');
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to load transaction data');
            }

            // Render transactions
            if (data.recentTransactions && data.recentTransactions.length > 0) {
                renderTransactions(data.recentTransactions);
            } else {
                transactionsBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="empty">No recent transactions found.</td>
                    </tr>
                `;
            }

        } catch (error) {
            console.error('Error loading transactions:', error);
            transactionsBody.innerHTML = `
                <tr>
                    <td colspan="4" class="error">Failed to load transactions: ${error.message}</td>
                </tr>
            `;
        }
    }

    /**
     * Render transactions in the table
     */
    function renderTransactions(transactions) {
        if (!transactions || transactions.length === 0) {
            transactionsBody.innerHTML = `
                <tr>
                    <td colspan="4" class="empty">No transactions found.</td>
                </tr>
            `;
            return;
        }

        transactionsBody.innerHTML = transactions.map(transaction => {
            const date = new Date(transaction.date);
            const formattedDate = date.toLocaleDateString();
            const amountClass = transaction.type === 'Income' ? 'income' : 'expense';
            const amountPrefix = transaction.type === 'Income' ? '+' : '-';

            return `
                <tr>
                    <td>${formattedDate}</td>
                    <td>${transaction.description}</td>
                    <td class="${amountClass}">${amountPrefix}$${transaction.amount.toFixed(2)}</td>
                    <td>${transaction.type}</td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Export financial data
     */
    async function exportFinancialData() {
        try {
            // Get date range for export
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            const startDate = firstDayOfMonth.toISOString().split('T')[0];
            const endDate = lastDayOfMonth.toISOString().split('T')[0];

            // Fetch orders data
            const ordersResponse = await fetch(`http://localhost:5000/api/orders/report?startDate=${startDate}&endDate=${endDate}`);
            if (!ordersResponse.ok) {
                throw new Error('Failed to fetch orders data');
            }

            const ordersData = await ordersResponse.json();

            // Fetch expenses data
            const expensesResponse = await fetch(`http://localhost:5000/api/expenses?startDate=${startDate}&endDate=${endDate}`);
            if (!expensesResponse.ok) {
                throw new Error('Failed to fetch expenses data');
            }

            const expensesData = await expensesResponse.json();

            // Prepare CSV data
            let csvContent = 'Date,Type,Description,Amount\n';

            // Add orders data
            if (ordersData.success && ordersData.orders) {
                ordersData.orders.forEach(order => {
                    if (order.paid) {
                        let items;
                        try {
                            items = JSON.parse(order.items);
                        } catch (e) {
                            items = [];
                        }

                        const subtotal = items.reduce((sum, item) => {
                            const price = parseFloat(item.price) || 0;
                            const quantity = parseInt(item.quantity) || 1;
                            return sum + (price * quantity);
                        }, 0);

                        const date = new Date(order.timestamp).toISOString().split('T')[0];
                        csvContent += `${date},Income,Table ${order.tableNumber} - ${order.customerName},${subtotal.toFixed(2)}\n`;
                    }
                });
            }

            // Add expenses data
            if (expensesData.success && expensesData.expenses) {
                expensesData.expenses.forEach(expense => {
                    csvContent += `${expense.date},Expense,${expense.description},${expense.amount.toFixed(2)}\n`;
                });
            }

            // Create and download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', `financial_report_${startDate}_to_${endDate}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('Error exporting financial data:', error);
            alert('Failed to export financial data: ' + error.message);
        }
    }

    /**
     * Show expense recording modal
     */
    async function showExpenseModal() {
        // Create modal if it doesn't exist
        let modal = document.getElementById('expense-modal');

        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'expense-modal';
            modal.className = 'modal';

            // Fetch expense categories
            let categoriesHtml = '';
            try {
                const response = await fetch('http://localhost:5000/api/expenses/categories');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.categories) {
                        categoriesHtml = data.categories.map(category =>
                            `<option value="${category}">${category}</option>`
                        ).join('');
                    }
                }
            } catch (error) {
                console.error('Error fetching expense categories:', error);
                categoriesHtml = `
                    <option value="Supplies">Supplies</option>
                    <option value="Utilities">Utilities</option>
                    <option value="Rent">Rent</option>
                    <option value="Salaries">Salaries</option>
                    <option value="Equipment">Equipment</option>
                    <option value="Marketing">Marketing</option>
                    <option value="Maintenance">Maintenance</option>
                    <option value="Food">Food</option>
                    <option value="Beverages">Beverages</option>
                    <option value="Tobacco">Tobacco</option>
                    <option value="Other">Other</option>
                `;
            }

            // Set modal content
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Record Expense</h2>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="expense-form">
                            <div class="form-group">
                                <label for="expense-description">Description</label>
                                <input type="text" id="expense-description" required placeholder="e.g., Tobacco supplies">
                            </div>
                            <div class="form-group">
                                <label for="expense-amount">Amount</label>
                                <input type="number" id="expense-amount" required min="0.01" step="0.01" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label for="expense-category">Category</label>
                                <select id="expense-category" required>
                                    <option value="">Select a category</option>
                                    ${categoriesHtml}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="expense-date">Date</label>
                                <input type="date" id="expense-date" required>
                            </div>
                            <div class="form-group">
                                <label for="expense-notes">Notes (Optional)</label>
                                <textarea id="expense-notes" rows="3" placeholder="Additional details about this expense"></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="cancel-expense" class="cancel-btn">Cancel</button>
                                <button type="submit" id="save-expense" class="save-btn">Save Expense</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Set today's date as default
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('expense-date').value = today;

            // Add event listeners
            document.querySelector('.close-modal').addEventListener('click', () => {
                modal.style.display = 'none';
            });

            document.getElementById('cancel-expense').addEventListener('click', () => {
                modal.style.display = 'none';
            });

            document.getElementById('expense-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await saveExpense();
            });
        }

        // Show the modal
        modal.style.display = 'block';
    }

    /**
     * Save expense to the server
     */
    async function saveExpense() {
        try {
            const description = document.getElementById('expense-description').value;
            const amount = parseFloat(document.getElementById('expense-amount').value);
            const category = document.getElementById('expense-category').value;
            const date = document.getElementById('expense-date').value;
            const notes = document.getElementById('expense-notes').value;

            // Validate inputs
            if (!description || !amount || !category || !date) {
                alert('Please fill in all required fields');
                return;
            }

            // Prepare expense data
            const expenseData = {
                description,
                amount,
                category,
                date,
                notes
            };

            // Send to server
            const response = await fetch('http://localhost:5000/api/expenses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(expenseData)
            });

            if (!response.ok) {
                throw new Error('Failed to save expense');
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Failed to save expense');
            }

            // Close modal
            document.getElementById('expense-modal').style.display = 'none';

            // Reset form
            document.getElementById('expense-form').reset();

            // Set today's date as default again
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('expense-date').value = today;

            // Refresh data
            loadFinancialData();
            loadRecentTransactions();

            // Show success message
            alert('Expense recorded successfully!');

        } catch (error) {
            console.error('Error saving expense:', error);
            alert('Failed to save expense: ' + error.message);
        }
    }

    /**
     * Show transaction history modal
     */
    async function showTransactionHistoryModal() {
        // Create modal if it doesn't exist
        let modal = document.getElementById('transaction-history-modal');

        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'transaction-history-modal';
            modal.className = 'modal transaction-modal';

            // Set modal content
            modal.innerHTML = `
                <div class="modal-content large-modal">
                    <div class="modal-header">
                        <h2>Transaction History</h2>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="filter-controls">
                            <div class="date-range">
                                <label for="start-date">From:</label>
                                <input type="date" id="start-date">
                                <label for="end-date">To:</label>
                                <input type="date" id="end-date">
                            </div>
                            <div class="type-filter">
                                <label>
                                    <input type="checkbox" id="filter-income" checked>
                                    Income
                                </label>
                                <label>
                                    <input type="checkbox" id="filter-expense" checked>
                                    Expenses
                                </label>
                            </div>
                            <button id="apply-filters" class="filter-btn">Apply Filters</button>
                        </div>

                        <div class="transaction-table-container">
                            <table class="transactions-table full-width">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Type</th>
                                    </tr>
                                </thead>
                                <tbody id="history-transactions-body">
                                    <tr>
                                        <td colspan="4" class="loading"><i class="fas fa-spinner fa-spin"></i> Loading transactions...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination">
                            <button id="prev-page" class="pagination-btn" disabled><i class="fas fa-chevron-left"></i> Previous</button>
                            <span id="page-info">Page 1 of 1</span>
                            <button id="next-page" class="pagination-btn" disabled>Next <i class="fas fa-chevron-right"></i></button>
                        </div>

                        <div class="export-section">
                            <button id="export-history" class="export-btn"><i class="fas fa-file-export"></i> Export to CSV</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Set default date range (last 30 days)
            const today = new Date();
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(today.getDate() - 30);

            document.getElementById('end-date').value = today.toISOString().split('T')[0];
            document.getElementById('start-date').value = thirtyDaysAgo.toISOString().split('T')[0];

            // Add event listeners
            document.querySelector('#transaction-history-modal .close-modal').addEventListener('click', () => {
                modal.style.display = 'none';
            });

            document.getElementById('apply-filters').addEventListener('click', () => {
                loadTransactionHistory(1);
            });

            document.getElementById('prev-page').addEventListener('click', () => {
                const currentPage = parseInt(document.getElementById('page-info').textContent.split(' ')[1]);
                if (currentPage > 1) {
                    loadTransactionHistory(currentPage - 1);
                }
            });

            document.getElementById('next-page').addEventListener('click', () => {
                const pageInfo = document.getElementById('page-info').textContent;
                const [currentPage, totalPages] = pageInfo.split(' of ');
                const current = parseInt(currentPage.split(' ')[1]);
                const total = parseInt(totalPages);

                if (current < total) {
                    loadTransactionHistory(current + 1);
                }
            });

            document.getElementById('export-history').addEventListener('click', () => {
                exportTransactionHistory();
            });
        }

        // Show the modal
        modal.style.display = 'block';

        // Load transaction history
        loadTransactionHistory(1);
    }

    /**
     * Load transaction history with pagination
     */
    async function loadTransactionHistory(page = 1, pageSize = 10) {
        try {
            const historyBody = document.getElementById('history-transactions-body');
            const prevPageBtn = document.getElementById('prev-page');
            const nextPageBtn = document.getElementById('next-page');
            const pageInfo = document.getElementById('page-info');

            // Show loading state
            historyBody.innerHTML = `
                <tr>
                    <td colspan="4" class="loading"><i class="fas fa-spinner fa-spin"></i> Loading transactions...</td>
                </tr>
            `;

            // Get filter values
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            const includeIncome = document.getElementById('filter-income').checked;
            const includeExpenses = document.getElementById('filter-expense').checked;

            // Validate filters
            if (!startDate || !endDate) {
                historyBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="error">Please select a valid date range</td>
                    </tr>
                `;
                return;
            }

            if (!includeIncome && !includeExpenses) {
                historyBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="error">Please select at least one transaction type</td>
                    </tr>
                `;
                return;
            }

            // Fetch orders (income) if needed
            let incomeTransactions = [];
            if (includeIncome) {
                try {
                    const ordersResponse = await fetch(`http://localhost:5000/api/orders/report?startDate=${startDate}&endDate=${endDate}`);
                    if (ordersResponse.ok) {
                        const ordersData = await ordersResponse.json();
                        if (ordersData.success && ordersData.orders) {
                            // Format orders as transactions
                            incomeTransactions = ordersData.orders
                                .filter(order => order.paid)
                                .map(order => {
                                    let items;
                                    try {
                                        items = JSON.parse(order.items);
                                    } catch (e) {
                                        items = [];
                                    }

                                    const subtotal = items.reduce((sum, item) => {
                                        const price = parseFloat(item.price) || 0;
                                        const quantity = parseInt(item.quantity) || 1;
                                        return sum + (price * quantity);
                                    }, 0);

                                    return {
                                        date: new Date(order.timestamp).toISOString().split('T')[0],
                                        timestamp: order.timestamp,
                                        description: `Table ${order.tableNumber} - ${order.customerName}`,
                                        amount: subtotal,
                                        type: 'Income'
                                    };
                                });
                        }
                    }
                } catch (error) {
                    console.error('Error fetching orders:', error);
                }
            }

            // Fetch expenses if needed
            let expenseTransactions = [];
            if (includeExpenses) {
                try {
                    const expensesResponse = await fetch(`http://localhost:5000/api/expenses?startDate=${startDate}&endDate=${endDate}`);
                    if (expensesResponse.ok) {
                        const expensesData = await expensesResponse.json();
                        if (expensesData.success && expensesData.expenses) {
                            // Format expenses as transactions
                            expenseTransactions = expensesData.expenses.map(expense => ({
                                date: expense.date,
                                timestamp: expense.created_at,
                                description: expense.description,
                                amount: expense.amount,
                                type: 'Expense'
                            }));
                        }
                    }
                } catch (error) {
                    console.error('Error fetching expenses:', error);
                }
            }

            // Combine and sort transactions
            const allTransactions = [...incomeTransactions, ...expenseTransactions]
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // Calculate pagination
            const totalTransactions = allTransactions.length;
            const totalPages = Math.ceil(totalTransactions / pageSize);
            const startIndex = (page - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, totalTransactions);
            const currentPageTransactions = allTransactions.slice(startIndex, endIndex);

            // Update pagination controls
            pageInfo.textContent = `Page ${page} of ${totalPages || 1}`;
            prevPageBtn.disabled = page <= 1;
            nextPageBtn.disabled = page >= totalPages || totalPages === 0;

            // Render transactions
            if (currentPageTransactions.length === 0) {
                historyBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="empty">No transactions found for the selected criteria</td>
                    </tr>
                `;
                return;
            }

            historyBody.innerHTML = currentPageTransactions.map(transaction => {
                const date = new Date(transaction.date);
                const formattedDate = date.toLocaleDateString();
                const amountClass = transaction.type === 'Income' ? 'income' : 'expense';
                const amountPrefix = transaction.type === 'Income' ? '+' : '-';

                return `
                    <tr>
                        <td>${formattedDate}</td>
                        <td>${transaction.description}</td>
                        <td class="${amountClass}">${amountPrefix}$${transaction.amount.toFixed(2)}</td>
                        <td>${transaction.type}</td>
                    </tr>
                `;
            }).join('');

        } catch (error) {
            console.error('Error loading transaction history:', error);
            document.getElementById('history-transactions-body').innerHTML = `
                <tr>
                    <td colspan="4" class="error">Failed to load transactions: ${error.message}</td>
                </tr>
            `;
        }
    }

    /**
     * Export transaction history to CSV
     */
    async function exportTransactionHistory() {
        try {
            // Get filter values
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            const includeIncome = document.getElementById('filter-income').checked;
            const includeExpenses = document.getElementById('filter-expense').checked;

            // Validate filters
            if (!startDate || !endDate) {
                alert('Please select a valid date range');
                return;
            }

            if (!includeIncome && !includeExpenses) {
                alert('Please select at least one transaction type');
                return;
            }

            // Show loading state
            const exportBtn = document.getElementById('export-history');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
            exportBtn.disabled = true;

            // Fetch all transactions without pagination
            let allTransactions = [];

            // Fetch orders (income) if needed
            if (includeIncome) {
                try {
                    const ordersResponse = await fetch(`http://localhost:5000/api/orders/report?startDate=${startDate}&endDate=${endDate}`);
                    if (ordersResponse.ok) {
                        const ordersData = await ordersResponse.json();
                        if (ordersData.success && ordersData.orders) {
                            // Format orders as transactions
                            const incomeTransactions = ordersData.orders
                                .filter(order => order.paid)
                                .map(order => {
                                    let items;
                                    try {
                                        items = JSON.parse(order.items);
                                    } catch (e) {
                                        items = [];
                                    }

                                    const subtotal = items.reduce((sum, item) => {
                                        const price = parseFloat(item.price) || 0;
                                        const quantity = parseInt(item.quantity) || 1;
                                        return sum + (price * quantity);
                                    }, 0);

                                    return {
                                        date: new Date(order.timestamp).toISOString().split('T')[0],
                                        description: `Table ${order.tableNumber} - ${order.customerName}`,
                                        amount: subtotal,
                                        type: 'Income'
                                    };
                                });

                            allTransactions = [...allTransactions, ...incomeTransactions];
                        }
                    }
                } catch (error) {
                    console.error('Error fetching orders for export:', error);
                }
            }

            // Fetch expenses if needed
            if (includeExpenses) {
                try {
                    const expensesResponse = await fetch(`http://localhost:5000/api/expenses?startDate=${startDate}&endDate=${endDate}`);
                    if (expensesResponse.ok) {
                        const expensesData = await expensesResponse.json();
                        if (expensesData.success && expensesData.expenses) {
                            // Format expenses as transactions
                            const expenseTransactions = expensesData.expenses.map(expense => ({
                                date: expense.date,
                                description: expense.description,
                                amount: expense.amount,
                                type: 'Expense'
                            }));

                            allTransactions = [...allTransactions, ...expenseTransactions];
                        }
                    }
                } catch (error) {
                    console.error('Error fetching expenses for export:', error);
                }
            }

            // Sort transactions by date (newest first)
            allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

            // Create CSV content
            let csvContent = 'Date,Description,Amount,Type\n';

            allTransactions.forEach(transaction => {
                const amountPrefix = transaction.type === 'Income' ? '' : '-';
                csvContent += `${transaction.date},"${transaction.description.replace(/"/g, '""')}",${amountPrefix}${transaction.amount.toFixed(2)},${transaction.type}\n`;
            });

            // Create and download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', `transaction_history_${startDate}_to_${endDate}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Restore button state
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;

        } catch (error) {
            console.error('Error exporting transaction history:', error);
            alert('Failed to export transaction history: ' + error.message);

            // Restore button state
            const exportBtn = document.getElementById('export-history');
            exportBtn.innerHTML = '<i class="fas fa-file-export"></i> Export to CSV';
            exportBtn.disabled = false;
        }
    }

    // Apply saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }
});
