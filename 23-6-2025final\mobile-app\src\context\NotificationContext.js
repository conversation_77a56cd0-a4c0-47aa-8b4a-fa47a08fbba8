import React, { createContext, useState, useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import NotificationService from '../utils/NotificationService';
import { api, notificationApi, eventsApi } from '../api/api';

export const NotificationContext = createContext();

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [expoPushToken, setExpoPushToken] = useState('');
  const [loading, setLoading] = useState(true);

  const notificationListener = useRef();
  const responseListener = useRef();

  useEffect(() => {
    registerForPushNotifications();

    // This listener is fired whenever a notification is received while the app is foregrounded
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      const newNotification = formatNotification(notification);
      setNotifications(prev => [newNotification, ...prev]);
      setUnreadCount(prev => prev + 1);
    });

    // This listener is fired whenever a user taps on or interacts with a notification
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      const notificationData = response.notification.request.content.data;
      handleNotificationResponse(notificationData);
    });

    // Load notifications from API
    fetchNotifications();

    return () => {
      Notifications.removeNotificationSubscription(notificationListener.current);
      Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []);

  const registerForPushNotifications = async () => {
    try {
      const permissionGranted = await NotificationService.requestPermissions();

      if (permissionGranted) {
        const token = await NotificationService.getExpoPushToken();

        if (token) {
          setExpoPushToken(token);

          // Send token to your backend
          try {
            await notificationApi.registerPushToken(token);
          } catch (error) {
            console.error('Error saving push token to server:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error registering for push notifications:', error);
    }
  };

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await notificationApi.getNotifications();

      if (response.data) {
        setNotifications(response.data);
        const unread = response.data.filter(notification => !notification.read).length;
        setUnreadCount(unread);

        // Update badge count
        await NotificationService.setBadgeCount(unread);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNotification = (notification) => {
    const data = notification.request?.content?.data || {};
    return {
      id: notification.request?.identifier || Date.now().toString(),
      title: notification.request?.content?.title || 'New Notification',
      body: notification.request?.content?.body || '',
      data,
      timestamp: new Date().toISOString(),
      read: false,
    };
  };

  const handleNotificationResponse = (data) => {
    // Handle different notification types
    if (data && data.type) {
      // This would be handled by your navigation logic
      console.log('Notification interaction:', data);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      // Update on server
      await notificationApi.markAsRead(notificationId);

      // Update local state
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );

      const newUnreadCount = unreadCount - 1;
      setUnreadCount(newUnreadCount);

      // Update badge count
      await NotificationService.setBadgeCount(newUnreadCount);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      // Update on server
      await notificationApi.markAllAsRead();

      // Update local state
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      setUnreadCount(0);

      // Update badge count
      await NotificationService.setBadgeCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const scheduleEventReminder = async (event, reminderHours = 24) => {
    try {
      const notificationId = await NotificationService.scheduleEventReminder(event, reminderHours);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling event reminder:', error);
      return null;
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        loading,
        expoPushToken,
        markAsRead,
        markAllAsRead,
        refreshNotifications: fetchNotifications,
        scheduleEventReminder,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
