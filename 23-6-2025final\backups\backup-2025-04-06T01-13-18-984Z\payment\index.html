<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="payment-container">
            <!-- Left Side: Bill <PERSON>ails -->
            <div class="bill-details">
                <h2>Bill <PERSON>ails</h2>
                <div class="customer-info">
                    <p id="table-number">Table: </p>
                    <p id="customer-name">Customer: </p>
                </div>
                <div class="items-list">
                    <h3>Items</h3>
                    <ul id="items-list"></ul>
                </div>
                <div class="bill-summary">
                    <div class="summary-row">
                        <span>Subtotal:</span>
                        <span id="subtotal">$0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>GST (5%):</span>
                        <span id="gst">$0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>PST (7%):</span>
                        <span id="pst">$0.00</span>
                    </div>
                    <div class="summary-row total">
                        <span>Total:</span>
                        <span id="total">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Right Side: Payment Options -->
            <div class="payment-options">
                <h2>Payment Options</h2>
                <div class="payment-buttons">
                    <div class="payment-btn-row">
                        <button id="pay-cash" class="payment-btn">Pay Cash</button>
                        <button id="pay-card" class="payment-btn">Pay Card</button>
                    </div>
                    <div class="payment-btn-row">
                        <button id="print-receipt" class="payment-btn">Print Receipt</button>
                        <button id="whatsapp-receipt" class="payment-btn">WhatsApp Receipt</button>
                    </div>
                    <div class="payment-btn-row">
                        <button id="edit-bill" class="payment-btn">Edit</button>
                        <button id="mis-report" class="payment-btn">MIS</button>
                    </div>
                    <div class="payment-btn-row">
                        <button id="backup-button" class="payment-btn">Backup</button>
                        <button id="back-to-pos" class="payment-btn">Back to POS</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- WhatsApp Modal -->
    <div id="whatsapp-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Send Receipt via WhatsApp</h2>
            <div class="whatsapp-input">
                <input type="text" id="whatsapp-number" placeholder="Enter phone number" readonly>
                <div class="keypad">
                    <button class="key">1</button>
                    <button class="key">2</button>
                    <button class="key">3</button>
                    <button class="key">4</button>
                    <button class="key">5</button>
                    <button class="key">6</button>
                    <button class="key">7</button>
                    <button class="key">8</button>
                    <button class="key">9</button>
                    <button class="key">*</button>
                    <button class="key">0</button>
                    <button class="key">#</button>
                </div>
                <button id="send-whatsapp" class="action-btn">Send</button>
            </div>
        </div>
    </div>

    <!-- Custom Amount Modal -->
    <div id="custom-amount-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Enter Custom Amount</h3>
            <div class="amount-input">
                <input type="number" id="custom-amount" placeholder="Enter amount" step="0.01" min="0">
            </div>
            <div class="keypad">
                <button class="key">1</button>
                <button class="key">2</button>
                <button class="key">3</button>
                <button class="key">4</button>
                <button class="key">5</button>
                <button class="key">6</button>
                <button class="key">7</button>
                <button class="key">8</button>
                <button class="key">9</button>
                <button class="key">.</button>
                <button class="key">0</button>
                <button class="key">C</button>
            </div>
            <button id="apply-custom-amount" class="send-btn">Apply Amount</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 