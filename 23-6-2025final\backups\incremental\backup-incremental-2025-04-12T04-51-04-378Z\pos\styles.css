/* General Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Icon Styles */
.fas, .far, .fab, .fa {
    margin-right: 5px;
}

.container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 10px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 15px;
    box-sizing: border-box; /* Ensure padding is included in width */
}

/* Entry Fee Toggle */
.entry-fee-toggle {
    margin-bottom: 20px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.entry-fee-toggle label {
    font-weight: bold;
    margin-right: 10px;
}

.entry-fee-toggle select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
}

/* Refresh Button */
.refresh-btn {
    position: static;
    padding: 10px 20px;
    background-color: #3498db; /* Blue */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.refresh-btn:hover {
    background-color: #2980b9; /* Darker blue */
}

/* Table Buttons Section */
.table-buttons {
    width: 100%;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.table-buttons h3 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 18px;
}

.button-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
    margin-bottom: 5px;
}

.button-row button {
    padding: 10px;
    min-height: 40px;
    font-size: 14px;
    background-color: #3498db; /* Blue */
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    white-space: nowrap;
}

.button-row button:hover {
    background-color: #2980b9; /* Darker blue */
}

/* Highlight Selected Table Button */
.button-row button.selected {
    background-color: #2ecc71; /* Green */
    color: white;
    border: 2px solid #27ae60; /* Darker green */
}

/* Table Button - Unpaid (Orange) */
.button-row button.unpaid {
    background-color: #e67e22; /* Orange */
    color: white;
    border: 2px solid #d35400; /* Darker orange */
}

/* Table Summary Section */
.table-summary {
    width: 100%;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.table-summary h3 {
    margin-top: 0;
    color: #2c3e50;
}

/* Customer Bills Container - Grid Layout */
.customer-bills-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    padding: 15px;
    max-height: 65vh;
    overflow-y: auto;
}

/* Customer Bill Card */
.customer-bill {
    width: 100%;
    min-width: 0;
    max-width: 100%;
    margin: 0 auto;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
}

.customer-bill.paid {
    background-color: #f5f5f5;
    opacity: 0.7;
    border-color: #ccc;
}

.customer-bill.paid h4 {
    color: #666;
}

.customer-bill.paid .item-name,
.customer-bill.paid .item-quantity,
.customer-bill.paid .item-price {
    color: #666;
}

.customer-bill.paid .order-total {
    color: #666;
}

.customer-bill.paid .paid-status {
    color: #4CAF50;
    font-weight: bold;
    text-align: right;
    margin-top: 10px;
}

.customer-bill h4 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 14px;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.customer-bill ul {
    list-style-type: none;
    padding: 0;
    margin: 8px 0;
    width: 100%;
}

.customer-bill li {
    padding: 3px 0;
    border-bottom: 1px solid #eee;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.customer-bill li:last-child {
    border-bottom: none;
}

.item-button {
    width: 100%;
    text-align: left;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-name {
    flex: 1;
    text-align: left;
}

.item-price {
    margin-left: 10px;
    text-align: right;
    min-width: 60px;
}

.item-quantity {
    margin: 0 10px;
    color: #666;
}

.order-total {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    padding-top: 10px;
    border-top: 2px solid #eee;
}

.pay-split-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.pay-btn, .split-btn {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.pay-btn {
    background-color: #4CAF50;
    color: white;
}

.split-btn {
    background-color: #2196F3;
    color: white;
}

.pay-btn:hover {
    background-color: #45a049;
}

.split-btn:hover {
    background-color: #0b7dda;
}

/* Item Button Styles */
.customer-bill .item-button {
    width: 100%;
    padding: 6px 8px; /* Reduced from 8px 10px */
    background-color: #f0f7ff;
    color: #2c3e50;
    border: 1px solid #b3d7ff;
    border-radius: 4px; /* Reduced from 5px */
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-size: 13px; /* Reduced from 14px */
}

.customer-bill .item-button:hover {
    background-color: #e6f3ff;  /* Slightly darker light blue on hover */
    transform: translateX(5px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.customer-bill .item-button:active {
    background-color: #d9ecff;  /* Even darker light blue when clicked */
}

/* Split Item Button Styles */
.customer-bill .item-button[disabled] {
    background-color: #f5f5f5;  /* Light gray background */
    color: #999;  /* Light gray text */
    cursor: not-allowed;
    border-color: #ddd;  /* Light gray border */
}

.customer-bill .item-button[disabled]:hover {
    transform: none;
    box-shadow: none;
}

.customer-bill p {
    font-weight: bold;
    margin: 8px 0; /* Reduced from 10px */
    font-size: 13px; /* Added font size */
}

/* Pay and Split Buttons */
.customer-bill .pay-split-buttons {
    display: flex;
    gap: 4px; /* Reduced from 5px */
    margin-top: 8px; /* Reduced from 10px */
}

.customer-bill .pay-split-buttons button {
    flex: 1;
    padding: 6px 8px; /* Reduced from 8px 10px */
    font-size: 12px;
    border: none;
    border-radius: 4px; /* Reduced from 5px */
    cursor: pointer;
    transition: background 0.3s ease;
}

.customer-bill .pay-split-buttons button.pay-btn {
    background-color: #2ecc71; /* Green */
    color: white;
}

.customer-bill .pay-split-buttons button.pay-btn:hover {
    background-color: #27ae60; /* Darker green */
}

.customer-bill .pay-split-buttons button.split-btn {
    background-color: #3498db; /* Blue */
    color: white;
}

.customer-bill .pay-split-buttons button.undo-split-btn {
    background-color: #e74c3c; /* Red */
    color: white;
}

.customer-bill .pay-split-buttons button.split-btn:hover {
    background-color: #2980b9; /* Darker blue */
}

.customer-bill .pay-split-buttons button.undo-split-btn:hover {
    background-color: #c0392b; /* Darker red */
}

.paid-status {
    color: #4caf50; /* Green */
    font-weight: bold;
    text-align: center;
}

/* Bottom Section */
.bottom-section {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 15px;
}

.bottom-section button {
    padding: 12px;
    min-height: 45px;
    font-size: 14px;
    white-space: nowrap;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Variables */
:root {
    --primary-color: #4CAF50;
    --primary-dark-color: #388E3C;
    --secondary-color: #2196F3;
    --accent-color: #FF9800;
    --danger-color: #f44336;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --text-light: #ffffff;
    --text-dark: #333333;
}

/* Total Bill Button */
.total-bill-btn {
    padding: 10px 15px;
    background-color: #3498db; /* Blue */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: default;
}

.total-bill-btn span {
    font-weight: bold;
}

/* Pay Table Button */
.pay-table-btn {
    background-color: #95a5a6; /* Gray - same as mis-btn */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.pay-table-btn:hover {
    background-color: #7f8c8d; /* Darker gray - same as mis-btn hover */
}

/* MIS Buttons */
.mis-btn {
    padding: 10px 20px;
    background-color: #9b59b6; /* Purple */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.mis-btn:hover {
    background-color: #8e44ad; /* Darker purple */
}

/* Clear Table Button */
#clear-table-btn {
    background-color: #e74c3c; /* Red */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

#clear-table-btn:hover {
    background-color: #c0392b; /* Darker red */
}

/* Back to Ordering Button */
.back-to-ordering-btn {
    padding: 10px 20px;
    background-color: #9b59b6; /* Purple */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-left: auto; /* Push to the right */
}

.back-to-ordering-btn:hover {
    background-color: #8e44ad; /* Darker purple */
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    overflow-y: auto; /* Allow scrolling if modal is taller than viewport */
}

.modal-content {
    display: flex;
    flex-direction: row;
    background-color: #fefefe;
    margin: 10% auto; /* Reduced from 15% to position higher */
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    position: relative;
    gap: 20px;
}

.modal-content h2 {
    margin-top: 0;
    margin-bottom: 8px; /* Added smaller bottom margin */
    font-size: 18px; /* Reduced from 24px */
}

/* Specific style for bill split modal title */
.bill-split-modal h2 {
    font-size: 16px;
}

.modal-content button {
    padding: 15px 25px;
    font-size: 16px;
    margin: 10px 0;
}

.close {
    float: right;
    cursor: pointer;
    font-size: 24px;
}

/* Split Modal Left Side */
.modal-left {
    flex: 1;
    padding-right: 20px;
    border-right: 1px solid #ddd;
}

/* Split Modal Right Side */
.modal-right {
    width: 250px;
    border-left: 1px solid #ddd;
    padding-left: 20px;
    display: flex;
    flex-direction: column;
}

.modal-right h3 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 15px;
}

/* Split Modal Content */
#splitOptions {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#splitOptions label {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    justify-content: center;
    user-select: none;
}

#splitOptions input[type="radio"] {
    display: none;
}

/* Specific colors for each split type when selected */
#splitOptions label.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
}

#splitOptions label[for="equal"].selected {
    background-color: #e8f5e9;
    border-color: #4caf50;
    color: #2e7d32;
}

#splitOptions label[for="percentage"].selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

#splitOptions label[for="custom"].selected {
    background-color: #fff3e0;
    border-color: #ff9800;
    color: #f57c00;
}

/* Percentage and Custom Inputs Styles */
#percentageInputs, #customInputs {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

#percentageInputs h3, #customInputs h3 {
    color: #2c3e50;
    margin-top: 0;
    font-size: 16px;
    margin-bottom: 10px;
}

#percentageFields input, #customFields input {
    width: 100%;
    padding: 8px 12px;
    margin: 8px 0;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
    box-sizing: border-box;
}

#percentageFields input:focus, #customFields input:focus {
    border-color: #b3d7ff;
    box-shadow: 0 0 0 3px rgba(179, 215, 255, 0.2);
    outline: none;
}

/* Split Modal Buttons */
#splitModal button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    margin-top: 15px;
    min-width: 120px;
    display: inline-block;
}

#confirmSplit {
    background-color: #2ecc71;
    color: white;
    margin-right: 10px;
}

#confirmSplit:hover {
    background-color: #27ae60;
    transform: translateY(-2px);
}

#undoSplit {
    background-color: #e74c3c;
    color: white;
    display: inline-block;
}

#undoSplit:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
}

/* Selected Item Name */
#selectedItemName {
    color: #2ecc71;
    font-weight: bold;
    font-size: 16px;
}

/* Message styles */
.message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    z-index: 1000;
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, -20px); }
    15% { opacity: 1; transform: translate(-50%, 0); }
    85% { opacity: 1; transform: translate(-50%, 0); }
    100% { opacity: 0; transform: translate(-50%, -20px); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        width: 98%;
        padding: 10px;
    }

    .customer-bills-container {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .container {
        width: 100%;
        margin: 0;
        border-radius: 0;
    }

    .button-row {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    }

    .customer-bills-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .bottom-section {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .bottom-section {
        grid-template-columns: 1fr;
    }

    .customer-bill {
        min-width: 140px;
    }
}

/* Split Modal Styles */
#splitModal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

#splitModal .modal-content {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 400px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

#splitModal h2 {
    color: #2c3e50;
    margin-top: 0;
    font-size: 20px;
    border-bottom: 2px solid #f0f7ff;
    padding-bottom: 8px;
}

#splitModal p {
    color: #666;
    margin: 10px 0;
    font-size: 14px;
}

#splitModal .close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

#splitModal .close:hover {
    color: #333;
}

/* Bill Selection Styles */
#billSelection {
    margin: 8px 0;
    max-height: 120px;
    overflow-y: auto;
    padding: 4px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

#billSelection label {
    display: block;
    padding: 8px 12px;
    margin: 2px 0;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    user-select: none;
}

#billSelection label:hover {
    background-color: #f0f7ff;
    transform: translateX(2px);
}

#billSelection label.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
}

.modal-left button {
    margin-right: 10px;
    display: inline-block;
}

.customer-bill .total-amount {
    font-weight: bold;
    margin: 8px 0;
    font-size: 13px;
    transition: color 0.3s ease;
}

.customer-bill .total-amount:hover {
    color: #3498db;
}

.customer-bill.paid .total-amount {
    color: #666;
    cursor: default;
}

.customer-bill.paid .total-amount:hover {
    color: #666;
}
/* Disabled Split Button Styles */
.customer-bill .pay-split-buttons button.disabled-split-btn {
    background-color: #cccccc !important;
    color: #666666 !important;
    cursor: not-allowed !important;
    opacity: 0.7 !important;
}

.customer-bill .pay-split-buttons button.disabled-split-btn:hover {
    background-color: #cccccc !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Bill Split Modal Styles */
.bill-split-modal {
    flex-direction: column;
    max-width: 400px; /* Reduced from 500px */
    margin: 5% auto; /* Position higher on the screen */
    max-height: 80vh; /* Limit height to prevent overflow */
    overflow-y: auto; /* Add scrolling if needed */
}

.parts-selection {
    margin: 12px 0; /* Reduced from 20px */
}

.parts-selection label {
    display: block;
    margin-bottom: 6px; /* Reduced from 10px */
    font-weight: bold;
    font-size: 14px; /* Smaller font */
}

.parts-controls {
    display: flex;
    align-items: center;
    gap: 8px; /* Reduced from 10px */
}

.parts-controls input {
    width: 50px; /* Reduced from 60px */
    text-align: center;
    padding: 6px; /* Reduced from 8px */
    font-size: 14px; /* Reduced from 16px */
    border: 1px solid #ddd;
    border-radius: 4px;
}

.parts-controls .count-btn {
    background-color: #3498db;
    color: white;
    border: none;
    width: 26px; /* Reduced from 30px */
    height: 26px; /* Reduced from 30px */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px; /* Reduced from 14px */
}

.parts-controls .count-btn:hover {
    background-color: #2980b9;
}

.bill-split-preview {
    background-color: #f8f9fa;
    padding: 10px; /* Reduced from 15px */
    border-radius: 6px; /* Reduced from 8px */
    margin: 10px 0; /* Reduced from 15px */
}

.bill-split-preview h3 {
    margin-top: 0;
    margin-bottom: 6px; /* Added smaller bottom margin */
    color: #2c3e50;
    font-size: 14px; /* Smaller font */
}

#splitPreview {
    list-style-type: none;
    padding: 0;
    margin: 6px 0; /* Reduced from 10px */
}

#splitPreview li {
    padding: 3px 0; /* Reduced from 5px */
    border-bottom: 1px solid #eee;
    font-size: 13px; /* Smaller font */
}

#amountPerBill {
    font-weight: bold;
    color: #2ecc71;
    font-size: 14px; /* Smaller font */
}

.bill-split-buttons {
    display: flex;
    gap: 8px; /* Reduced from 10px */
    margin-top: 12px; /* Reduced from 20px */
    justify-content: flex-end;
}

.bill-split-buttons button {
    padding: 8px 12px; /* Reduced from 10px 15px */
    border: none;
    border-radius: 4px; /* Reduced from 5px */
    cursor: pointer;
    font-size: 13px; /* Reduced from 14px */
    transition: background 0.3s ease;
}

#confirmBillSplit {
    background-color: #2ecc71;
    color: white;
}

#confirmBillSplit:hover {
    background-color: #27ae60;
}

#cancelBillSplit {
    background-color: #e74c3c;
    color: white;
}

#cancelBillSplit:hover {
    background-color: #c0392b;
}