{"name": "paradiz-mobile-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "npx expo start", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.16", "@types/react": "~18.0.27", "axios": "^1.4.0", "expo": "~48.0.18", "expo-barcode-scanner": "~12.3.2", "expo-device": "~11.1.1", "expo-font": "~11.1.1", "expo-notifications": "~0.18.1", "expo-secure-store": "~12.1.1", "expo-splash-screen": "~0.18.2", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.8", "react-native-calendars": "^1.1294.0", "react-native-gesture-handler": "~2.9.0", "react-native-paper": "^5.8.0", "react-native-reanimated": "~2.14.4", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0", "react-native-vector-icons": "^9.2.0", "typescript": "^4.9.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}