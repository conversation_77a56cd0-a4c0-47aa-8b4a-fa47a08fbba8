import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';

class NotificationService {
  constructor() {
    this.configure();
  }

  configure = async () => {
    // Configure how notifications appear when the app is in the foreground
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
  }

  // Request permissions
  requestPermissions = async () => {
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#6B46C1',
      });
      
      await Notifications.setNotificationChannelAsync('events', {
        name: 'Events',
        description: 'Notifications for upcoming events',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#6B46C1',
      });
    }

    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return false;
      }
      
      return true;
    } else {
      console.log('Must use physical device for Push Notifications');
      return false;
    }
  }

  // Get Expo push token
  getExpoPushToken = async () => {
    try {
      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        return null;
      }

      const { status } = await Notifications.getPermissionsAsync();
      if (status !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return null;
      }

      const token = (await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      })).data;
      
      return token;
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  // Schedule a local notification
  scheduleLocalNotification = async (title, body, data = {}, trigger = null) => {
    try {
      const notificationContent = {
        title,
        body,
        data,
        sound: true,
      };

      if (trigger) {
        return await Notifications.scheduleNotificationAsync({
          content: notificationContent,
          trigger,
        });
      } else {
        return await Notifications.scheduleNotificationAsync({
          content: notificationContent,
          trigger: { seconds: 1 },
        });
      }
    } catch (error) {
      console.error('Error scheduling notification:', error);
    }
  }

  // Schedule an event reminder
  scheduleEventReminder = async (event, reminderHours = 24) => {
    try {
      const eventDate = new Date(event.date);
      const reminderDate = new Date(eventDate);
      reminderDate.setHours(reminderDate.getHours() - reminderHours);

      // Only schedule if the reminder time is in the future
      if (reminderDate > new Date()) {
        return await this.scheduleLocalNotification(
          `Reminder: ${event.title}`,
          `Don't forget: ${event.title} is happening ${reminderHours === 24 ? 'tomorrow' : `in ${reminderHours} hours`}!`,
          { type: 'event', eventId: event.id },
          { date: reminderDate }
        );
      }
    } catch (error) {
      console.error('Error scheduling event reminder:', error);
    }
  }

  // Cancel a scheduled notification
  cancelScheduledNotification = async (notificationId) => {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  // Cancel all scheduled notifications
  cancelAllScheduledNotifications = async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  // Get all scheduled notifications
  getAllScheduledNotifications = async () => {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  // Set badge count
  setBadgeCount = async (count) => {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }
}

export default new NotificationService();
