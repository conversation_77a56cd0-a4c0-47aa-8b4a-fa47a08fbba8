document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const backBtn = document.getElementById('back-btn');
    const businessName = document.getElementById('business-name');
    const businessPhone = document.getElementById('business-phone');
    const businessAddress = document.getElementById('business-address');
    const businessTagline = document.getElementById('business-tagline');
    const includeLogo = document.getElementById('include-logo');
    const includeQR = document.getElementById('include-qr');
    const includeItemsDetail = document.getElementById('include-items-detail');
    const footerText = document.getElementById('footer-text');
    const testPhone = document.getElementById('test-phone');
    const sendTestBtn = document.getElementById('send-test');
    const saveSettingsBtn = document.getElementById('save-settings');
    const templatePreview = document.getElementById('template-preview');
    const statusMessage = document.getElementById('status-message');
    
    // Default settings
    const defaultSettings = {
        businessName: 'Paradiz Hookah Lounge',
        businessPhone: '',
        businessAddress: '',
        businessTagline: 'The Best Hookah Experience',
        includeLogo: true,
        includeQR: true,
        includeItemsDetail: true,
        footerText: 'Thank you for your business!'
    };
    
    // Sample order data for preview
    const sampleOrder = {
        id: 12345,
        tableNumber: '7',
        customerName: 'John Doe',
        timestamp: new Date().toISOString(),
        items: [
            { name: 'Hookah - Mint', price: 25.00, quantity: 1 },
            { name: 'Soft Drink', price: 3.50, quantity: 2 },
            { name: 'Fries', price: 5.00, quantity: 1 }
        ],
        totalAmount: 37.00,
        paymentMethod: 'Cash'
    };
    
    // Initialize
    loadSettings();
    updatePreview();
    
    // Event Listeners
    backBtn.addEventListener('click', function() {
        window.location.href = '../index.html';
    });
    
    // Update preview when settings change
    [businessName, businessTagline, footerText].forEach(input => {
        input.addEventListener('input', updatePreview);
    });
    
    [includeLogo, includeQR, includeItemsDetail].forEach(checkbox => {
        checkbox.addEventListener('change', updatePreview);
    });
    
    // Save settings
    saveSettingsBtn.addEventListener('click', saveSettings);
    
    // Send test receipt
    sendTestBtn.addEventListener('click', sendTestReceipt);
    
    /**
     * Load saved settings
     */
    function loadSettings() {
        fetch('http://localhost:5000/api/whatsapp-settings')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const settings = data.settings;
                    
                    // Set form values
                    businessName.value = settings.businessName || defaultSettings.businessName;
                    businessPhone.value = settings.businessPhone || defaultSettings.businessPhone;
                    businessAddress.value = settings.businessAddress || defaultSettings.businessAddress;
                    businessTagline.value = settings.businessTagline || defaultSettings.businessTagline;
                    includeLogo.checked = settings.includeLogo !== undefined ? settings.includeLogo : defaultSettings.includeLogo;
                    includeQR.checked = settings.includeQR !== undefined ? settings.includeQR : defaultSettings.includeQR;
                    includeItemsDetail.checked = settings.includeItemsDetail !== undefined ? settings.includeItemsDetail : defaultSettings.includeItemsDetail;
                    footerText.value = settings.footerText || defaultSettings.footerText;
                    
                    // Update preview
                    updatePreview();
                    
                    showStatus('Settings loaded successfully', 'success');
                } else {
                    // Use default settings
                    businessName.value = defaultSettings.businessName;
                    businessTagline.value = defaultSettings.businessTagline;
                    includeLogo.checked = defaultSettings.includeLogo;
                    includeQR.checked = defaultSettings.includeQR;
                    includeItemsDetail.checked = defaultSettings.includeItemsDetail;
                    footerText.value = defaultSettings.footerText;
                    
                    updatePreview();
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
                // Use default settings
                businessName.value = defaultSettings.businessName;
                businessTagline.value = defaultSettings.businessTagline;
                includeLogo.checked = defaultSettings.includeLogo;
                includeQR.checked = defaultSettings.includeQR;
                includeItemsDetail.checked = defaultSettings.includeItemsDetail;
                footerText.value = defaultSettings.footerText;
                
                updatePreview();
            });
    }
    
    /**
     * Save settings
     */
    function saveSettings() {
        const settings = {
            businessName: businessName.value,
            businessPhone: businessPhone.value,
            businessAddress: businessAddress.value,
            businessTagline: businessTagline.value,
            includeLogo: includeLogo.checked,
            includeQR: includeQR.checked,
            includeItemsDetail: includeItemsDetail.checked,
            footerText: footerText.value
        };
        
        fetch('http://localhost:5000/api/whatsapp-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatus('Settings saved successfully', 'success');
            } else {
                showStatus(`Error saving settings: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            showStatus('Error saving settings', 'error');
        });
    }
    
    /**
     * Send test receipt
     */
    function sendTestReceipt() {
        const phone = testPhone.value.trim();
        
        if (!phone) {
            showStatus('Please enter a phone number', 'error');
            return;
        }
        
        // Show loading state
        sendTestBtn.disabled = true;
        sendTestBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        
        // Get current settings
        const settings = {
            businessName: businessName.value,
            businessPhone: businessPhone.value,
            businessAddress: businessAddress.value,
            businessTagline: businessTagline.value,
            includeLogo: includeLogo.checked,
            includeQR: includeQR.checked,
            includeItemsDetail: includeItemsDetail.checked,
            footerText: footerText.value
        };
        
        // Send test receipt
        fetch('http://localhost:5000/api/whatsapp-test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phoneNumber: phone,
                settings: settings,
                order: sampleOrder
            })
        })
        .then(response => response.json())
        .then(data => {
            // Restore button state
            sendTestBtn.disabled = false;
            sendTestBtn.innerHTML = '<i class="fab fa-whatsapp"></i> Send Test Receipt';
            
            if (data.success) {
                showStatus('Test receipt sent successfully', 'success');
            } else {
                showStatus(`Error sending test receipt: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error sending test receipt:', error);
            
            // Restore button state
            sendTestBtn.disabled = false;
            sendTestBtn.innerHTML = '<i class="fab fa-whatsapp"></i> Send Test Receipt';
            
            showStatus('Error sending test receipt', 'error');
        });
    }
    
    /**
     * Update receipt preview
     */
    function updatePreview() {
        const name = businessName.value || defaultSettings.businessName;
        const tagline = businessTagline.value || defaultSettings.businessTagline;
        const showLogo = includeLogo.checked;
        const showQR = includeQR.checked;
        const showItems = includeItemsDetail.checked;
        const footer = footerText.value || defaultSettings.footerText;
        
        // Calculate total
        const total = sampleOrder.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        // Format date
        const date = new Date(sampleOrder.timestamp);
        const formattedDate = date.toLocaleDateString();
        const formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        // Build preview HTML
        let html = '';
        
        // Logo
        if (showLogo) {
            html += `
                <div class="business-logo">
                    <i class="fas fa-store fa-2x"></i>
                </div>
            `;
        }
        
        // Business name and tagline
        html += `
            <div class="business-name">${name}</div>
        `;
        
        if (tagline) {
            html += `<div class="business-tagline">${tagline}</div>`;
        }
        
        // Receipt header
        html += `
            <div class="receipt-header">
                <div>Date: ${formattedDate} | Time: ${formattedTime}</div>
                <div>Table: ${sampleOrder.tableNumber} | Customer: ${sampleOrder.customerName}</div>
            </div>
        `;
        
        // Items
        if (showItems) {
            html += '<div class="receipt-items">';
            html += '<div><strong>Items:</strong></div>';
            
            sampleOrder.items.forEach(item => {
                const itemTotal = item.price * item.quantity;
                html += `
                    <div class="item">
                        <span>${item.name} x${item.quantity}</span>
                        <span>$${itemTotal.toFixed(2)}</span>
                    </div>
                `;
            });
            
            html += '</div>';
        }
        
        // Total
        html += `
            <div class="receipt-total">
                Total: $${total.toFixed(2)}
            </div>
        `;
        
        // Payment method
        html += `
            <div>Payment Method: ${sampleOrder.paymentMethod}</div>
        `;
        
        // QR Code
        if (showQR) {
            html += `
                <div class="qr-code">
                    <div>Scan to verify receipt:</div>
                    <div><i class="fas fa-qrcode fa-3x"></i></div>
                </div>
            `;
        }
        
        // Footer
        if (footer) {
            html += `
                <div class="receipt-footer">
                    ${footer}
                </div>
            `;
        }
        
        // Update preview
        templatePreview.innerHTML = html;
    }
    
    /**
     * Show status message
     * @param {string} message - Status message
     * @param {string} type - 'success' or 'error'
     */
    function showStatus(message, type = 'success') {
        statusMessage.textContent = message;
        statusMessage.className = 'status-message';
        
        if (type === 'success') {
            statusMessage.classList.add('status-success');
        } else {
            statusMessage.classList.add('status-error');
        }
        
        // Hide after 5 seconds
        setTimeout(() => {
            statusMessage.style.display = 'none';
        }, 5000);
    }
});
