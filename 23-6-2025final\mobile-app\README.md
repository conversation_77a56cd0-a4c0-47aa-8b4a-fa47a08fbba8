# Paradiz Hookah Lounge Mobile App

This is the mobile app for Paradiz Hookah Lounge, allowing customers to view the menu, make reservations, and place orders directly from their tables.

## Features

- **Digital Menu**: Browse the complete menu with images and descriptions
- **Reservations**: Make, view, and cancel reservations
- **In-House Ordering**: Scan table QR codes to place orders directly from your table
- **User Profiles**: Save preferences and view order history

## Getting Started

### Prerequisites

- Node.js (v14 or newer)
- npm or yarn
- Expo CLI (`npm install -g expo-cli`)

### Installation

1. Clone this repository
2. Navigate to the mobile-app directory
3. Install dependencies:

```bash
npm install
# or
yarn install
```

4. Start the development server:

```bash
npm start
# or
yarn start
```

5. Use the Expo Go app on your mobile device to scan the QR code, or run on an emulator

### Building for Production

#### Android

```bash
expo build:android
```

#### iOS

```bash
expo build:ios
```

## Project Structure

```
mobile-app/
├── assets/            # Images, fonts, and other static assets
├── src/
│   ├── api/           # API service functions
│   ├── components/    # Reusable UI components
│   ├── context/       # React Context providers
│   ├── hooks/         # Custom React hooks
│   ├── screens/       # App screens
│   └── utils/         # Utility functions
├── App.tsx            # Main app component
├── app.json           # Expo configuration
└── package.json       # Dependencies and scripts
```

## Integration with POS System

The mobile app integrates with the existing POS system through the following API endpoints:

- `/api/menu/categories` - Get menu categories
- `/api/menu/items` - Get menu items
- `/api/reservations/*` - Reservation management
- `/api/orders/*` - Order management
- `/api/auth/*` - User authentication

## QR Code System for Table Ordering

Each table in the establishment has a unique QR code that contains:

1. Table ID
2. Session ID (for security)

When scanned, the app opens the TableOrderScreen with these parameters, allowing customers to place orders directly from their table.

## Deployment

### App Store Submission

1. Build the app for iOS using `expo build:ios`
2. Submit the resulting IPA file to the App Store using App Store Connect
3. Complete the App Store listing information
4. Wait for Apple's review process

### Google Play Submission

1. Build the app for Android using `expo build:android`
2. Create a Google Play Developer account
3. Create a new app in the Google Play Console
4. Upload the APK or AAB file
5. Complete the Play Store listing information
6. Publish the app (can start with a closed testing track)

## Server-Side Requirements

The mobile app requires the following API endpoints to be implemented on the server:

1. Authentication endpoints
2. Menu data endpoints
3. Reservation management endpoints
4. Order management endpoints

See the API service files for the expected request/response formats.

## Customization

- Update colors in `App.tsx` to match your branding
- Replace logo and splash images in the `assets` folder
- Modify the app configuration in `app.json`

## License

This project is proprietary and confidential. Unauthorized copying or distribution is prohibited.
