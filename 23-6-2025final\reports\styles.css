/* Reports Page Styles */

/* Data Export Styles */
.data-export-section {
    margin-top: 40px;
    padding: 20px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.data-export-section h2 {
    font-size: 22px;
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.data-export-section h2 i {
    margin-right: 10px;
}

.section-description {
    margin-bottom: 20px;
    color: var(--dark-gray);
    font-size: 16px;
}

.export-options-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.export-form {
    flex: 1;
    min-width: 300px;
}

.export-info {
    flex: 1;
    min-width: 300px;
    background-color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
}

.date-input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 16px;
}

.export-destinations {
    margin-top: 20px;
    margin-bottom: 20px;
}

.export-destinations h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--text-color);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.checkbox-item label {
    font-size: 16px;
    cursor: pointer;
}

.conditional-options {
    background-color: rgba(52, 152, 219, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    border-left: 3px solid var(--primary-color);
}

.help-text {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.help-text i {
    color: var(--primary-color);
}

.export-actions {
    margin-top: 20px;
}

.export-btn {
    background-color: var(--secondary-color);
    color: white;
    padding: 12px 20px;
    font-size: 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.export-btn:hover {
    background-color: var(--secondary-dark);
}

.export-contents {
    list-style-type: none;
    padding: 0;
    margin: 15px 0;
}

.export-contents li {
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.export-contents li i {
    color: var(--secondary-color);
    margin-top: 3px;
}

.auto-export-settings {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--medium-gray);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--secondary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--secondary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: var(--border-radius);
    max-width: 500px;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close-modal:hover {
    color: var(--text-color);
}

.success-message, .error-message {
    text-align: center;
    padding: 20px 0;
}

.success-message i, .error-message i {
    font-size: 48px;
    margin-bottom: 15px;
}

.success-message i {
    color: var(--secondary-color);
}

.error-message i {
    color: var(--danger-color);
}

.success-message h2, .error-message h2 {
    margin-top: 0;
    margin-bottom: 10px;
}

.success-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.success-actions button {
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 16px;
    background-color: var(--primary-color);
    color: white;
    transition: background-color 0.2s;
}

.success-actions button:hover {
    background-color: var(--primary-dark);
}

.success-actions p {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--secondary-color);
    font-weight: bold;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .export-options-container {
        flex-direction: column;
    }

    .export-form, .export-info {
        min-width: 100%;
    }
}

/* Reports Page Styles */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --accent-color: #f39c12;
    --danger-color: #e74c3c;
    --light-gray: #f4f4f4;
    --medium-gray: #e0e0e0;
    --dark-gray: #333;
    --text-color: #333;
    --border-radius: 5px;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

header h1 {
    font-size: 24px;
    margin: 0;
    display: flex;
    align-items: center;
}

header h1 i {
    margin-right: 10px;
    color: var(--primary-color);
}

.actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn i {
    margin-right: 8px;
    font-size: 16px;
}

.action-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Style for each specific button */
.action-btn#back-btn {
    background-color: var(--primary-color);
}

.action-btn#print-btn {
    background-color: var(--secondary-color);
}

.action-btn#export-btn {
    background-color: var(--accent-color);
}

.action-btn#back-btn:hover {
    background-color: var(--primary-dark);
}

.action-btn#print-btn:hover {
    background-color: var(--secondary-dark);
}

.action-btn#export-btn:hover {
    background-color: #e67e22; /* Darker orange */
}

/* Report Controls */
.report-controls {
    background-color: var(--light-gray);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.date-range {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-range label {
    font-weight: bold;
}

.date-range select {
    padding: 8px;
    border-radius: var(--border-radius);
    border: 1px solid var(--medium-gray);
}

.custom-date-range {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed var(--medium-gray);
}

.date-input {
    display: flex;
    align-items: center;
    gap: 5px;
}

.date-input label {
    font-weight: bold;
}

.date-input input {
    padding: 8px;
    border-radius: var(--border-radius);
    border: 1px solid var(--medium-gray);
}

.apply-btn {
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.apply-btn:hover {
    background-color: var(--primary-dark);
}

/* Summary Cards */
.report-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    text-align: center;
}

.summary-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--dark-gray);
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

/* Charts */
.report-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
}

.chart-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--dark-gray);
}

canvas {
    width: 100% !important;
    height: 250px !important;
}

/* Tables */
.report-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.table-container {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
}

.table-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--dark-gray);
}

.report-table {
    width: 100%;
    border-collapse: collapse;
}

.report-table th {
    background-color: var(--light-gray);
    padding: 10px;
    text-align: left;
    font-weight: bold;
    border-bottom: 2px solid var(--medium-gray);
}

.report-table td {
    padding: 10px;
    border-bottom: 1px solid var(--medium-gray);
}

.report-table tr:last-child td {
    border-bottom: none;
}

.report-table .loading {
    text-align: center;
    color: var(--dark-gray);
    font-style: italic;
    padding: 20px;
}

/* Order Details */
.report-details {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
}

.report-details h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--dark-gray);
}

.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.search-box input {
    padding: 8px 12px;
    border-radius: var(--border-radius);
    border: 1px solid var(--medium-gray);
    width: 250px;
}

.table-pagination {
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-pagination button {
    background-color: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    padding: 5px 10px;
    cursor: pointer;
}

.table-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.orders-table {
    margin-top: 10px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        align-items: flex-start;
    }

    .actions {
        margin-top: 15px;
    }

    .report-charts,
    .report-tables {
        grid-template-columns: 1fr;
    }

    .custom-date-range {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .table-controls {
        flex-direction: column;
        gap: 10px;
    }

    .search-box input {
        width: 100%;
    }
}

/* Dark Theme Support */
body.dark-theme {
    background-color: #1a1a1a;
    color: #f0f0f0;
}

body.dark-theme .container {
    background-color: #2a2a2a;
}

body.dark-theme .summary-card,
body.dark-theme .chart-container,
body.dark-theme .table-container,
body.dark-theme .report-details {
    background-color: #333;
    color: #f0f0f0;
}

body.dark-theme .report-controls {
    background-color: #333;
}

body.dark-theme .report-table th {
    background-color: #444;
    border-bottom: 2px solid #555;
}

body.dark-theme .report-table td {
    border-bottom: 1px solid #444;
}

body.dark-theme .date-range select,
body.dark-theme .date-input input,
body.dark-theme .search-box input,
body.dark-theme .table-pagination button {
    background-color: #444;
    border-color: #555;
    color: #f0f0f0;
}

/* Dark theme action buttons - keep their colors but adjust for dark mode */
body.dark-theme .action-btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.dark-theme .action-btn:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

body.dark-theme .action-btn#back-btn {
    background-color: #2980b9; /* Slightly darker blue for dark theme */
}

body.dark-theme .action-btn#print-btn {
    background-color: #27ae60; /* Slightly darker green for dark theme */
}

body.dark-theme .action-btn#export-btn {
    background-color: #d35400; /* Slightly darker orange for dark theme */
}

body.dark-theme .summary-card h3,
body.dark-theme .chart-container h3,
body.dark-theme .table-container h3,
body.dark-theme .report-details h3 {
    color: #f0f0f0;
}

body.dark-theme .summary-value {
    color: #3498db;
}
