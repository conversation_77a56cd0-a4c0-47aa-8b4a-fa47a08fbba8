<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pending Bills - Paradiz <PERSON></title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="../theme.js"></script>
</head>
<body>
    <div class="container">
        <h1>Pending Bills</h1>
        
        <div class="pending-bills-container">
            <div class="pending-bills-header">
                <div class="total-section">
                    <div class="total-item">
                        <span class="total-label">Total Pending:</span>
                        <span id="total-count" class="total-value">0</span>
                    </div>
                    <div class="total-item">
                        <span class="total-label">Total Amount:</span>
                        <span id="total-amount" class="total-value">$0.00</span>
                    </div>
                </div>
                <div class="actions-section">
                    <button id="refresh-btn" class="action-btn"><i class="fas fa-sync-alt"></i> Refresh</button>
                </div>
            </div>
            
            <div id="bills-list" class="bills-list">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading pending bills...</p>
                </div>
            </div>
        </div>
        
        <nav class="secondary-nav">
            <ul>
                <li><a id="back-btn" class="button" href="#"><i class="fas fa-arrow-left"></i> BACK TO SETTINGS</a></li>
            </ul>
        </nav>
    </div>
    
    <!-- Bill Details Modal -->
    <div id="bill-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="bill-details" class="bill-details">
                <!-- Bill details will be loaded here -->
            </div>
        </div>
    </div>
    
    <!-- Payment Modal -->
    <div id="payment-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Process Payment</h2>
            <div class="payment-options">
                <button id="pay-cash" class="payment-btn"><i class="fas fa-money-bill-wave"></i> Cash</button>
                <button id="pay-card" class="payment-btn"><i class="fas fa-credit-card"></i> Card</button>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
