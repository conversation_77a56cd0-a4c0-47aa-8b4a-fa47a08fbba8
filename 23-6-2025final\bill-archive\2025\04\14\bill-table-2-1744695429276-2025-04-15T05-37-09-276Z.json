{"id": "table-2-1744695429276", "tableNumber": "2", "customerName": "Table 2 (Combined)", "timestamp": "2025-04-15T05:37:09.276Z", "items": [{"name": "<PERSON><PERSON>", "price": 25, "quantity": 1, "notes": ""}, {"name": "Veggie Burger", "price": 1.11, "quantity": 1, "notes": ""}, {"name": "Watermelon + Double apple", "price": 25, "quantity": 1, "notes": ""}, {"name": "<PERSON><PERSON>", "price": 7.99, "quantity": 1, "notes": ""}], "payment": {"paymentMethod": "card", "tip": 0, "subtotal": 59.1, "tax": 4.71, "taxRate": 0.07969543147208122, "gst": 2.96, "gstRate": 0.050084602368866325, "pst": 1.75, "pstRate": 0.02961082910321489, "total": 63.8, "transactionId": null}, "archiveTimestamp": "2025-04-15T05:37:09.277Z"}