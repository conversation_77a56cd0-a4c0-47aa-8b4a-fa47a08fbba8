<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Settings - Paradiz <PERSON></title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="./styles.css">
    <script src="../../theme.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <button id="back-btn" class="back-button">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1><i class="fab fa-whatsapp"></i> WhatsApp Settings</h1>
        </div>

        <div class="content">
            <div class="settings-card">
                <h2>Business Information</h2>
                <div class="form-group">
                    <label for="business-name">Business Name</label>
                    <input type="text" id="business-name" placeholder="Enter your business name">
                </div>
                
                <div class="form-group">
                    <label for="business-phone">WhatsApp Phone Number</label>
                    <input type="text" id="business-phone" placeholder="Enter your WhatsApp number (with country code)">
                    <small>Include country code (e.g., +1 for USA)</small>
                </div>
                
                <div class="form-group">
                    <label for="business-tagline">Business Tagline</label>
                    <input type="text" id="business-tagline" placeholder="Enter your business tagline">
                </div>
                
                <div class="form-group">
                    <label for="business-address">Business Address</label>
                    <textarea id="business-address" rows="2" placeholder="Enter your business address"></textarea>
                </div>
            </div>
            
            <div class="settings-card">
                <h2>Receipt Options</h2>
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="include-logo" checked>
                        Include Business Logo
                    </label>
                </div>
                
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="include-qr" checked>
                        Include QR Code
                    </label>
                </div>
                
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="include-items" checked>
                        Show Detailed Item List
                    </label>
                </div>
                
                <div class="form-group">
                    <label for="footer-text">Custom Footer Text</label>
                    <textarea id="footer-text" rows="2" placeholder="Enter custom footer text for receipts"></textarea>
                </div>
            </div>
            
            <div class="settings-card">
                <h2>Receipt Preview</h2>
                <div class="preview-container">
                    <div id="receipt-preview" class="whatsapp-preview">
                        <!-- Preview content will be generated by JavaScript -->
                        <div class="loading">Loading preview...</div>
                    </div>
                </div>
            </div>
            
            <div class="settings-card">
                <h2>Test Receipt</h2>
                <div class="form-group">
                    <label for="test-phone">Test Phone Number</label>
                    <input type="text" id="test-phone" placeholder="Enter phone number to send test receipt">
                    <small>Include country code (e.g., +1 for USA)</small>
                </div>
                
                <button id="test-btn" class="whatsapp-btn">
                    <i class="fab fa-whatsapp"></i> Send Test Receipt
                </button>
            </div>
            
            <div class="action-buttons">
                <button id="save-btn" class="primary-btn">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </div>
            
            <div id="status-message" class="status-message"></div>
        </div>
    </div>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="./script.js"></script>
</body>
</html>
