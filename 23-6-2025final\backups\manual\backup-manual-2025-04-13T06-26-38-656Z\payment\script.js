// Global variables
let orderData = null;
let customTotal = null;

// Get order data from URL parameters or POST data
const urlParams = new URLSearchParams(window.location.search);
const orderDataParam = urlParams.get('orderData');

// Check if we have POST data from the pending bills page
function getPostData() {
    // Check if we have form data in the URL (from a POST that was redirected)
    const tableNumber = urlParams.get('tableNumber');
    const customerName = urlParams.get('customerName');

    if (tableNumber && customerName) {
        return { tableNumber, customerName };
    }

    // If we're coming directly from a form submission
    const forms = document.getElementsByTagName('form');
    if (forms.length > 0) {
        const form = forms[0];
        const formData = new FormData(form);
        const tableNumber = formData.get('tableNumber');
        const customerName = formData.get('customerName');

        if (tableNumber && customerName) {
            return { tableNumber, customerName };
        }
    }

    return null;
}

// Get POST data if available
const postData = getPostData();

// DOM Elements
const tableNumberElement = document.getElementById('table-number');
const customerNameElement = document.getElementById('customer-name');
const itemsListElement = document.getElementById('items-list');
const subtotalElement = document.getElementById('subtotal');
const gstElement = document.getElementById('gst');
const pstElement = document.getElementById('pst');
const totalElement = document.getElementById('total');
const whatsappModal = document.getElementById('whatsapp-modal');
const whatsappNumberInput = document.getElementById('whatsapp-number');
const closeModal = document.querySelector('.close');
const keypadButtons = document.querySelectorAll('.key');
const sendWhatsappButton = document.getElementById('send-whatsapp');
const backupButton = document.getElementById('backup-button');

// Payment buttons
const payCashButton = document.getElementById('pay-cash');
const payCardButton = document.getElementById('pay-card');
const printReceiptButton = document.getElementById('print-receipt');
const whatsappReceiptButton = document.getElementById('whatsapp-receipt');
const pendingBillButton = document.getElementById('pending-bill');

// Get the custom amount modal elements
const customAmountModal = document.getElementById('custom-amount-modal');
const customAmountInput = document.getElementById('custom-amount');
const applyCustomAmountBtn = document.getElementById('apply-custom-amount');
const editBillBtn = document.getElementById('edit-bill');

// Function to display order details
function displayOrderDetails() {
    if (!orderData) return;

    // Filter out any null or invalid items
    orderData.items = orderData.items.filter(item =>
        item &&
        item.name &&
        item.price !== undefined &&
        item.price !== null &&
        item.price > 0
    );

    // Display order information
    document.getElementById('table-number').innerHTML = `<i class="fas fa-table"></i> Table: ${orderData.tableNumber}`;
    document.getElementById('customer-name').innerHTML = `<i class="fas fa-user"></i> Customer: ${orderData.customerName}`;

    // Display items
    displayItems();

    // Calculate and display totals
    updateTotals();
}

// Function to display items
function displayItems() {
    if (!orderData || !orderData.items) return;

    const itemsList = document.getElementById('items-list');
    itemsList.innerHTML = '';

    orderData.items.forEach(item => {
        const li = document.createElement('li');
        const quantity = item.quantity || 1;

        // Remove split prefixes like "1/2 " from item names
        let displayName = item.name.replace(/^\d+\/\d+\s/, '');

        // Check if this is a split item and add a badge if it is
        const isSplitItem = item.name !== displayName;
        const splitMatch = item.name.match(/^(\d+)\/(\d+)\s/);
        const splitBadge = isSplitItem && splitMatch ?
            `<span class="split-badge">${splitMatch[1]}/${splitMatch[2]}</span>` : '';

        li.innerHTML = `
            <span>
                <i class="fas fa-utensils"></i>
                ${displayName} ${splitBadge}
                ${quantity > 1 ? `<span class="item-quantity">x${quantity}</span>` : ''}
            </span>
            <span>$${formatPrice(item.price * quantity)}</span>
        `;
        itemsList.appendChild(li);
    });
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Set up event listeners for payment buttons
    setupEventListeners();

    // Get order data from URL
    if (orderDataParam) {
        try {
            orderData = JSON.parse(decodeURIComponent(orderDataParam));
            displayOrderDetails();
        } catch (error) {
            console.error('Error parsing order data:', error);
            showError('Invalid order data');
        }
    } else if (postData) {
        // We have POST data from the pending bills page
        console.log('Loading order from pending bills:', postData);

        // Fetch the order data from the server based on table and customer
        fetch(`/api/orders?tableNumber=${encodeURIComponent(postData.tableNumber)}&customerName=${encodeURIComponent(postData.customerName)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.orders && data.orders.length > 0) {
                    orderData = data.orders[0];
                    displayOrderDetails();
                } else {
                    // If no active order found, try to get it from pending bills
                    return fetch(`/api/pending-bills?tableNumber=${encodeURIComponent(postData.tableNumber)}&customerName=${encodeURIComponent(postData.customerName)}`);
                }
            })
            .then(response => response ? response.json() : null)
            .then(data => {
                if (data && data.success && data.bills && data.bills.length > 0) {
                    // Convert pending bill to order data format
                    const pendingBill = data.bills[0];
                    orderData = {
                        id: pendingBill.id,
                        tableNumber: pendingBill.tableNumber,
                        customerName: pendingBill.customerName,
                        items: pendingBill.items,
                        timestamp: pendingBill.createdAt
                    };
                    displayOrderDetails();

                    // Show a message that this is a pending bill
                    showMessage('Loaded pending bill for payment');
                } else {
                    showError('No order found for this table and customer');
                }
            })
            .catch(error => {
                console.error('Error fetching order data:', error);
                showError('Failed to load order data');
            });
    } else {
        // If accessed directly from main page, redirect to POS page
        window.location.href = '../pos/index.html';
    }
});

// Format price to 2 decimal places
function formatPrice(price) {
    return Number(price).toFixed(2);
}

// Show error message
function showError(message) {
    showMessage(message, true);
}

// Handle WhatsApp modal
function openWhatsappModal() {
    whatsappModal.style.display = 'block';
    whatsappNumberInput.value = '';
}

function closeWhatsappModal() {
    whatsappModal.style.display = 'none';
}

// This is handled in setupEventListeners

// Handle WhatsApp send
async function sendWhatsappReceipt() {
    const phoneNumber = whatsappNumberInput.value;
    if (!phoneNumber) {
        showError('Please enter a phone number');
        return;
    }

    try {
        // Show loading state
        const sendButton = document.getElementById('send-whatsapp');
        const originalText = sendButton.textContent;
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        sendButton.disabled = true;

        // Format the receipt message (now async)
        const receiptMessage = await formatReceiptMessage();

        // Try to get the sender's WhatsApp number from localStorage or server
        let senderNumber = '';
        try {
            const savedBusinessInfo = localStorage.getItem('whatsappBusinessInfo');
            if (savedBusinessInfo) {
                const businessInfo = JSON.parse(savedBusinessInfo);
                senderNumber = businessInfo.senderWhatsapp || '';
            }
        } catch (error) {
            console.error('Error getting sender number:', error);
        }

        // Send via WhatsApp API
        const response = await fetch('http://localhost:5000/send-whatsapp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phoneNumber,
                message: receiptMessage,
                senderNumber: senderNumber
            })
        });

        if (response.ok) {
            alert('Receipt sent successfully!');
            closeWhatsappModal();
        } else {
            throw new Error('Failed to send receipt');
        }
    } catch (error) {
        console.error('Error sending WhatsApp:', error);
        showError('Failed to send receipt. Please try again.');
    } finally {
        // Restore button state
        const sendButton = document.getElementById('send-whatsapp');
        if (sendButton) {
            sendButton.innerHTML = 'Send';
            sendButton.disabled = false;
        }
    }
}

// Format receipt message
async function formatReceiptMessage() {
    try {
        // Try to get the template and business info from the server
        const response = await fetch('http://localhost:5000/api/whatsapp-template');
        const data = await response.json();

        if (response.ok && data.success) {
            let template = data.template;
            const businessInfo = data.businessInfo || {};

            // Get current date and time
            const now = new Date();
            const dateStr = now.toLocaleDateString();
            const timeStr = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Format items
            let itemsText = '';
            orderData.items.forEach(item => {
                itemsText += `${item.name} x ${item.quantity || 1} - $${formatPrice(item.price * (item.quantity || 1))}\n`;
            });

            // Get transaction ID (from orderData or localStorage)
            const transactionId = orderData.transactionId || localStorage.getItem('lastTransactionId') || '';

            // Get QR code if transaction ID exists
            let qrCodeDataUrl = '';
            if (transactionId) {
                try {
                    const qrResponse = await fetch(`http://localhost:5000/api/qrcode/${transactionId}`);
                    const qrData = await qrResponse.json();
                    if (qrResponse.ok && qrData.success) {
                        qrCodeDataUrl = qrData.qrCode;
                    }
                } catch (qrError) {
                    console.error('Error fetching QR code:', qrError);
                }
            }

            // Create data object with all variables
            const templateData = {
                businessName: businessInfo.name || 'Paradiz Hookah Lounge',
                businessTagline: businessInfo.tagline || '',
                logoUrl: businessInfo.logoUrl || '',
                contactInfo: businessInfo.contactInfo || '',
                tableNumber: orderData.tableNumber,
                customerName: orderData.customerName,
                date: dateStr,
                time: timeStr,
                items: itemsText,
                subtotal: subtotalElement.textContent.replace('$', ''),
                gst: gstElement.textContent.replace('$', ''),
                pst: pstElement.textContent.replace('$', ''),
                total: totalElement.textContent.replace('$', ''),
                paymentMethod: 'Pending',
                transactionId: transactionId,
                qrCode: qrCodeDataUrl
            };

            // Set logo position flags based on business info
            const logoPosition = businessInfo.logoPosition || 'top';
            templateData.logoTop = (logoPosition === 'top' || logoPosition === 'both');
            templateData.logoBottom = (logoPosition === 'bottom' || logoPosition === 'both');

            // Process conditional sections
            template = processConditionalSections(template, templateData);

            // Replace all variables with data
            for (const [key, value] of Object.entries(templateData)) {
                if (typeof value === 'string') {
                    const regex = new RegExp(`{{${key}}}`, 'g');
                    template = template.replace(regex, value);
                }
            }

            return template;
        } else {
            throw new Error('Failed to get template');
        }
    } catch (error) {
        console.error('Error getting template:', error);

        // Fallback to default format if template fetch fails
        let message = `Receipt for Table ${orderData.tableNumber}\n\n`;
        message += `Items:\n`;
        orderData.items.forEach(item => {
            message += `${item.name} x ${item.quantity || 1} - $${formatPrice(item.price * (item.quantity || 1))}\n`;
        });
        message += `\nSummary:\n`;
        message += `Subtotal: $${subtotalElement.textContent.replace('$', '')}\n`;
        message += `GST (5%): $${gstElement.textContent.replace('$', '')}\n`;
        message += `PST (7%): $${pstElement.textContent.replace('$', '')}\n`;
        message += `Total: $${totalElement.textContent.replace('$', '')}\n`;

        // Add transaction ID if available
        const transactionId = orderData.transactionId || localStorage.getItem('lastTransactionId');
        if (transactionId) {
            message += `\nTransaction ID: ${transactionId}\n`;
        }

        message += `\nThank you for your business!`;
        return message;
    }
}

// Process conditional sections like {{#section}}content{{/section}}
function processConditionalSections(template, data) {
    let processedTemplate = template;

    // Process each conditional section
    for (const [key, value] of Object.entries(data)) {
        // Skip non-string and non-boolean values
        if (typeof value !== 'string' && typeof value !== 'boolean') continue;

        // Pattern for conditional sections
        const sectionPattern = new RegExp(`{{#${key}}}([\s\S]*?){{\/${key}}}`, 'g');

        if (typeof value === 'boolean') {
            // For boolean values (like logoTop, logoBottom)
            if (value) {
                // If true, keep the content but remove the conditional tags
                processedTemplate = processedTemplate.replace(sectionPattern, '$1');
            } else {
                // If false, remove the entire section including content
                processedTemplate = processedTemplate.replace(sectionPattern, '');
            }
        } else {
            // For string values
            if (value && value.trim() !== '') {
                // If value exists, keep the content but remove the conditional tags
                processedTemplate = processedTemplate.replace(sectionPattern, '$1');
            } else {
                // If value is empty, remove the entire section
                processedTemplate = processedTemplate.replace(sectionPattern, '');
            }
        }
    }

    return processedTemplate;
}

// Handle payment methods
async function handlePayment(method) {
    try {
        if (!orderData) {
            showError('No order data available');
            return;
        }

        // Show loading indicator
        const button = method === 'cash' ? payCashButton : payCardButton;
        const originalText = button.innerHTML;
        button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> Processing...`;
        button.disabled = true;

        // Show temporary message
        showMessage(`Processing ${method} payment...`);

        // Get tax rates from the displayed values
        const subtotalValue = parseFloat(subtotalElement.textContent.replace('$', '')) || 0;
        let gstValue = parseFloat(gstElement.textContent.replace('$', '')) || 0;
        let pstValue = parseFloat(pstElement.textContent.replace('$', '')) || 0;
        let totalValue = parseFloat(totalElement.textContent.replace('$', '')) || 0;

        // For cash payments, remove taxes
        if (method === 'cash') {
            gstValue = 0;
            pstValue = 0;
            totalValue = subtotalValue; // Total is just the subtotal for cash payments

            // Update the display to show zero taxes for cash payments
            gstElement.textContent = '$0.00';
            pstElement.textContent = '$0.00';
            totalElement.textContent = `$${formatPrice(subtotalValue)}`;
        }

        // Calculate tax rates
        const gstRate = method === 'cash' ? 0 : (subtotalValue > 0 ? gstValue / subtotalValue : 0.05);
        const pstRate = method === 'cash' ? 0 : (subtotalValue > 0 ? pstValue / subtotalValue : 0.07);
        const taxRate = gstRate + pstRate;
        const taxAmount = gstValue + pstValue;

        // Add tax information to the order data
        const paymentData = {
            orderData: {
                ...orderData,
                taxRate: taxRate,
                taxAmount: taxAmount,
                gstRate: gstRate,
                gstAmount: gstValue,
                pstRate: pstRate,
                pstAmount: pstValue,
                subtotal: subtotalValue,
                total: totalValue
            },
            paymentMethod: method
        };

        console.log('Sending payment data with tax information:', paymentData);

        // Process payment through the server
        const response = await fetch('http://localhost:5000/process-payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(paymentData)
        });

        const result = await response.json();

        // Store the transaction ID for use in receipts
        if (result.transactionId) {
            orderData.transactionId = result.transactionId;
            localStorage.setItem('lastTransactionId', result.transactionId);
        }

        if (response.ok) {
            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'success-message';

            if (result.message === 'Order already paid') {
                successMessage.innerHTML = `
                    <h3>Order Already Paid</h3>
                    <p>This order was already paid via ${result.paymentMethod}</p>
                    <div class="success-options">
                        <button onclick="returnToPOSWithRefresh()" class="action-btn">Return to POS</button>
                        <button onclick="this.parentElement.parentElement.remove()" class="action-btn">Close</button>
                    </div>
                `;
            } else {
                successMessage.innerHTML = `
                    <h3>Payment Successful!</h3>
                    <p>Payment processed via ${method}</p>
                    ${result.transactionId ? `<p class="transaction-id">Transaction ID: ${result.transactionId}</p>` : ''}
                    <div class="success-options">
                        <button onclick="returnToPOSWithRefresh()" class="action-btn">Return to POS</button>
                        <button onclick="this.parentElement.parentElement.remove()" class="action-btn">Close</button>
                    </div>
                `;
            }

            document.body.appendChild(successMessage);
        } else {
            throw new Error(result.error || 'Payment failed');
        }
    } catch (error) {
        console.error('Payment error:', error);
        showError('Payment failed: ' + error.message);
    } finally {
        // Restore button state
        if (method === 'cash' && payCashButton) {
            payCashButton.innerHTML = `<i class="fas fa-money-bill-wave"></i> Pay Cash`;
            payCashButton.disabled = false;
        } else if (method === 'card' && payCardButton) {
            payCardButton.innerHTML = `<i class="fas fa-credit-card"></i> Pay Card`;
            payCardButton.disabled = false;
        }
    }
}

// Show receipt processing modal
function showReceiptProcessingModal() {
    const receiptModal = document.getElementById('receipt-processing-modal');
    if (receiptModal) {
        receiptModal.style.display = 'block';
    }
}

// Close receipt processing modal
function closeReceiptProcessingModal() {
    const receiptModal = document.getElementById('receipt-processing-modal');
    if (receiptModal) {
        receiptModal.style.display = 'none';
    }
}

// Return to POS page
function returnToPOS() {
    window.location.href = '../pos/index.html';
}

// Return to POS page with refresh parameter to ensure it reloads
function returnToPOSWithRefresh() {
    window.location.href = '../pos/index.html?refresh=' + new Date().getTime();
}

// Setup all event listeners
function setupEventListeners() {
    // Close modal button
    if (closeModal) {
        closeModal.addEventListener('click', closeWhatsappModal);
    }

    // Close modal when clicking outside of it
    window.addEventListener('click', (event) => {
        if (event.target === whatsappModal) {
            closeWhatsappModal();
        }
    });

    // Keypad functionality
    keypadButtons.forEach(button => {
        button.addEventListener('click', () => {
            const key = button.textContent;
            if (key === '*' || key === '#' || key === 'C') {
                if (key === 'C') {
                    whatsappNumberInput.value = '';
                } else {
                    whatsappNumberInput.value += key;
                }
            } else {
                whatsappNumberInput.value += key;
            }
        });
    });

    // Event listeners for payment buttons
    if (whatsappReceiptButton) {
        whatsappReceiptButton.addEventListener('click', openWhatsappModal);
    }

    if (sendWhatsappButton) {
        sendWhatsappButton.addEventListener('click', sendWhatsappReceipt);
    }

    if (payCashButton) {
        payCashButton.addEventListener('click', () => handlePayment('cash'));
    }

    if (payCardButton) {
        payCardButton.addEventListener('click', () => handlePayment('card'));
    }

    if (printReceiptButton) {
        printReceiptButton.addEventListener('click', () => {
            window.print();
        });
    }

    // Add event listener for edit button
    if (editBillBtn) {
        editBillBtn.addEventListener('click', () => {
            customAmountModal.style.display = 'block';
            customAmountInput.value = totalElement.textContent.replace('$', '');
        });
    }

    // Back to POS button
    const backToPosBtn = document.getElementById('back-to-pos');
    if (backToPosBtn) {
        backToPosBtn.addEventListener('click', () => {
            window.location.href = '../pos/index.html';
        });
    }

    // Close custom amount modal
    if (customAmountModal) {
        const closeCustomModal = customAmountModal.querySelector('.close');
        if (closeCustomModal) {
            closeCustomModal.addEventListener('click', () => {
                customAmountModal.style.display = 'none';
            });
        }

        // Handle custom amount keypad
        const customAmountKeypad = customAmountModal.querySelector('.keypad');
        if (customAmountKeypad) {
            customAmountKeypad.addEventListener('click', (e) => {
                if (e.target.classList.contains('key')) {
                    const key = e.target.textContent;
                    if (key === 'C') {
                        customAmountInput.value = '';
                    } else if (key === '.') {
                        if (!customAmountInput.value.includes('.')) {
                            customAmountInput.value += key;
                        }
                    } else {
                        customAmountInput.value += key;
                    }
                }
            });
        }
    }

    // Apply custom amount
    if (applyCustomAmountBtn) {
        applyCustomAmountBtn.addEventListener('click', () => {
            const customAmount = parseFloat(customAmountInput.value);
            if (!isNaN(customAmount) && customAmount >= 0) {
                customTotal = customAmount;
                totalElement.textContent = `$${customAmount.toFixed(2)}`;
                customAmountModal.style.display = 'none';
                showMessage('Custom amount applied successfully');
            } else {
                showMessage('Please enter a valid amount');
            }
        });
    }

    // Add event listener for backup button
    if (backupButton) {
        backupButton.addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:5000/backup', {
                    method: 'POST'
                });

                if (response.ok) {
                    showMessage('Backup created successfully');
                } else {
                    throw new Error('Failed to create backup');
                }
            } catch (error) {
                console.error('Error creating backup:', error);
                showError('Failed to create backup. Please try again.');
            }
        });
    }

    // Add event listener for pending bill button
    if (pendingBillButton) {
        pendingBillButton.addEventListener('click', async () => {
            try {
                if (!orderData) {
                    showError('No order data available');
                    return;
                }

                // Show loading indicator
                const originalText = pendingBillButton.innerHTML;
                pendingBillButton.innerHTML = `<i class="fas fa-spinner fa-spin"></i> Processing...`;
                pendingBillButton.disabled = true;

                // Create a modal for notes
                const notesModal = document.createElement('div');
                notesModal.className = 'modal';
                notesModal.style.display = 'block';
                notesModal.innerHTML = `
                    <div class="modal-content">
                        <span class="close">&times;</span>
                        <h2>Mark Bill as Pending</h2>
                        <p>This bill will be saved for later payment.</p>
                        <div class="form-group">
                            <label for="pending-notes">Notes (optional):</label>
                            <textarea id="pending-notes" placeholder="Add any notes about this pending payment..."></textarea>
                        </div>
                        <button id="confirm-pending" class="action-btn">Mark as Pending</button>
                    </div>
                `;

                document.body.appendChild(notesModal);

                // Add event listeners for the modal
                const closeBtn = notesModal.querySelector('.close');
                const confirmBtn = notesModal.querySelector('#confirm-pending');

                closeBtn.addEventListener('click', () => {
                    notesModal.remove();
                    pendingBillButton.innerHTML = originalText;
                    pendingBillButton.disabled = false;
                });

                confirmBtn.addEventListener('click', async () => {
                    const notes = document.getElementById('pending-notes').value;

                    try {
                        // Send request to mark bill as pending
                        console.log('Sending data to mark as pending:', {
                            orderData,
                            notes
                        });

                        const response = await fetch('/mark-as-pending', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                orderData,
                                notes
                            })
                        });

                        // Check if response is ok before trying to parse JSON
                        if (!response.ok) {
                            const text = await response.text();
                            console.error('Server returned error:', response.status, text);
                            throw new Error(`Server error: ${response.status}`);
                        }

                        let result;
                        try {
                            result = await response.json();
                        } catch (e) {
                            console.error('Error parsing response:', e);
                            const text = await response.text();
                            console.error('Response text:', text);
                            throw new Error('Invalid response from server');
                        }

                        // Process the successful response
                        notesModal.remove();

                        // Show success message
                        const successMessage = document.createElement('div');
                        successMessage.className = 'success-message';
                        successMessage.innerHTML = `
                            <h3>Bill Marked as Pending</h3>
                            <p>This bill has been saved to the pending bills section.</p>
                            <div class="success-options">
                                <button onclick="window.location.href='../pos/index.html'" class="action-btn">Return to POS</button>
                                <button onclick="window.location.href='../pending-bills/index.html'" class="action-btn">View Pending Bills</button>
                            </div>
                        `;

                        document.body.appendChild(successMessage);
                    } catch (error) {
                        console.error('Error marking bill as pending:', error);
                        showError('Failed to mark bill as pending: ' + error.message);
                        notesModal.remove();
                    } finally {
                        pendingBillButton.innerHTML = originalText;
                        pendingBillButton.disabled = false;
                    }
                });
            } catch (error) {
                console.error('Error:', error);
                showError('An error occurred: ' + error.message);
                pendingBillButton.innerHTML = originalText;
                pendingBillButton.disabled = false;
            }
        });
    }

    // Add event listeners for receipt processing modal
    const closeReceiptModal = document.querySelector('.close-receipt-modal');
    if (closeReceiptModal) {
        closeReceiptModal.addEventListener('click', closeReceiptProcessingModal);
    }

    const returnToPOSBtn = document.getElementById('return-to-pos');
    if (returnToPOSBtn) {
        returnToPOSBtn.addEventListener('click', returnToPOS);
    }
}

// Show message
function showMessage(message, isError = false) {
    // Create a message element
    const messageElement = document.createElement('div');
    messageElement.className = isError ? 'error-message' : 'success-message';
    messageElement.innerHTML = `
        <i class="${isError ? 'fas fa-exclamation-circle' : 'fas fa-check-circle'}"></i>
        <span>${message}</span>
    `;

    // Add to the body
    document.body.appendChild(messageElement);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        messageElement.style.opacity = '0';
        setTimeout(() => {
            messageElement.remove();
        }, 500);
    }, 3000);
}

// Update the updateTotals function to handle custom amount
function updateTotals() {
    // Calculate subtotal
    const subtotal = orderData.items.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);

    // Separate items by category for tax calculation
    let gstSubtotal = 0; // All items get GST
    let pstSubtotal = 0; // Only cold drinks, hookah, and cover charges get PST

    orderData.items.forEach(item => {
        const itemTotal = item.price * (item.quantity || 1);
        const itemName = item.name.toLowerCase();

        // All items get GST
        gstSubtotal += itemTotal;

        // Check if item is a cold drink
        const isColdDrink = itemName.includes('soda') ||
                           itemName.includes('pop') ||
                           itemName.includes('juice') ||
                           itemName.includes('water') ||
                           itemName.includes('cold') ||
                           itemName.includes('iced') ||
                           itemName.includes('ice');

        // Check if item is hookah
        const isHookah = itemName.includes('hookah') ||
                        itemName.includes('shisha');

        // Check for cover charge
        const isCoverCharge = itemName.includes('cover') ||
                             itemName.includes('entry') ||
                             itemName.includes('admission') ||
                             itemName.includes('fee');

        // Apply PST to cold drinks, hookah items, and cover charges
        if (isColdDrink || isHookah || isCoverCharge) {
            pstSubtotal += itemTotal;

            // Add a visual indicator for PST-applicable items
            const listItem = document.querySelector(`#items-list li:nth-child(${orderData.items.indexOf(item) + 1})`);
            if (listItem) {
                const nameSpan = listItem.querySelector('span:first-child');
                if (nameSpan && !nameSpan.innerHTML.includes('tax-badge')) {
                    nameSpan.innerHTML += '<span class="tax-badge" title="PST Applicable">P</span>';
                }
            }
        }
    });

    // Calculate taxes
    const gst = gstSubtotal * 0.05;
    const pst = pstSubtotal * 0.07;
    const calculatedTotal = subtotal + gst + pst;

    // Update display
    subtotalElement.textContent = `$${formatPrice(subtotal)}`;
    gstElement.textContent = `$${formatPrice(gst)}`;
    pstElement.textContent = `$${formatPrice(pst)}`;

    // Only update total if it hasn't been manually set
    if (!customTotal) {
        totalElement.textContent = `$${formatPrice(calculatedTotal)}`;
    }
}

// Function to display orders
function displayOrders() {
    const ordersList = document.getElementById('ordersList');
    ordersList.innerHTML = '';

    // Group orders by table number and customer name
    const groupedOrders = {};
    orders.forEach(order => {
        const key = `${order.tableNumber}-${order.customerName}`;
        if (!groupedOrders[key]) {
            groupedOrders[key] = {
                tableNumber: order.tableNumber,
                customerName: order.customerName,
                items: [],
                paid: order.paid
            };
        }

        // Add items to the grouped order, combining duplicates
        order.items.forEach(item => {
            const existingItem = groupedOrders[key].items.find(i =>
                i.name === item.name &&
                Math.abs(i.price - item.price) < 0.01
            );

            if (existingItem) {
                existingItem.quantity = (existingItem.quantity || 1) + (item.quantity || 1);
            } else {
                groupedOrders[key].items.push({
                    ...item,
                    quantity: item.quantity || 1
                });
            }
        });
    });

    // Display grouped orders
    Object.values(groupedOrders).forEach(order => {
        const orderDiv = document.createElement('div');
        orderDiv.className = 'order';
        orderDiv.innerHTML = `
            <h3>Table ${order.tableNumber} - ${order.customerName}</h3>
            <ul>
                ${order.items.map(item => `
                    <li>
                        <span class="item-name">${item.name}</span>
                        <span class="item-quantity">x${item.quantity || 1}</span>
                        <span class="item-price">$${formatPrice(item.price * (item.quantity || 1))}</span>
                    </li>
                `).join('')}
            </ul>
            <div class="order-total">
                <span>Total:</span>
                <span>$${formatPrice(order.items.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0))}</span>
            </div>
        `;
        ordersList.appendChild(orderDiv);
    });

    // Update the total amount
    const totalAmount = Object.values(groupedOrders).reduce((total, order) => {
        return total + order.items.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);
    }, 0);
    document.getElementById('totalAmount').textContent = `$${formatPrice(totalAmount)}`;
}

// Handle backup
async function handleBackup() {
    try {
        const response = await fetch('http://localhost:5000/api/backup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            alert('Backup created successfully!');
            console.log('Backup created at:', result.backupPath);
        } else {
            throw new Error(result.error || 'Failed to create backup');
        }
    } catch (error) {
        console.error('Backup error:', error);
        alert('Failed to create backup: ' + error.message);
    }
}

