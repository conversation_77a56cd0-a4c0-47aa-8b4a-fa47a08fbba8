const fs = require('fs');
const path = require('path');

class FinancialService {
    constructor() {
        this.dataDir = path.join(__dirname, 'data');
        this.transactionsFile = path.join(this.dataDir, 'transactions.json');
        this.dailySummariesFile = path.join(this.dataDir, 'dailySummaries.json');
        this.ensureDataDirectory();
        this.initializeFiles();
    }

    ensureDataDirectory() {
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
    }

    initializeFiles() {
        if (!fs.existsSync(this.transactionsFile)) {
            fs.writeFileSync(this.transactionsFile, JSON.stringify({ transactions: [] }));
        }
        if (!fs.existsSync(this.dailySummariesFile)) {
            fs.writeFileSync(this.dailySummariesFile, JSON.stringify({ dailySummaries: [] }));
        }
    }

    async recordTransaction(transaction) {
        const data = JSON.parse(fs.readFileSync(this.transactionsFile));
        
        // Add transaction with unique ID and timestamp
        const newTransaction = {
            ...transaction,
            id: crypto.randomUUID(),
            timestamp: new Date().toISOString()
        };

        data.transactions.push(newTransaction);
        fs.writeFileSync(this.transactionsFile, JSON.stringify(data, null, 2));

        // Update daily summary
        await this.updateDailySummary(newTransaction);

        return newTransaction;
    }

    async updateDailySummary(transaction) {
        const data = JSON.parse(fs.readFileSync(this.dailySummariesFile));
        const today = new Date().toISOString().split('T')[0];
        
        let dailySummary = data.dailySummaries.find(summary => 
            summary.date.split('T')[0] === today
        );

        if (!dailySummary) {
            dailySummary = {
                date: today,
                totalSales: 0,
                totalTips: 0,
                totalTax: 0,
                paymentMethodBreakdown: {
                    cash: 0,
                    card: 0,
                    other: 0
                },
                voidedTransactions: 0,
                refundedTransactions: 0
            };
            data.dailySummaries.push(dailySummary);
        }

        // Update summary with new transaction
        dailySummary.totalSales += transaction.paymentDetails.total;
        dailySummary.totalTips += transaction.paymentDetails.tip || 0;
        dailySummary.totalTax += transaction.paymentDetails.tax || 0;
        
        const paymentMethod = transaction.paymentDetails.paymentMethod.toLowerCase();
        if (dailySummary.paymentMethodBreakdown[paymentMethod] !== undefined) {
            dailySummary.paymentMethodBreakdown[paymentMethod] += transaction.paymentDetails.total;
        } else {
            dailySummary.paymentMethodBreakdown.other += transaction.paymentDetails.total;
        }

        if (transaction.status === 'voided') {
            dailySummary.voidedTransactions++;
        } else if (transaction.status === 'refunded') {
            dailySummary.refundedTransactions++;
        }

        fs.writeFileSync(this.dailySummariesFile, JSON.stringify(data, null, 2));
    }

    async getTransactions(filters = {}) {
        const data = JSON.parse(fs.readFileSync(this.transactionsFile));
        let transactions = data.transactions;

        // Apply filters
        if (filters.date) {
            transactions = transactions.filter(t => 
                t.timestamp.split('T')[0] === filters.date
            );
        }
        if (filters.tableNumber) {
            transactions = transactions.filter(t => 
                t.tableNumber === filters.tableNumber
            );
        }
        if (filters.customerName) {
            transactions = transactions.filter(t => 
                t.customerName === filters.customerName
            );
        }
        if (filters.status) {
            transactions = transactions.filter(t => 
                t.status === filters.status
            );
        }

        return transactions;
    }

    async getDailySummary(date) {
        const data = JSON.parse(fs.readFileSync(this.dailySummariesFile));
        return data.dailySummaries.find(summary => 
            summary.date.split('T')[0] === date
        );
    }

    async getFinancialReport(startDate, endDate) {
        const transactions = await this.getTransactions();
        const filteredTransactions = transactions.filter(t => {
            const transactionDate = new Date(t.timestamp);
            return transactionDate >= new Date(startDate) && 
                   transactionDate <= new Date(endDate);
        });

        const report = {
            period: { startDate, endDate },
            totalSales: 0,
            totalTips: 0,
            totalTax: 0,
            paymentMethodBreakdown: {
                cash: 0,
                card: 0,
                other: 0
            },
            voidedTransactions: 0,
            refundedTransactions: 0,
            transactions: filteredTransactions
        };

        filteredTransactions.forEach(transaction => {
            report.totalSales += transaction.paymentDetails.total;
            report.totalTips += transaction.paymentDetails.tip || 0;
            report.totalTax += transaction.paymentDetails.tax || 0;
            
            const paymentMethod = transaction.paymentDetails.paymentMethod.toLowerCase();
            if (report.paymentMethodBreakdown[paymentMethod] !== undefined) {
                report.paymentMethodBreakdown[paymentMethod] += transaction.paymentDetails.total;
            } else {
                report.paymentMethodBreakdown.other += transaction.paymentDetails.total;
            }

            if (transaction.status === 'voided') {
                report.voidedTransactions++;
            } else if (transaction.status === 'refunded') {
                report.refundedTransactions++;
            }
        });

        return report;
    }
}

module.exports = FinancialService; 