@echo off
echo ============================================
echo Paradiz POS System - Server Diagnostic Tool
echo ============================================
echo.

REM Check Python installation
echo Checking Python installation...
python --version >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
) else (
    python --version
)
echo.

REM Check Node.js installation
echo Checking Node.js installation...
node --version >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
) else (
    node --version
)
echo.

REM Check if required files exist
echo Checking required files...
if exist server.js (
    echo [OK] server.js found
) else (
    echo [ERROR] server.js not found
)

if exist app.py (
    echo [OK] app.py found
) else (
    echo [ERROR] app.py not found
)
echo.

REM Check if ports are in use
echo Checking ports...
netstat -ano | findstr ":5000" >nul
if %ERRORLEVEL% equ 0 (
    echo [WARNING] Port 5000 is already in use
) else (
    echo [OK] Port 5000 is available
)

netstat -ano | findstr ":5001" >nul
if %ERRORLEVEL% equ 0 (
    echo [WARNING] Port 5001 is already in use
) else (
    echo [OK] Port 5001 is available
)
echo.

REM Check node_modules
echo Checking Node.js dependencies...
if exist node_modules (
    echo [OK] node_modules found
) else (
    echo [WARNING] node_modules not found - need to run npm install
)
echo.

REM Try to start Node.js server
echo Attempting to start Node.js server...
node -e "require('./server.js')" 2>temp_error.txt
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Failed to start Node.js server
    echo Error message:
    type temp_error.txt
    del temp_error.txt
) else (
    echo [OK] Node.js server code is valid
)
echo.

echo ============================================
echo Diagnostic Results:
echo ============================================
echo 1. Make sure no other applications are using ports 5000 or 5001
echo 2. Try running 'npm install' to install dependencies
echo 3. Check the server windows for specific error messages
echo.
echo Press any key to attempt to start the servers...
pause >nul

REM Kill existing processes on ports
taskkill /F /PID "LISTENING:5000" >nul 2>nul
taskkill /F /PID "LISTENING:5001" >nul 2>nul

REM Start servers with visible output
echo Starting Node.js server...
start "Node.js Server" cmd /k "node server.js"

if exist app.py (
    echo Starting Python server...
    start "Python Server" cmd /k "python app.py"
)

echo.
echo Servers should be starting in separate windows.
echo Check those windows for any error messages.
echo.
echo Press any key to exit...
pause >nul