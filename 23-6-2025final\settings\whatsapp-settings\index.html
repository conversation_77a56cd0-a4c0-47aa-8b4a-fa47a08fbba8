<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Settings - Paradiz Hookah Lounge</title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles.css">
    <script src="../../theme.js"></script>
</head>
<body>
    <div class="container">
        <div class="header-with-back">
            <button class="back-button" id="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>WhatsApp Settings</h1>
        </div>

        <div class="settings-section">
            <h2>Business Information</h2>
            <p class="settings-description">Enter your business information to be displayed on WhatsApp receipts.</p>

            <div class="form-group">
                <label for="business-name">Business Name:</label>
                <input type="text" id="business-name" placeholder="e.g., Paradiz Hookah Lounge">
            </div>

            <div class="form-group">
                <label for="business-phone">WhatsApp Business Phone Number:</label>
                <input type="text" id="business-phone" placeholder="e.g., +1234567890">
                <p class="form-hint">Include country code (e.g., +1 for USA)</p>
            </div>

            <div class="form-group">
                <label for="business-address">Business Address:</label>
                <textarea id="business-address" rows="2" placeholder="e.g., 123 Main St, City, State"></textarea>
            </div>

            <div class="form-group">
                <label for="business-tagline">Business Tagline (optional):</label>
                <input type="text" id="business-tagline" placeholder="e.g., The Best Hookah Experience">
            </div>
        </div>

        <div class="settings-section">
            <h2>Receipt Template</h2>
            <p class="settings-description">Customize your WhatsApp receipt template.</p>

            <div class="template-preview">
                <h3>Preview</h3>
                <div class="preview-container" id="template-preview">
                    <!-- Preview will be rendered here -->
                </div>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="include-logo" checked>
                    Include Business Logo
                </label>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="include-qr" checked>
                    Include QR Code
                </label>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="include-items-detail" checked>
                    Show Detailed Item List
                </label>
            </div>

            <div class="form-group">
                <label for="footer-text">Custom Footer Text (optional):</label>
                <textarea id="footer-text" rows="2" placeholder="e.g., Thank you for your business!"></textarea>
            </div>
        </div>

        <div class="settings-section">
            <h2>Test WhatsApp Receipt</h2>
            <p class="settings-description">Send a test receipt to verify your settings.</p>

            <div class="form-group">
                <label for="test-phone">Test Phone Number:</label>
                <input type="text" id="test-phone" placeholder="e.g., +1234567890">
                <p class="form-hint">Include country code (e.g., +1 for USA)</p>
            </div>

            <button id="send-test" class="primary-btn">
                <i class="fab fa-whatsapp"></i> Send Test Receipt
            </button>
        </div>

        <div class="settings-actions">
            <button id="save-settings" class="save-btn">
                <i class="fas fa-save"></i> Save Settings
            </button>
        </div>

        <div id="status-message" class="status-message"></div>
    </div>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="script.js"></script>
</body>
</html>
