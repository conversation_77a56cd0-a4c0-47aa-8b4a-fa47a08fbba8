from flask import Flask, render_template, jsonify, request, send_from_directory
import json
import sqlite3
from datetime import datetime
import os

app = Flask(__name__, static_folder='static', template_folder='templates')

# Load items from items.json
def load_items():
    try:
        with open('items.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Warning: items.json not found")
        return {}

menu_items = load_items()  # Renamed from items to menu_items

# Database connection
def get_db():
    try:
        db = sqlite3.connect('orders.db')
        db.row_factory = sqlite3.Row
        return db
    except sqlite3.Error as e:
        print(f"Database connection error: {e}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

# Serve kitchen display files
@app.route('/kitchen/food-drinks.html')
def food_drinks_display():
    return send_from_directory('kitchen', 'food-drinks.html')

@app.route('/kitchen/hookah.html')
def hookah_display():
    return send_from_directory('kitchen', 'hookah.html')

@app.route('/orders/<order_id>/kitchen', methods=['PUT'])
def complete_order(order_id):
    db = get_db()
    if not db:
        return jsonify({'success': False, 'error': 'Database connection failed'}), 500
    
    try:
        db.execute('''
            UPDATE orders 
            SET kitchenStatus = 'completed' 
            WHERE id = ?
        ''', (order_id,))
        db.commit()
        return jsonify({'success': True})
    except Exception as e:
        print(f"Error completing order: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        db.close()

@app.route('/orders')
def get_orders():
    db = get_db()
    if not db:
        return jsonify([])
    
    try:
        cursor = db.execute('''
            SELECT * FROM orders 
            WHERE paid = 0
        ''')
        orders = cursor.fetchall()
        
        # Format orders for JSON response
        formatted_orders = []
        for order in orders:
            try:
                formatted_orders.append({
                    'id': order['id'],
                    'tableNumber': order['tableNumber'],
                    'customerName': order['customerName'],
                    'items': json.loads(order['items']),
                    'timestamp': order['timestamp'],
                    'kitchenStatus': order['kitchenStatus']
                })
            except json.JSONDecodeError:
                print(f"Error decoding JSON for order {order['id']}")
                continue
        
        return jsonify(formatted_orders)
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return jsonify([])
    finally:
        db.close()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)  # Changed port to 5001