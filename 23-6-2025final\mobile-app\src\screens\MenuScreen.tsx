import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Image, Alert } from 'react-native';
import { Text, Searchbar, Chip, Card, Title, Paragraph, ActivityIndicator } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { menuApi } from '../api/api';

const MenuScreen = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCategories();
    loadMenuItems();
  }, []);

  useEffect(() => {
    loadMenuItems(selectedCategory);
  }, [selectedCategory]);

  const loadCategories = async () => {
    try {
      const response = await menuApi.getCategories();
      setCategories(response.data.categories);
    } catch (error) {
      console.error('Error loading categories:', error);
      Alert.alert('Error', 'Failed to load menu categories');
    }
  };

  const loadMenuItems = async (categoryId = null) => {
    try {
      setLoading(true);
      const response = await menuApi.getItems(categoryId);
      setMenuItems(response.data.items);
    } catch (error) {
      console.error('Error loading menu items:', error);
      Alert.alert('Error', 'Failed to load menu items');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadMenuItems(selectedCategory);
      return;
    }

    try {
      setLoading(true);
      const response = await menuApi.searchItems(searchQuery);
      setMenuItems(response.data.items);
    } catch (error) {
      console.error('Error searching items:', error);
      Alert.alert('Error', 'Failed to search menu items');
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryChip = ({ item }) => (
    <Chip
      selected={selectedCategory === item.id}
      onPress={() => setSelectedCategory(selectedCategory === item.id ? null : item.id)}
      style={styles.categoryChip}
      textStyle={{ color: selectedCategory === item.id ? 'white' : '#6B46C1' }}
      selectedColor="#6B46C1"
    >
      {item.name}
    </Chip>
  );

  const renderMenuItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => navigation.navigate('MenuDetail', { itemId: item.id })}
    >
      <Card style={styles.menuItemCard}>
        <Card.Content style={styles.menuItemContent}>
          <View style={styles.menuItemInfo}>
            <Title style={styles.menuItemTitle}>{item.name}</Title>
            <Paragraph style={styles.menuItemDescription} numberOfLines={2}>
              {item.description}
            </Paragraph>
            <Text style={styles.menuItemPrice}>${item.price.toFixed(2)}</Text>
          </View>
          {item.image && (
            <Image
              source={{ uri: item.image }}
              style={styles.menuItemImage}
              resizeMode="cover"
            />
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Searchbar
        placeholder="Search menu..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        onSubmitEditing={handleSearch}
        style={styles.searchBar}
      />

      <FlatList
        horizontal
        data={categories}
        renderItem={renderCategoryChip}
        keyExtractor={(item) => item.id.toString()}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContainer}
      />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6B46C1" />
        </View>
      ) : (
        <FlatList
          data={menuItems}
          renderItem={renderMenuItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.menuItemsContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No menu items found</Text>
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
    padding: 16,
  },
  searchBar: {
    marginBottom: 16,
    elevation: 2,
  },
  categoriesContainer: {
    paddingVertical: 8,
  },
  categoryChip: {
    marginRight: 8,
    backgroundColor: 'white',
    borderColor: '#6B46C1',
    borderWidth: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuItemsContainer: {
    paddingBottom: 16,
  },
  menuItemCard: {
    marginVertical: 8,
    elevation: 2,
  },
  menuItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  menuItemInfo: {
    flex: 1,
    marginRight: 8,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  menuItemDescription: {
    fontSize: 14,
    color: '#4A5568',
    marginVertical: 4,
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B46C1',
  },
  menuItemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#4A5568',
  },
});

export default MenuScreen;
