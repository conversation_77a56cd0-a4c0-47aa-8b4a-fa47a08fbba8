:root {
    --primary-color: #4a90e2;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --background-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    position: relative;
}

.header-left {
    position: absolute;
    left: 0;
}

.header-actions {
    position: absolute;
    right: 0;
    display: flex;
    gap: 1rem;
}

h1 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    flex-grow: 1;
    margin: 0 120px; /* Add space for the buttons on both sides */
}

.back-icon {
    margin-right: 0.5rem;
    font-size: 1.2em;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.backup-list {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
}

tr:hover {
    background-color: #f8f9fa;
}

.action-btn {
    padding: 0.375rem 0.75rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
}

.action-btn:hover {
    background-color: #1d4ed8;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: var(--surface-color);
    margin: 2rem auto;
    padding: 0;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-body {
    padding: 1rem;
}

.close {
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.close:hover {
    color: var(--text-primary);
}

/* Backup details styles */
.details-section {
    margin-bottom: 2rem;
}

.details-section h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    padding: 0.75rem;
    background-color: white;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
}

.detail-item strong {
    display: block;
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

/* Order card styles */
.order-card {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.order-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.order-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.order-items {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.order-items h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.items-list {
    list-style: none;
    padding: 0;
}

.items-list li {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.payment-details {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 0.375rem;
}

.payment-details h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.payment-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
}

.payment-info p {
    margin: 0;
}

.payment-info strong {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.error-section {
    background-color: #fee2e2;
    border: 1px solid #fecaca;
}

.error-message {
    color: #dc2626;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.files-list {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 1rem;
}

.files-list ul {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.5rem;
}

.files-list li {
    padding: 0.5rem;
    background-color: #f8fafc;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    word-break: break-all;
}

.table-container {
    overflow-x: auto;
}

.orders-table {
    min-width: 800px;
}

.orders-table th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
}

.orders-table td {
    vertical-align: top;
}

.status-pending {
    color: #ffc107;
}

.status-completed {
    color: var(--success-color);
}

.payment-method {
    text-transform: capitalize;
}

.total-amount {
    font-weight: 600;
    color: var(--primary-color);
} 