/* General Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
}

/* Icon Styles */
.fas, .far, .fab, .fa {
    margin-right: 5px;
}

.container {
    display: flex;
    width: 100%;
    height: 100vh;
    gap: 10px;
    padding: 10px;
    box-sizing: border-box;
}

/* Left Panel - Table & Customer Selection */
.left-panel {
    width: 20%;
    padding: 10px;
    background: #2c3e50;
    color: white;
    text-align: center;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.customer-name-input {
    margin-bottom: 20px;
}

.customer-name-input label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

.customer-name-input input {
    width: 100%;
    padding: 8px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    box-sizing: border-box;
}

/* Cover Charge Styles */
.cover-charge-section {
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
}

.cover-charge-toggle {
    margin-bottom: 15px;
}

.cover-charge-toggle label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #ecf0f1;
}

.cover-charge-toggle select {
    width: 100%;
    padding: 8px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    background-color: white;
    color: #333;
}

.person-count {
    margin-top: 10px;
}

.person-count label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #ecf0f1;
}

.person-count-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
}

.count-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 5px;
    background-color: #3498db;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.count-btn:hover {
    background-color: #2980b9;
}

#person-count {
    width: 60px;
    padding: 8px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    text-align: center;
    background-color: white;
}

#person-count::-webkit-inner-spin-button,
#person-count::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.selection-grid {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tables-column {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.tables-column h3 {
    margin-top: 0;
    font-size: 16px;
}

.table-buttons {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
    overflow-y: auto;
}

.table-btn {
    padding: 15px;
    background: #3498db;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.table-btn:hover {
    background: #2980b9;
}

/* Middle Panel - Categories & Order Items */
.middle-panel {
    width: 55%;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.categories {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.category-btn {
    flex: 1;
    padding: 15px;
    background: #3498db;
    color: white;
    border: none;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.category-btn:hover {
    background: #2980b9;
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    overflow-y: auto;
    flex-grow: 1;
    padding-bottom: 20px;
}

.items-grid p {
    grid-column: span 4;
    text-align: center;
    color: #666;
}

.item-btn {
    padding: 15px;
    background: #3498db;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.item-btn:hover {
    background: #2980b9;
}

/* Right Panel - Order Summary */
.right-panel {
    width: 25%;
    background: #ecf0f1;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.order-summary {
    margin-top: 20px;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.order-summary h3 {
    margin-top: 0;
    color: #2c3e50;
}

#order-list {
    list-style-type: none;
    padding: 0;
}

#order-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #ecf0f1;
    margin-bottom: 5px;
    border-radius: 5px;
}

.remove-btn {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.remove-btn .fas {
    margin-right: 0; /* Override the default margin for trash icon */
}

.remove-btn:hover {
    background: #c0392b;
}

.summary-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.send-btn {
    flex: 2;
    padding: 15px;
    background-color: #2ecc71;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
    transition: background 0.3s ease;
}

.send-btn:hover {
    background: #27ae60;
}

/* Hookah Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 30px;
    border: none;
    width: 80%;
    max-width: 800px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.flavor-hint {
    color: #666;
    margin-bottom: 20px;
    font-style: italic;
}

.flavor-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    margin: 20px 0;
}

.flavor-grid label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    background-color: #f8f9fa;
    border: 2px solid #ddd;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    color: #333;
    text-align: center;
}

.flavor-grid label:hover {
    background-color: #e9ecef;
    border-color: #3498db;
}

.flavor-grid input[type="checkbox"] {
    display: none;
}

.flavor-grid input[type="checkbox"]:checked + label {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.selected-flavors {
    font-size: 16px;
    color: #666;
}

.add-hookah-btn {
    padding: 12px 30px;
    background-color: #2ecc71;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.add-hookah-btn:hover {
    background-color: #27ae60;
}

.add-hookah-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.table-customer-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.table-customer-info span {
    font-size: 16px;
    font-weight: bold;
}

/* POS Button */
.pos-button {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 15px;
    background-color: #FF9800;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.pos-button:hover {
    background-color: #F57C00;
}

/* Improved Tablet-Friendly Virtual Keyboard Styles */
.virtual-keyboard {
    background-color: rgba(240, 240, 240, 0.95);
    border: 1px solid #ccc;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    position: fixed;
    width: auto;
    max-width: 600px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.virtual-keyboard.dragging {
    opacity: 0.8;
    cursor: move;
}

.virtual-keyboard.minimized {
    width: 200px;
    height: auto;
    opacity: 0.8;
}

.virtual-keyboard:hover {
    opacity: 1;
}

/* Keyboard Header */
.keyboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #3498db;
    color: white;
    padding: 8px 12px;
    cursor: move;
    user-select: none;
}

.keyboard-title {
    font-weight: bold;
    font-size: 14px;
}

.keyboard-controls {
    display: flex;
    gap: 8px;
}

.keyboard-control-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.keyboard-control-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.close-btn:hover {
    background-color: #e74c3c;
}

/* Keyboard Body */
.keyboard-body {
    padding: 10px;
}

.keyboard-row {
    display: flex;
    justify-content: center;
    margin-bottom: 6px;
}

.keyboard-key {
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 6px;
    margin: 0 3px;
    padding: 10px 12px;
    font-size: 16px;
    min-width: 36px;
    text-align: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.keyboard-key:hover {
    background-color: #e0e0e0;
}

.keyboard-key:active {
    background-color: #d0d0d0;
    transform: translateY(1px);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

.space-key {
    min-width: 150px;
}

.backspace-key {
    min-width: 70px;
    background-color: #f8f8f8;
}

.done-key {
    background-color: #4CAF50;
    color: white;
    min-width: 70px;
    font-weight: bold;
}

.shift-key {
    background-color: #e0e0e0;
    min-width: 60px;
}

.shift-key.active {
    background-color: #3498db;
    color: white;
}