/* Settings Page Styles */

/* Secondary navigation for back button */
.secondary-nav {
    margin-top: 30px;
}

.secondary-nav ul {
    display: flex;
    justify-content: center;
}

/* Custom button colors for settings page */
#whatsapp-btn {
    background-color: #25D366; /* WhatsApp green */
}

#whatsapp-btn:hover {
    background-color: #128C7E; /* Darker WhatsApp green on hover */
}

#accounting-btn {
    background: linear-gradient(135deg, #F5F5F5 0%, #E0E0E0 40%, #C0C0C0 60%, #E0E0E0 100%); /* Silver gradient */
    color: #333; /* Darker text for better contrast on silver */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
    box-shadow: 0 3px 10px rgba(192, 192, 192, 0.4);
    border: none;
    transition: all 0.3s ease;
}

#accounting-btn:hover {
    background: linear-gradient(135deg, #E0E0E0 0%, #C0C0C0 40%, #A9A9A9 60%, #C0C0C0 100%); /* Darker silver gradient on hover */
    box-shadow: 0 5px 15px rgba(192, 192, 192, 0.6);
    transform: translateY(-2px);
}

#display-btn {
    background-color: #E6E6FA; /* Light lavender */
    color: #333; /* Darker text for better contrast on light lavender */
}

#display-btn:hover {
    background-color: #D8BFD8; /* Slightly darker lavender (thistle) on hover */
}

#backup-btn {
    background-color: #FF0000; /* Bright red */
}

#backup-btn:hover {
    background-color: #CC0000; /* Darker red on hover */
}

#items-btn {
    background: linear-gradient(135deg, #F7CAC9 0%, #E7A9AB 40%, #B76E79 60%, #E7A9AB 80%, #F7CAC9 100%); /* Shiny rose gold gradient */
    color: #fff;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    box-shadow: 0 3px 10px rgba(183, 110, 121, 0.4);
    border: none;
    transition: all 0.3s ease;
}

#items-btn:hover {
    background: linear-gradient(135deg, #F7CAC9 0%, #E7A9AB 20%, #B76E79 50%, #E7A9AB 80%, #F7CAC9 100%); /* Adjusted gradient on hover */
    box-shadow: 0 5px 15px rgba(183, 110, 121, 0.6);
    transform: translateY(-2px);
}

#reports-btn {
    background-color: #40E0D0; /* Turquoise */
}

#reports-btn:hover {
    background-color: #00CED1; /* Darker turquoise on hover */
}

#bill-archive-btn {
    background: linear-gradient(135deg, #FFD700 0%, #FFC800 40%, #FFDF00 60%, #FFD700 100%); /* Bright golden gradient */
    color: #333; /* Darker text for better contrast on gold */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
    box-shadow: 0 3px 10px rgba(255, 215, 0, 0.4);
    border: none;
    transition: all 0.3s ease;
}

#bill-archive-btn:hover {
    background: linear-gradient(135deg, #FFDF00 0%, #FFD700 40%, #FFC800 60%, #FFDF00 100%); /* Adjusted bright golden gradient on hover */
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.6);
    transform: translateY(-2px);
}

#back-btn {
    background-color: #FF69B4; /* Hot pink */
}

#back-btn:hover {
    background-color: #FF1493; /* Deeper pink on hover */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    nav ul {
        grid-template-columns: 1fr;
    }

    .button {
        padding: 25px 20px;
        font-size: 20px;
    }
}
