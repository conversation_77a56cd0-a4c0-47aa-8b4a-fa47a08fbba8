<!DOCTYPE html>
<html>
<head>
    <title>Paradiz App Preview - Out of Store</title>
    <style>
        body { font-family: Arial, sans-serif; display: flex; justify-content: center; padding: 20px; background-color: #f5f5f5; }
        .phone { width: 375px; height: 667px; background: white; border-radius: 30px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15); position: relative; }
        .header { height: 60px; background: #6B46C1; color: white; display: flex; align-items: center; justify-content: center; }
        .content { padding: 20px; height: calc(100% - 120px); overflow-y: auto; }
        .card { background: white; border-radius: 8px; padding: 16px; margin-bottom: 16px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .button { background: #6B46C1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-weight: bold; cursor: pointer; }
        .menu-item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
        .price { color: #6B46C1; font-weight: bold; }
        .nav { position: absolute; bottom: 0; width: 100%; display: flex; background: white; box-shadow: 0 -2px 10px rgba(0,0,0,0.05); }
        .nav-item { flex: 1; text-align: center; padding: 10px; color: #A0AEC0; cursor: pointer; }
        .nav-item.active { color: #6B46C1; }
        .status-banner { background-color: #F56565; color: white; text-align: center; padding: 8px; font-size: 14px; }
        .demo-controls { margin-top: 20px; text-align: center; }
        .featured-image { width: 100%; height: 120px; object-fit: cover; border-radius: 8px 8px 0 0; }
        .promo-banner { background: linear-gradient(135deg, #6B46C1, #38B2AC); color: white; padding: 12px; border-radius: 8px; margin-bottom: 16px; }
        .icon { margin-right: 8px; }
        .menu-category { display: inline-block; background: #EDF2F7; color: #4A5568; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-bottom: 8px; }

        /* Notification styles */
        .notification-bell { position: absolute; top: 15px; right: 20px; font-size: 24px; color: white; cursor: pointer; }
        .notification-badge { position: absolute; top: -5px; right: -5px; background-color: #E53E3E; color: white; border-radius: 50%; width: 18px; height: 18px; font-size: 12px; display: flex; align-items: center; justify-content: center; }
        .notification-panel { position: absolute; top: 60px; right: 10px; width: 300px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 100; display: none; }
        .notification-header { padding: 12px 16px; border-bottom: 1px solid #E2E8F0; display: flex; justify-content: space-between; align-items: center; }
        .notification-title { font-weight: bold; color: #2D3748; }
        .notification-settings { color: #6B46C1; font-size: 12px; cursor: pointer; }
        .notification-list { max-height: 300px; overflow-y: auto; }
        .notification-item { padding: 12px 16px; border-bottom: 1px solid #E2E8F0; cursor: pointer; }
        .notification-item:hover { background-color: #F7FAFC; }
        .notification-item.unread { border-left: 3px solid #6B46C1; }
        .notification-item-title { font-weight: bold; margin-bottom: 4px; color: #2D3748; }
        .notification-item-desc { font-size: 14px; color: #4A5568; }
        .notification-item-time { font-size: 12px; color: #718096; margin-top: 4px; }
        .notification-footer { padding: 12px 16px; text-align: center; color: #6B46C1; font-size: 14px; cursor: pointer; }

        /* Event notification styles */
        .event-card { background: linear-gradient(135deg, #6B46C1, #805AD5); color: white; border-radius: 8px; padding: 16px; margin-bottom: 16px; position: relative; }
        .event-date { font-size: 14px; margin-bottom: 8px; }
        .event-title { font-size: 18px; font-weight: bold; margin-bottom: 8px; }
        .event-desc { font-size: 14px; margin-bottom: 12px; }
        .event-button { background-color: white; color: #6B46C1; border: none; padding: 8px 16px; border-radius: 20px; font-weight: bold; font-size: 14px; cursor: pointer; }
        .event-reminder { display: flex; align-items: center; margin-top: 12px; font-size: 14px; }
        .event-reminder-icon { margin-right: 8px; }
    </style>
</head>
<body>
    <div>
        <div class="phone">
            <div class="status-banner">
                You are viewing the app as an out-of-store customer
            </div>
            <div class="header">
                <h2>Paradiz Hookah Lounge</h2>
                <div class="notification-bell" onclick="toggleNotifications()">
                    🔔
                    <div class="notification-badge">3</div>
                </div>
            </div>

            <div class="notification-panel" id="notificationPanel">
                <div class="notification-header">
                    <div class="notification-title">Notifications</div>
                    <div class="notification-settings">Settings</div>
                </div>
                <div class="notification-list">
                    <div class="notification-item unread" onclick="showEventDetails()">
                        <div class="notification-item-title">Live Music Night - This Friday!</div>
                        <div class="notification-item-desc">Join us for live music and special hookah flavors</div>
                        <div class="notification-item-time">2 hours ago</div>
                    </div>
                    <div class="notification-item unread">
                        <div class="notification-item-title">Your Reservation is Confirmed</div>
                        <div class="notification-item-desc">Table for 4 on Saturday, 8:00 PM</div>
                        <div class="notification-item-time">Yesterday</div>
                    </div>
                    <div class="notification-item unread">
                        <div class="notification-item-title">New Flavor Alert!</div>
                        <div class="notification-item-desc">Try our new Blueberry Mint hookah flavor</div>
                        <div class="notification-item-time">2 days ago</div>
                    </div>
                    <div class="notification-item">
                        <div class="notification-item-title">Weekend Special</div>
                        <div class="notification-item-desc">Get 15% off on all food items this weekend</div>
                        <div class="notification-item-time">5 days ago</div>
                    </div>
                </div>
                <div class="notification-footer">
                    Mark all as read
                </div>
            </div>

            <div class="content" id="mainContent">
                <div class="promo-banner">
                    <h3 style="margin-top: 0;">Happy Hour Special!</h3>
                    <p style="margin-bottom: 0;">20% off all hookah flavors from 4-7pm daily</p>
                </div>

                <div class="card">
                    <h3>Welcome to Paradiz!</h3>
                    <p>Browse our menu, make reservations, or visit us to place an order directly from your table.</p>
                </div>

                <div class="card">
                    <h3>At Paradiz Hookah Lounge?</h3>
                    <p>Scan the QR code at your table to place an order directly from your phone.</p>
                    <button class="button" onclick="window.location.href='qr-scan.html'">
                        <span class="icon">📷</span>Scan QR Code
                    </button>
                </div>

                <h3>Featured Items</h3>
                <div class="card">
                    <img src="https://placehold.co/600x400/6B46C1/FFFFFF/png?text=Hookah" class="featured-image" alt="Featured hookah">
                    <div style="padding: 12px;">
                        <span class="menu-category">Premium</span>
                        <div class="menu-item">
                            <div>
                                <h4 style="margin-bottom: 4px;">Double Apple Hookah</h4>
                                <p style="margin-top: 4px; color: #718096;">Classic double apple flavor</p>
                            </div>
                            <p class="price">$25.99</p>
                        </div>
                        <div class="menu-item">
                            <div>
                                <h4 style="margin-bottom: 4px;">Mint Hookah</h4>
                                <p style="margin-top: 4px; color: #718096;">Refreshing mint flavor</p>
                            </div>
                            <p class="price">$25.99</p>
                        </div>
                        <div class="menu-item" style="border-bottom: none;">
                            <div>
                                <h4 style="margin-bottom: 4px;">Hummus Plate</h4>
                                <p style="margin-top: 4px; color: #718096;">Served with warm pita bread</p>
                            </div>
                            <p class="price">$8.99</p>
                        </div>
                    </div>
                </div>

                <h3>Make a Reservation</h3>
                <div class="card">
                    <p>Reserve a table for your next visit!</p>
                    <button class="button">
                        <span class="icon">📅</span>Book Now
                    </button>
                </div>
            </div>

            <div id="eventDetails" style="display: none; padding: 20px; height: calc(100% - 120px); overflow-y: auto;">
                <button class="button" style="background-color: transparent; color: #6B46C1; padding: 8px 0;" onclick="hideEventDetails()">
                    <span class="icon">←</span>Back to Home
                </button>

                <div class="event-card">
                    <div class="event-date">Friday, April 19, 2025 • 8:00 PM</div>
                    <div class="event-title">Live Music Night</div>
                    <div class="event-desc">Join us for an unforgettable evening featuring live music by local artist Alex Rivera. Enjoy special hookah flavors and cocktails created just for this event.</div>
                    <button class="event-button">RSVP Now</button>
                    <div class="event-reminder">
                        <span class="event-reminder-icon">🔔</span> Remind me 1 day before
                    </div>
                </div>

                <div class="card">
                    <h3>Event Details</h3>
                    <p><strong>Date & Time:</strong> Friday, April 19, 2025 at 8:00 PM</p>
                    <p><strong>Location:</strong> Paradiz Hookah Lounge - Main Area</p>
                    <p><strong>Cover Charge:</strong> $10 per person (includes one drink)</p>
                    <p><strong>Special Menu:</strong> Exclusive hookah flavors and cocktails available only during this event</p>
                </div>

                <div class="card">
                    <h3>About the Artist</h3>
                    <p>Alex Rivera is a talented local musician known for his unique blend of acoustic covers and original compositions. His music creates the perfect ambiance for a relaxing evening.</p>
                </div>

                <div class="card">
                    <h3>Reserve a Table</h3>
                    <p>Tables are filling up fast! Reserve yours now to guarantee seating.</p>
                    <button class="button">
                        <span class="icon">📅</span>Reserve for this Event
                    </button>
                </div>
            </div>

            <div class="nav">
                <div class="nav-item active">Home</div>
                <div class="nav-item">Menu</div>
                <div class="nav-item">Reserve</div>
                <div class="nav-item">Profile</div>
            </div>
        </div>

        <div class="demo-controls">
            <p>Switch between experiences:</p>
            <button class="button" style="background-color: #F56565;" disabled>Out-of-Store View</button>
            <button class="button" onclick="window.location.href='table-order.html'">In-Store View (After QR Scan)</button>
        </div>
    </div>

    <script>
        function toggleNotifications() {
            const panel = document.getElementById('notificationPanel');
            if (panel.style.display === 'block') {
                panel.style.display = 'none';
            } else {
                panel.style.display = 'block';
            }
        }

        function showEventDetails() {
            document.getElementById('mainContent').style.display = 'none';
            document.getElementById('eventDetails').style.display = 'block';
            document.getElementById('notificationPanel').style.display = 'none';
        }

        function hideEventDetails() {
            document.getElementById('mainContent').style.display = 'block';
            document.getElementById('eventDetails').style.display = 'none';
        }
    </script>
</body>
</html>