import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Provider as PaperProvider, DefaultTheme } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import * as SplashScreen from 'expo-splash-screen';
// import * as Font from 'expo-font';
import * as SecureStore from 'expo-secure-store';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import MenuScreen from './src/screens/MenuScreen';
import MenuDetailScreen from './src/screens/MenuDetailScreen';
import ReservationScreen from './src/screens/ReservationScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import OrderScreen from './src/screens/OrderScreen';
import TableOrderScreen from './src/screens/TableOrderScreen';
import LoginScreen from './src/screens/LoginScreen';
import RegisterScreen from './src/screens/RegisterScreen';
import SplashLoadingScreen from './src/screens/SplashLoadingScreen';
import NotificationScreen from './src/screens/NotificationScreen';
import EventDetailScreen from './src/screens/EventDetailScreen';

// Import context
import { AuthProvider } from './src/context/AuthContext';
import { NotificationProvider } from './src/context/NotificationContext';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

// Define theme
const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#6B46C1', // Dark Lavender
    accent: '#38B2AC', // Turquoise
    background: '#F7FAFC',
    text: '#2D3748',
    placeholder: '#A0AEC0',
    surface: '#FFFFFF',
  },
};

// Create navigators
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Tab Navigator
function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Menu') {
            iconName = focused ? 'restaurant' : 'restaurant-outline';
          } else if (route.name === 'Reservation') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#6B46C1',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Menu" component={MenuScreen} />
      <Tab.Screen name="Reservation" component={ReservationScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

export default function App() {
  const [appIsReady, setAppIsReady] = useState(false);
  const [initialRoute, setInitialRoute] = useState('Splash');

  useEffect(() => {
    async function prepare() {
      try {
        // Font loading removed temporarily
        // await Font.loadAsync({
        //   'poppins-regular': require('./assets/fonts/Poppins-Regular.ttf'),
        //   'poppins-medium': require('./assets/fonts/Poppins-Medium.ttf'),
        //   'poppins-bold': require('./assets/fonts/Poppins-Bold.ttf'),
        // });

        // Check if user is logged in
        const userToken = await SecureStore.getItemAsync('userToken');
        if (userToken) {
          setInitialRoute('Main');
        } else {
          setInitialRoute('Login');
        }

        // Artificially delay for a smoother splash screen
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (e) {
        console.warn(e);
      } finally {
        // Tell the application to render
        setAppIsReady(true);
      }
    }

    prepare();
  }, []);

  const onLayoutRootView = React.useCallback(async () => {
    if (appIsReady) {
      // This tells the splash screen to hide immediately
      await SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <SafeAreaProvider onLayout={onLayoutRootView}>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <NotificationProvider>
            <NavigationContainer>
              <Stack.Navigator initialRouteName={initialRoute}>
                <Stack.Screen
                  name="Splash"
                  component={SplashLoadingScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="Login"
                  component={LoginScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="Register"
                  component={RegisterScreen}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="Main"
                  component={TabNavigator}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="MenuDetail"
                  component={MenuDetailScreen}
                  options={{
                    title: 'Menu Item',
                    headerStyle: {
                      backgroundColor: '#6B46C1',
                    },
                    headerTintColor: '#fff',
                  }}
                />
                <Stack.Screen
                  name="Order"
                  component={OrderScreen}
                  options={{
                    title: 'My Order',
                    headerStyle: {
                      backgroundColor: '#6B46C1',
                    },
                    headerTintColor: '#fff',
                  }}
                />
                <Stack.Screen
                  name="TableOrder"
                  component={TableOrderScreen}
                  options={{
                    title: 'Table Order',
                    headerStyle: {
                      backgroundColor: '#6B46C1',
                    },
                    headerTintColor: '#fff',
                  }}
                />
                <Stack.Screen
                  name="Notifications"
                  component={NotificationScreen}
                  options={{
                    title: 'Notifications',
                    headerStyle: {
                      backgroundColor: '#6B46C1',
                    },
                    headerTintColor: '#fff',
                  }}
                />
                <Stack.Screen
                  name="EventDetails"
                  component={EventDetailScreen}
                  options={{
                    title: 'Event Details',
                    headerStyle: {
                      backgroundColor: '#6B46C1',
                    },
                    headerTintColor: '#fff',
                  }}
                />
              </Stack.Navigator>
            </NavigationContainer>
          </NotificationProvider>
        </AuthProvider>
        <StatusBar style="auto" />
      </PaperProvider>
    </SafeAreaProvider>
  );
}
