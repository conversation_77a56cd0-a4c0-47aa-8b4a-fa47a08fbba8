document.addEventListener('DOMContentLoaded', function() {
    // Update current time every second
    function updateTime() {
        const now = new Date();
        document.getElementById('current-time').textContent = now.toLocaleTimeString();
    }
    setInterval(updateTime, 1000);
    updateTime();

    // Station toggle functionality
    const stationButtons = document.querySelectorAll('.station-toggles button');
    stationButtons.forEach(button => {
        button.addEventListener('click', function() {
            stationButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.dataset.category;
            const orders = document.querySelectorAll('.order-card');
            
            orders.forEach(order => {
                if (category === 'all') {
                    order.style.display = 'block';
                } else {
                    const orderItems = order.querySelectorAll('.item');
                    let hasItems = false;
                    
                    orderItems.forEach(item => {
                        if (item.classList.contains(category)) {
                            hasItems = true;
                        }
                    });
                    
                    order.style.display = hasItems ? 'block' : 'none';
                }
            });
        });
    });

    // Order completion handling
    document.querySelectorAll('.complete-btn').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.orderid;
            fetch(`/complete_order/${orderId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.closest('.order-card').remove();
                }
            });
        });
    });

    // Flash new orders
    const newOrders = document.querySelectorAll('.order-card:first-child');
    newOrders.forEach(order => {
        order.style.animation = 'flash 3s';
    });
});