document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const backupBtn = document.getElementById('backup-btn');
    const backupModal = document.getElementById('backup-modal');
    const closeBtn = document.querySelector('.close');
    const createBackupBtn = document.getElementById('create-backup-btn');
    const viewBackupsBtn = document.getElementById('view-backups-btn');
    const lastBackupTime = document.getElementById('last-backup-time');
    const nextBackupTime = document.getElementById('next-backup-time');
    const backupStorage = document.getElementById('backup-storage');

    // Initialize backup status if elements exist
    if (lastBackupTime && nextBackupTime && backupStorage) {
        updateBackupStatus();
    }

    // Backup button - Redirect to backup manager page
    if (backupBtn) {
        backupBtn.addEventListener('click', function() {
            window.location.href = '../backup-manager/index.html';
        });
    }

    // Create backup button
    if (createBackupBtn) {
        createBackupBtn.addEventListener('click', function() {
            // Show loading state
            const originalText = createBackupBtn.innerHTML;
            createBackupBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Backup...';
            createBackupBtn.disabled = true;

            // Call the enhanced backup API
            fetch('http://localhost:5000/api/backup?type=manual', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Restore button state
                createBackupBtn.innerHTML = originalText;
                createBackupBtn.disabled = false;

                if (data.success) {
                    alert('Backup created successfully!');
                    updateBackupStatus(); // Update the status after successful backup
                } else {
                    throw new Error(data.error || 'Unknown error');
                }
            })
            .catch(error => {
                // Restore button state
                createBackupBtn.innerHTML = originalText;
                createBackupBtn.disabled = false;

                console.error('Error creating backup:', error);
                alert('Failed to create backup: ' + error.message);
            });
        });
    }

    // View backups button
    if (viewBackupsBtn) {
        viewBackupsBtn.addEventListener('click', function() {
            window.location.href = '../backup-viewer/index.html';
        });
    }

    // Display button
    const displayBtn = document.getElementById('display-btn');
    if (displayBtn) {
        displayBtn.addEventListener('click', function() {
            window.location.href = 'display-settings/index.html';
        });
    }

    // Reports button
    const reportsBtn = document.getElementById('reports-btn');
    if (reportsBtn) {
        reportsBtn.addEventListener('click', function() {
            window.location.href = '../reports/index.html';
        });
    }

    // Pending Bills button
    const pendingBillsBtn = document.getElementById('pending-bills-btn');
    if (pendingBillsBtn) {
        pendingBillsBtn.addEventListener('click', function() {
            window.location.href = '../pending-bills/index.html';
        });
    }

    // Items button
    const itemsBtn = document.getElementById('items-btn');
    if (itemsBtn) {
        itemsBtn.addEventListener('click', function() {
            window.location.href = '../items/index.html';
        });
    }

    // WhatsApp button
    const whatsappBtn = document.getElementById('whatsapp-btn');
    if (whatsappBtn) {
        whatsappBtn.addEventListener('click', function() {
            window.location.href = 'whatsapp-new/index.html';
        });
    }

    // Printer button
    const printerBtn = document.getElementById('printer-btn');
    if (printerBtn) {
        printerBtn.addEventListener('click', function() {
            window.location.href = 'printer-settings/index.html';
        });
    }

    // Accounting button
    const accountingBtn = document.getElementById('accounting-btn');
    if (accountingBtn) {
        accountingBtn.addEventListener('click', function() {
            window.location.href = 'accounting/index.html';
        });
    }

    // Bill Archive button
    const billArchiveBtn = document.getElementById('bill-archive-btn');
    if (billArchiveBtn) {
        billArchiveBtn.addEventListener('click', function() {
            window.location.href = '../bills.html';
        });
    }

    // Back button
    const backBtn = document.getElementById('back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            window.location.href = '../index.html';
        });
    }

    // Apply saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }

    // Function to update backup status information
    function updateBackupStatus() {
        // Make sure the elements exist
        if (!lastBackupTime || !nextBackupTime || !backupStorage) {
            console.log('Backup status elements not found, skipping update');
            return;
        }

        // Fetch the latest backup information
        fetch('http://localhost:5000/api/backups')
            .then(response => response.json())
            .then(backups => {
                // Update last backup time
                if (backups && backups.length > 0) {
                    // Sort backups by timestamp (newest first)
                    backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                    const latestBackup = backups[0];

                    // Format the date for display
                    const backupDate = new Date(latestBackup.timestamp);
                    const formattedDate = backupDate.toLocaleString();
                    lastBackupTime.textContent = formattedDate;
                } else {
                    lastBackupTime.textContent = 'Never';
                }

                // Set next backup time (3 AM by default)
                const now = new Date();
                const nextBackup = new Date(now);
                nextBackup.setHours(3, 0, 0, 0);
                if (nextBackup < now) {
                    nextBackup.setDate(nextBackup.getDate() + 1); // Tomorrow at 3 AM
                }
                nextBackupTime.textContent = nextBackup.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                // Check for external storage
                fetch('http://localhost:5000/api/backup-storage')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const storage = data.storage;
                            let storageText = storage.primary;

                            if (storage.external) {
                                storageText += ' + ' + storage.external;
                            }

                            if (storage.cloud) {
                                storageText += ' + ' + storage.cloud;
                            }

                            backupStorage.textContent = storageText;
                        } else {
                            backupStorage.textContent = 'Local';
                        }
                    })
                    .catch(() => {
                        // If the endpoint doesn't exist, just use default value
                        backupStorage.textContent = 'Local';
                    });
            })
            .catch(error => {
                console.error('Error fetching backup status:', error);
                if (lastBackupTime) {
                    lastBackupTime.textContent = 'Unknown';
                }
            });
    }
});