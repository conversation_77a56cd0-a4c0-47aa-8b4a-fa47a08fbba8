<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accounting - Paradiz <PERSON></title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles.css">
    <script src="../../theme.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header-with-back">
            <button class="back-button" id="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1><i class="fas fa-calculator"></i> Accounting</h1>
        </div>

        <div class="settings-section">
            <h2>Financial Overview</h2>
            <p class="settings-description">View and manage your financial data.</p>
            
            <div class="accounting-summary">
                <div class="summary-card">
                    <h3>Today's Sales</h3>
                    <div class="summary-value" id="today-sales">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>This Month</h3>
                    <div class="summary-value" id="month-sales">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>Outstanding</h3>
                    <div class="summary-value" id="outstanding-amount">$0.00</div>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <h2>Financial Actions</h2>
            <p class="settings-description">Manage your financial records and transactions.</p>
            
            <div class="action-buttons">
                <button id="export-btn" class="action-btn">
                    <i class="fas fa-file-export"></i> Export Financial Data
                </button>
                <button id="reconcile-btn" class="action-btn">
                    <i class="fas fa-balance-scale"></i> Reconcile Accounts
                </button>
                <button id="expenses-btn" class="action-btn">
                    <i class="fas fa-receipt"></i> Record Expenses
                </button>
            </div>
        </div>

        <div class="settings-section">
            <h2>Recent Transactions</h2>
            <p class="settings-description">View your most recent financial transactions.</p>
            
            <div class="transactions-container">
                <table class="transactions-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Amount</th>
                            <th>Type</th>
                        </tr>
                    </thead>
                    <tbody id="transactions-body">
                        <tr>
                            <td colspan="4" class="loading">Loading transactions...</td>
                        </tr>
                    </tbody>
                </table>
                <div class="view-all">
                    <button id="view-all-btn" class="secondary-btn">
                        <i class="fas fa-list"></i> View All Transactions
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
