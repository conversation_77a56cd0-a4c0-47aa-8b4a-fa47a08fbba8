// Track the currently selected table
let selectedTable = null;
let currentOrders = []; // Store the currently displayed orders
let selectedItem = null; // Track the selected item for splitting
let lastAction = null; // Track the last split action for undo

// Check URL parameters
const urlParams = new URLSearchParams(window.location.search);
const isPaymentRedirect = urlParams.get('payment') === 'true';
const refreshParam = urlParams.get('refresh');

// If we have a refresh parameter, clear it from the URL to prevent endless refreshes
if (refreshParam) {
    // Remove the refresh parameter from the URL without reloading the page
    const newUrl = window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // If we're coming from the payment button, redirect to the first table with orders
    if (isPaymentRedirect) {
        // We'll fetch orders and redirect to the payment page for the first table with orders
        fetch('/orders')
            .then(response => response.json())
            .then(data => {
                if (data && data.length > 0) {
                    // Find the first table with orders
                    const firstOrder = data[0];
                    const tableNumber = firstOrder.tableNumber;
                    const customerName = firstOrder.customerName;

                    // Redirect to the payment page with this order
                    const orderData = encodeURIComponent(JSON.stringify(firstOrder));
                    window.location.href = `../payment/index.html?orderData=${orderData}`;
                } else {
                    // No orders found, just stay on the POS page
                    alert('No active orders found. Please select a table first.');
                }
            })
            .catch(error => {
                console.error('Error fetching orders:', error);
                alert('Error loading orders. Please try again.');
            });
    }
});

// Load split items from localStorage or initialize empty object
let splitItems = {};
try {
    const savedSplitItems = localStorage.getItem('splitItems');
    if (savedSplitItems) {
        splitItems = JSON.parse(savedSplitItems);
        console.log('Loaded split items from localStorage:', Object.keys(splitItems).length);
    }
} catch (error) {
    console.error('Error loading split items from localStorage:', error);
    splitItems = {};
}

// Function to save split items to localStorage
function saveSplitItems() {
    try {
        localStorage.setItem('splitItems', JSON.stringify(splitItems));
        console.log('Saved split items to localStorage:', Object.keys(splitItems).length);
    } catch (error) {
        console.error('Error saving split items to localStorage:', error);
    }
}

// Function to format price to 2 decimal places
function formatPrice(price) {
    return Number(price).toFixed(2);
}

// Initialize the app
document.addEventListener('DOMContentLoaded', function () {
    setupTableButtons(); // Initialize table buttons
    displayOrders(); // Display orders when the page loads

    // Add clear button event listener
    const clearTableBtn = document.getElementById('clear-table-btn');
    if (clearTableBtn) {
        clearTableBtn.addEventListener('click', clearTable);
        console.log('Clear button listener added');
    }

    // Add refresh orders button event listener
    const refreshOrdersBtn = document.getElementById('refresh-orders-btn');
    if (refreshOrdersBtn) {
        refreshOrdersBtn.addEventListener('click', () => {
            window.location.reload();
        });
        console.log('Refresh orders button listener added');
    }

    // Add backup button event listener
    const backupBtn = document.getElementById('backup-btn');
    if (backupBtn) {
        backupBtn.addEventListener('click', () => {
            // Redirect to food-drinks display page
            window.location.href = '../kitchen-displays/food-drinks.html';
        });
        console.log('Food-Drink button listener added');
    }

    // Add backup viewer button event listener
    const backupViewerBtn = document.getElementById('backup-viewer-btn');
    if (backupViewerBtn) {
        backupViewerBtn.addEventListener('click', () => {
            // Redirect to main page
            window.location.href = '../index.html';
        });
        console.log('Main button listener added');
    }

    // Backup manager button removed

    // Add back to ordering button event listener
    const backToOrderingBtn = document.getElementById('back-to-ordering-btn');
    if (backToOrderingBtn) {
        backToOrderingBtn.addEventListener('click', () => {
            window.location.href = '../tablet/index.html';
        });
        console.log('Back to ordering button listener added');
    }

    // Add event listeners for split modal
    setupSplitModal();

    // Add event listener for Pay Table button
    document.getElementById('pay-table-btn').addEventListener('click', () => {
        // Redirect to hookah display page
        window.location.href = '../kitchen-displays/hookah.html';
    });
});

// Setup table buttons
function setupTableButtons() {
    const tableButtonsRow1 = document.getElementById('table-buttons-row1');
    const tableButtonsRow2 = document.getElementById('table-buttons-row2');

    // Generate buttons for tables 1 to 8
    for (let i = 1; i <= 8; i++) {
        const button = document.createElement('button');
        button.className = 'table-btn';
        button.innerHTML = `<i class="fas fa-table"></i> Table ${i}`;
        button.addEventListener('click', () => pullUpTable(i.toString()));
        tableButtonsRow1.appendChild(button);
    }

    // Generate buttons for tables 9 to 15
    for (let i = 9; i <= 15; i++) {
        const button = document.createElement('button');
        button.className = 'table-btn';
        button.innerHTML = `<i class="fas fa-table"></i> Table ${i}`;
        button.addEventListener('click', () => pullUpTable(i.toString()));
        tableButtonsRow2.appendChild(button);
    }
}

// Pull Up Table
function pullUpTable(tableNumber) {
    selectedTable = tableNumber; // Update the selected table
    console.log('Selected table:', selectedTable);
    displayOrders(); // Refresh the table summary and update button colors
}

// This function is no longer needed as updateTableButtonColors handles the highlighting
// Keeping it for backward compatibility
function highlightSelectedTableButton(tableNumber) {
    // Do nothing - updateTableButtonColors will handle this
}

// Display Orders
async function displayOrders() {
    const customerBills = document.getElementById('customer-bills');
    customerBills.innerHTML = '';

    try {
        console.log('Fetching orders from server...');
        const response = await fetch('http://localhost:5000/orders');
        console.log('Response status:', response.status);
        const orders = await response.json();
        console.log('Orders received:', orders);

        // Always update table button colors, even if no orders are found
        updateTableButtonColors(orders);

        if (orders.length === 0) {
            console.log('No orders found');
            customerBills.innerHTML = `<p>No orders found.</p>`;
            return;
        }

        // Filter orders for the selected table
        const tableOrders = selectedTable ? orders.filter(order => order.tableNumber === selectedTable) : orders;
        console.log('Filtered orders for table:', tableOrders);

        if (tableOrders.length === 0) {
            console.log('No orders found for selected table');
            customerBills.innerHTML = `<p>No orders found for Table ${selectedTable}.</p>`;
            return;
        }

        // Group orders by table number and customer name
        const groupedOrders = {};
        tableOrders.forEach(order => {
            // Create a case-insensitive key for grouping
            const key = `${order.tableNumber}-${order.customerName.toLowerCase()}`;

            // Check if we already have an order with this table and customer name (case-insensitive)
            const existingKey = Object.keys(groupedOrders).find(k =>
                k.toLowerCase() === key
            );

            if (existingKey) {
                // Use the existing key to add items to the existing group
                const existingGroup = groupedOrders[existingKey];

                // If this order has an ID and the group doesn't, use this order's ID
                if (order.id && !existingGroup.id) {
                    existingGroup.id = order.id;
                }

                // Add items to the grouped order
                order.items.forEach(item => {
                    const existingItem = existingGroup.items.find(i =>
                        i.name === item.name &&
                        Math.abs(i.price - item.price) < 0.01
                    );

                    if (existingItem) {
                        existingItem.quantity = (existingItem.quantity || 1) + (item.quantity || 1);
                    } else {
                        existingGroup.items.push({
                            ...item,
                            quantity: item.quantity || 1
                        });
                    }
                });

                // If any order is unpaid, mark the entire group as unpaid
                if (!order.paid) {
                    existingGroup.paid = false;
                }
            } else {
                // Create a new group
                groupedOrders[key] = {
                    id: order.id, // Include the order ID
                    tableNumber: order.tableNumber,
                    customerName: order.customerName,
                    items: [],
                    paid: true // Will be set to false if any order is unpaid
                };

                // Add items to the new group
                order.items.forEach(item => {
                    groupedOrders[key].items.push({
                        ...item,
                        quantity: item.quantity || 1
                    });
                });

                // If any order is unpaid, mark the entire group as unpaid
                if (!order.paid) {
                    groupedOrders[key].paid = false;
                }
            }
        });

        // Calculate total bill amount
        let totalBillAmount = 0;
        Object.values(groupedOrders).forEach(groupedOrder => {
            const total = groupedOrder.items.reduce((total, item) => total + (item.price * (item.quantity || 1)), 0);
            totalBillAmount += total;
        });

        // Update total bill amount display
        const totalBillAmountElement = document.getElementById('table-total-amount');
        if (totalBillAmountElement) {
            totalBillAmountElement.textContent = `$${formatPrice(totalBillAmount)}`;
        }

        // Add click handler for total bill button
        const totalBillBtn = document.querySelector('.total-bill-btn');
        if (totalBillBtn) {
            totalBillBtn.onclick = () => {
                if (selectedTable) {
                    // Get all unpaid orders for the selected table
                    const unpaidOrders = Object.values(groupedOrders).filter(order => !order.paid);
                    if (unpaidOrders.length > 0) {
                        // Combine all items from unpaid orders
                        const combinedItems = unpaidOrders.reduce((items, order) => {
                            order.items.forEach(item => {
                                const existingItem = items.find(i =>
                                    i.name === item.name &&
                                    Math.abs(i.price - item.price) < 0.01
                                );
                                if (existingItem) {
                                    existingItem.quantity = (existingItem.quantity || 1) + (item.quantity || 1);
                                } else {
                                    items.push({...item});
                                }
                            });
                            return items;
                        }, []);

                        // Create order data for payment
                        const orderData = {
                            tableNumber: selectedTable,
                            customerName: `Table ${selectedTable}`,
                            items: combinedItems
                        };

                        // Convert order data to URL-safe string
                        const orderJson = encodeURIComponent(JSON.stringify(orderData));

                        // Open new payment page with order data
                        window.location.href = `../payment/index.html?orderData=${orderJson}`;
                    } else {
                        alert('No unpaid orders found for this table');
                    }
                } else {
                    alert('Please select a table first');
                }
            };
        }

        // Display grouped orders
        Object.values(groupedOrders).forEach(groupedOrder => {
            const orderDiv = document.createElement('div');
            orderDiv.className = `customer-bill ${groupedOrder.paid ? 'paid' : ''}`;
            const total = groupedOrder.items.reduce((total, item) => total + (item.price * (item.quantity || 1)), 0);
            orderDiv.innerHTML = `
                <h4>Table ${groupedOrder.tableNumber} - ${groupedOrder.customerName}</h4>
                <ul>
                    ${groupedOrder.items.map(item => `
                        <li>
                            <button class="item-button" data-item="${item.name}" data-price="${item.price}" onclick="selectItemForSplit(this)">
                                <span class="item-name">${item.name}</span>
                                <span class="item-price">$${formatPrice(item.price)}</span>
                            </button>
                        </li>
                    `).join('')}
                </ul>
                <p class="total-amount" onclick="${!groupedOrder.paid ? `handlePay('${groupedOrder.tableNumber}', '${groupedOrder.customerName}')` : ''}" style="${!groupedOrder.paid ? 'cursor: pointer;' : ''}">Total: $${formatPrice(total)}</p>
                ${groupedOrder.paid ? '<p class="paid-status"><i class="fas fa-check-circle"></i> Paid</p>' : `
                    <div class="pay-split-buttons">
                        <button class="pay-btn" onclick="handlePay('${groupedOrder.tableNumber}', '${groupedOrder.customerName}')"><i class="fas fa-credit-card"></i> Pay</button>
                        <button class="split-btn" onclick="handleSplit('${groupedOrder.tableNumber}', '${groupedOrder.customerName}')"><i class="fas fa-cut"></i> Split</button>
                    </div>
                `}
            `;
            customerBills.appendChild(orderDiv);

            // Check if this order has any split items and update the button accordingly
            if (!groupedOrder.paid) {
                // Find any split operations that involve this order
                const orderKey = `Table ${groupedOrder.tableNumber} - ${groupedOrder.customerName}`;

                // Check if this is the original order for any split operations
                const isOriginalOrder = Object.values(splitItems).some(splitInfo => {
                    return splitInfo.originalOrderId === groupedOrder.id;
                });

                if (isOriginalOrder) {
                    // Find the split ID for this original order
                    const splitId = Object.entries(splitItems).find(([id, splitInfo]) =>
                        splitInfo.originalOrderId === groupedOrder.id
                    )[0];

                    // Get the split info to check if it's a bill split or item split
                    const splitInfo = splitItems[splitId];

                    // Update the split button to an undo button for the original order only
                    const splitBtn = orderDiv.querySelector('.split-btn');
                    if (splitBtn) {
                        // Add a different class for bill splits to style them differently if needed
                        if (splitInfo.type === 'bill-equal') {
                            splitBtn.classList.add('undo-bill-split-btn');
                        } else {
                            splitBtn.classList.add('undo-split-btn');
                        }

                        splitBtn.innerHTML = '<i class="fas fa-undo"></i> Undo Split';
                        splitBtn.setAttribute('data-split-id', splitId);
                        splitBtn.setAttribute('data-split-type', splitInfo.type || 'item');
                        splitBtn.onclick = function() { undoSplit(splitId); };
                    }
                } else {
                    // Check if this is a target order in any split operations
                    // First, log some debug info
                    console.log('Checking if order is a target:', {
                        tableNumber: groupedOrder.tableNumber,
                        customerName: groupedOrder.customerName,
                        id: groupedOrder.id
                    });

                    // Only consider an order a target if it has a split indicator in the name
                    // or if it's explicitly listed in the targetOrders of a split
                    const isSplitBill = /\(\d+\)$/.test(groupedOrder.customerName);

                    // If it's not a split bill (no parentheses in name), it's not a target
                    // This ensures regular orders (like the first order) are never considered targets
                    let isTargetOrder = false;

                    if (isSplitBill) {
                        // Only check further if it has the split indicator
                        if (Object.keys(splitItems).length > 0) {
                            isTargetOrder = Object.values(splitItems).some(splitInfo => {
                                return splitInfo.targetOrders.some(target => {
                                    // First check by ID if available
                                    if (target.id && groupedOrder.id && target.id === groupedOrder.id) {
                                        return true;
                                    }

                                    // Then check by table number and customer name
                                    if (target.tableNumber === groupedOrder.tableNumber &&
                                        target.customerName === groupedOrder.customerName) {
                                        return true;
                                    }

                                    return false;
                                });
                            });
                        }
                    }

                    if (isTargetOrder) {
                        // For target orders, disable the split button
                        const splitBtn = orderDiv.querySelector('.split-btn');
                        if (splitBtn) {
                            splitBtn.disabled = true;
                            splitBtn.classList.add('disabled-split-btn');
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error:', error);
        customerBills.innerHTML = `<p>Failed to load orders. Please try again later.</p>`;
    }
}

// Update table button colors based on unpaid bills
function updateTableButtonColors(orders) {
    const tableButtons = document.querySelectorAll('.table-btn');
    tableButtons.forEach(button => {
        // Extract table number from button text, handling the new icon structure
        const buttonText = button.textContent.trim();
        const tableMatch = buttonText.match(/Table (\d+)/);
        if (!tableMatch) return;

        const tableNumber = tableMatch[1]; // Get the captured table number
        const hasUnpaidOrders = orders.some(order => order.tableNumber === tableNumber && !order.paid);

        // Check if this is the currently selected table
        const isSelected = tableNumber === selectedTable;

        // First remove both classes
        button.classList.remove('unpaid');
        button.classList.remove('selected');

        // Then apply the appropriate class
        if (isSelected) {
            // If this is the selected table, it should be green regardless of payment status
            button.classList.add('selected');
        } else if (hasUnpaidOrders) {
            // If it has unpaid orders and is not selected, it should be orange
            button.classList.add('unpaid');
        }
        // Otherwise it will be the default blue
    });
}

// Handle Pay Button Click
function handlePay(tableNumber, customerName) {
    console.log(`Pay button clicked for Table ${tableNumber} - ${customerName}`);

    // Get all items from the DOM for this bill
    const billElements = document.querySelectorAll('.customer-bill');
    const billElement = Array.from(billElements).find(bill => {
        const header = bill.querySelector('h4');
        return header && header.textContent === `Table ${tableNumber} - ${customerName}`;
    });

    if (!billElement) {
        console.error('Bill element not found in DOM');
        alert('Error: Could not find bill details');
        return;
    }

    // Get all items from the bill
    const items = Array.from(billElement.querySelectorAll('.item-button')).map(item => {
        const itemText = item.textContent.trim();
        const priceMatch = itemText.match(/\$(\d+\.\d+)/);
        const quantityMatch = itemText.match(/x(\d+)/);
        const nameMatch = itemText.match(/^(.+?)(?:\s+x\d+|\s+\$)/);

        return {
            name: nameMatch ? nameMatch[1].trim() : itemText,
            price: priceMatch ? parseFloat(priceMatch[1]) : parseFloat(item.getAttribute('data-price')),
            quantity: quantityMatch ? parseInt(quantityMatch[1]) : 1
        };
    });

    if (items.length === 0) {
        console.error('No items found in bill');
        alert('Error: No items found in bill');
        return;
    }

    // Prepare order data for payment confirmation
    const orderData = {
        tableNumber: tableNumber,
        customerName: customerName,
        items: items
    };

    // Convert order data to URL-safe string
    const orderJson = encodeURIComponent(JSON.stringify(orderData));

    // Open new payment page with order data
    window.location.href = `../payment/index.html?orderData=${orderJson}`;
}

// Handle Split Button Click
async function handleSplit(tableNumber, customerName) {
    try {
        // Get current orders from backend
        const response = await fetch('http://localhost:5000/orders');
        const orders = await response.json();

        // Find the original order
        const originalOrder = orders.find(o =>
            o.tableNumber === tableNumber &&
            o.customerName === customerName
        );

        if (!originalOrder) {
            alert('Order not found');
            return;
        }

        // Check if this bill has any items
        if (!originalOrder.items || originalOrder.items.length === 0) {
            alert('This bill has no items to split.');
            return;
        }

        // Show the bill split modal instead of the item split modal
        const billSplitModal = document.getElementById('billSplitModal');
        const billToSplit = document.getElementById('billToSplit');
        billToSplit.textContent = `Table ${tableNumber} - ${customerName}`;

        // Store the original order for later use
        billSplitModal.dataset.tableNumber = tableNumber;
        billSplitModal.dataset.customerName = customerName;
        billSplitModal.dataset.orderId = originalOrder.id;

        // Setup the number of parts controls
        const numberOfParts = document.getElementById('numberOfParts');
        const decreasePartsBtn = document.getElementById('decreaseParts');
        const increasePartsBtn = document.getElementById('increaseParts');

        // Reset to default value
        numberOfParts.value = 2;

        // Add event listeners for the parts controls
        decreasePartsBtn.onclick = () => {
            if (parseInt(numberOfParts.value) > 2) {
                numberOfParts.value = parseInt(numberOfParts.value) - 1;
                updateBillSplitPreview(originalOrder, parseInt(numberOfParts.value));
            }
        };

        increasePartsBtn.onclick = () => {
            if (parseInt(numberOfParts.value) < 10) {
                numberOfParts.value = parseInt(numberOfParts.value) + 1;
                updateBillSplitPreview(originalOrder, parseInt(numberOfParts.value));
            }
        };

        numberOfParts.onchange = () => {
            let parts = parseInt(numberOfParts.value);
            if (parts < 2) parts = 2;
            if (parts > 10) parts = 10;
            numberOfParts.value = parts;
            updateBillSplitPreview(originalOrder, parts);
        };

        // Initial preview update
        updateBillSplitPreview(originalOrder, 2);

        // Setup the confirm and cancel buttons
        const confirmBillSplitBtn = document.getElementById('confirmBillSplit');
        const cancelBillSplitBtn = document.getElementById('cancelBillSplit');

        confirmBillSplitBtn.onclick = () => confirmBillSplit(originalOrder, parseInt(numberOfParts.value));
        cancelBillSplitBtn.onclick = () => billSplitModal.style.display = 'none';

        // Setup the close button
        const closeBtn = billSplitModal.querySelector('.close');
        closeBtn.onclick = () => billSplitModal.style.display = 'none';

        // Show the modal
        billSplitModal.style.display = 'block';

        // Return early - we're using a different flow now
        return;

        // Get all unpaid bills for the same table
        const tableBills = orders.filter(o =>
            o.tableNumber === tableNumber &&
            !o.paid
        );

        // Create bill selection options
        const billSelection = document.getElementById('billSelection');
        billSelection.innerHTML = '';
        tableBills.forEach(bill => {
            if (bill.customerName !== customerName) {
                const label = document.createElement('label');
                label.className = 'bill-option';
                label.innerHTML = `
                    <input type="checkbox" value="${bill.tableNumber}-${bill.customerName}">
                    Table ${bill.tableNumber} - ${bill.customerName}
                `;
                billSelection.appendChild(label);
            }
        });

        // Show split modal
        const splitModal = document.getElementById('splitModal');
        splitModal.style.display = 'block';

        // Handle Confirm Split Button Click
        const confirmSplitButton = document.getElementById('confirmSplit');
        confirmSplitButton.onclick = async () => {
            const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected')).map(label => label.dataset.value);
            if (selectedBills.length === 0) {
                alert('Please select at least one bill to split with.');
                return;
            }

            const splitType = document.querySelector('input[name="splitType"]:checked').value;
            let splitAmounts = [];

            if (selectedItem) {
                // Item-level split
                const itemPrice = parseFloat(selectedItem.getAttribute('data-price'));
                const itemName = selectedItem.getAttribute('data-item');

                if (splitType === 'equal') {
                    const splitAmount = itemPrice / selectedBills.length;
                    splitAmounts = selectedBills.map(() => splitAmount);
                } else if (splitType === 'percentage') {
                    const percentageInputs = document.querySelectorAll('#percentageFields input');
                    let totalPercentage = 0;
                    percentageInputs.forEach(input => {
                        totalPercentage += parseFloat(input.value) || 0;
                    });

                    if (totalPercentage !== 100) {
                        alert('The total percentage must equal 100%.');
                        return;
                    }

                    splitAmounts = Array.from(percentageInputs).map(input => {
                        const percentage = parseFloat(input.value) || 0;
                        return (itemPrice * percentage) / 100;
                    });
                } else if (splitType === 'custom') {
                    const customInputs = document.querySelectorAll('#customFields input');
                    let totalAmount = 0;
                    customInputs.forEach(input => {
                        totalAmount += parseFloat(input.value) || 0;
                    });

                    if (Math.abs(totalAmount - itemPrice) > 0.01) {
                        alert('The total custom amounts must equal the item price.');
                        return;
                    }

                    splitAmounts = Array.from(customInputs).map(input => parseFloat(input.value) || 0);
                }

                // Remove the split item from original order
                const itemIndex = originalOrder.items.findIndex(i =>
                    i.name === itemName &&
                    Math.abs(i.price - itemPrice) < 0.01
                );
                if (itemIndex !== -1) {
                    originalOrder.items.splice(itemIndex, 1);
                }

                // Update original order in backend
                try {
                    const response = await fetch(`http://localhost:5000/orders/${originalOrder.id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            items: originalOrder.items
                        })
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        console.error('Error updating original order:', errorData);
                    }
                } catch (error) {
                    console.error('Failed to update original order:', error);
                }

                // Add split items to selected bills
                for (let i = 0; i < selectedBills.length; i++) {
                    const [tableNum, custName] = selectedBills[i].split(' - ');
                    let targetOrder = orders.find(o =>
                        o.tableNumber === tableNum.replace('Table ', '') &&
                        o.customerName === custName
                    );

                    if (!targetOrder) {
                        // Create new order if it doesn't exist
                        targetOrder = {
                            tableNumber: tableNum.replace('Table ', ''),
                            customerName: custName,
                            items: []
                        };
                    }

                    // Add split item to target order
                    targetOrder.items.push({
                        name: itemName,
                        price: splitAmounts[i],
                        quantity: 1
                    });

                    // Update or create order in backend
                    try {
                        if (targetOrder.id) {
                            const response = await fetch(`http://localhost:5000/orders/${targetOrder.id}`, {
                                method: 'PUT',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    items: targetOrder.items
                                })
                            });

                            if (!response.ok) {
                                const errorData = await response.json();
                                console.error('Error updating target order:', errorData);
                            }
                        } else {
                            const response = await fetch('http://localhost:5000/orders', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    tableNumber: targetOrder.tableNumber,
                                    customerName: targetOrder.customerName,
                                    items: targetOrder.items
                                })
                            });

                            if (!response.ok) {
                                const errorData = await response.json();
                                console.error('Error creating target order:', errorData);
                            }
                        }
                    } catch (error) {
                        console.error('Failed to update/create target order:', error);
                    }
                }
            }

            // Refresh the display
            displayOrders();

            // Close the modal
            splitModal.style.display = 'none';
            selectedItem = null;
        };

    } catch (error) {
        console.error('Error handling split:', error);
        alert('Failed to process split. Please try again.');
    }
}

// Select Item for Split
function selectItemForSplit(itemButton) {
    selectedItem = itemButton;
    document.getElementById('selectedItemName').textContent = itemButton.getAttribute('data-item');
    const splitModal = document.getElementById('splitModal');
    splitModal.style.display = 'flex';
    populateBills();
}

// Setup Split Modal
function setupSplitModal() {
    const splitModal = document.getElementById('splitModal');
    const closeModal = document.querySelector('.close');
    const splitOptions = document.querySelectorAll('input[name="splitType"]');
    const confirmSplitButton = document.getElementById('confirmSplit');
    const undoSplitButton = document.getElementById('undoSplit');

    // Close modal when clicking the "X" button
    closeModal.addEventListener('click', () => {
        splitModal.style.display = 'none';
    });

    // Close modal when clicking outside the modal
    window.addEventListener('click', (event) => {
        if (event.target === splitModal) {
            splitModal.style.display = 'none';
        }
    });

    // Handle split type change
    splitOptions.forEach(option => {
        option.addEventListener('change', () => {
            // Remove selected class from all labels
            document.querySelectorAll('#splitOptions label').forEach(label => {
                label.classList.remove('selected');
            });

            // Add selected class to the clicked label
            const selectedLabel = option.parentElement;
            selectedLabel.classList.add('selected');

            const percentageInputs = document.getElementById('percentageInputs');
            const customInputs = document.getElementById('customInputs');

            if (option.value === 'percentage') {
                percentageInputs.style.display = 'block';
                customInputs.style.display = 'none';
                populatePercentageFields();
            } else if (option.value === 'custom') {
                percentageInputs.style.display = 'none';
                customInputs.style.display = 'block';
                populateCustomFields();
            } else {
                percentageInputs.style.display = 'none';
                customInputs.style.display = 'none';
            }
        });
    });

    // Handle undo split button click
    undoSplitButton.addEventListener('click', () => {
        if (!lastAction) return;

        const { item, originalBill, selectedBills, splitAmounts } = lastAction;

        // Revert the totals for selected bills
        selectedBills.forEach((billId, index) => {
            const bills = document.querySelectorAll('.customer-bill');
            const bill = Array.from(bills).find(b => b.querySelector('h4').textContent === billId);

            if (bill) {
                // Remove the split item from the bill
                const itemsList = bill.querySelector('ul');
                const lastItem = itemsList.lastElementChild;
                if (lastItem) {
                    lastItem.remove();
                }

                // Update the total
                const totalElement = bill.querySelector('p');
                let currentTotal = parseFloat(totalElement.textContent.replace('Total: $', ''));
                currentTotal -= splitAmounts[index];
                totalElement.textContent = `Total: $${currentTotal.toFixed(2)}`;
            }
        });

        // Add the item back to the original bill
        const originalTotalElement = originalBill.querySelector('p');
        let originalTotal = parseFloat(originalTotalElement.textContent.replace('Total: $', ''));
        originalTotal += parseFloat(item.getAttribute('data-price'));
        originalTotalElement.textContent = `Total: $${originalTotal.toFixed(2)}`;
        originalBill.querySelector('ul').appendChild(item);

        // Reset last action and hide undo button
        lastAction = null;
        undoSplitButton.style.display = 'none';
    });

    // Set initial selected state for equal split
    const equalSplitLabel = document.querySelector('label[for="equal"]');
    if (equalSplitLabel) {
        equalSplitLabel.classList.add('selected');
    }

    // Handle Confirm Split Button Click
    confirmSplitButton.addEventListener('click', async () => {
        try {
            // Get current orders from backend
            const response = await fetch('http://localhost:5000/orders');
            const orders = await response.json();

            const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected')).map(label => label.dataset.value);
            if (selectedBills.length === 0) {
                alert('Please select at least one bill to split with.');
                return;
            }

            const splitType = document.querySelector('input[name="splitType"]:checked').value;
            let splitAmounts = [];

            if (selectedItem) {
                // Item-level split
                const itemPrice = parseFloat(selectedItem.getAttribute('data-price'));
                const itemName = selectedItem.getAttribute('data-item');

                if (splitType === 'equal') {
                    const splitAmount = itemPrice / selectedBills.length;
                    splitAmounts = selectedBills.map(() => splitAmount);
                } else if (splitType === 'percentage') {
                    const percentageInputs = document.querySelectorAll('#percentageFields input');
                    let totalPercentage = 0;
                    percentageInputs.forEach(input => {
                        totalPercentage += parseFloat(input.value) || 0;
                    });

                    if (totalPercentage !== 100) {
                        alert('The total percentage must equal 100%.');
                        return;
                    }

                    splitAmounts = Array.from(percentageInputs).map(input => {
                        const percentage = parseFloat(input.value) || 0;
                        return (itemPrice * percentage) / 100;
                    });
                } else if (splitType === 'custom') {
                    const customInputs = document.querySelectorAll('#customFields input');
                    let totalAmount = 0;
                    customInputs.forEach(input => {
                        totalAmount += parseFloat(input.value) || 0;
                    });

                    if (Math.abs(totalAmount - itemPrice) > 0.01) {
                        alert('The total custom amounts must equal the item price.');
                        return;
                    }

                    splitAmounts = Array.from(customInputs).map(input => parseFloat(input.value) || 0);
                }

                // Find the original order in the database
                const originalBill = selectedItem.closest('.customer-bill');
                const originalBillHeader = originalBill.querySelector('h4').textContent;
                const [originalTableNum, originalCustName] = originalBillHeader.split(' - ');
                const originalOrder = orders.find(o =>
                    o.tableNumber === originalTableNum.replace('Table ', '') &&
                    o.customerName === originalCustName
                );

                if (!originalOrder) {
                    alert('Original order not found in database');
                    return;
                }

                // Remove the split item from original order
                const itemIndex = originalOrder.items.findIndex(i =>
                    i.name === itemName &&
                    Math.abs(i.price - itemPrice) < 0.01
                );
                if (itemIndex !== -1) {
                    originalOrder.items.splice(itemIndex, 1);
                }

                // Update original order in backend
                try {
                    const updateResponse = await fetch(`http://localhost:5000/orders/${originalOrder.id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            items: originalOrder.items
                        })
                    });

                    if (!updateResponse.ok) {
                        const errorData = await updateResponse.json();
                        console.error('Error updating original order:', errorData);
                        alert('Failed to update original order. Please try again.');
                        return;
                    }
                } catch (error) {
                    console.error('Failed to update original order:', error);
                    alert('Failed to update original order. Please try again.');
                    return;
                }

                // Add split items to selected bills
                for (let i = 0; i < selectedBills.length; i++) {
                    const [tableNum, custName] = selectedBills[i].split(' - ');
                    let targetOrder = orders.find(o =>
                        o.tableNumber === tableNum.replace('Table ', '') &&
                        o.customerName === custName
                    );

                    if (!targetOrder) {
                        // Create new order if it doesn't exist
                        targetOrder = {
                            tableNumber: tableNum.replace('Table ', ''),
                            customerName: custName,
                            items: []
                        };
                    }

                    // Create a description of the split portion
                    let splitDescription = '';
                    if (splitType === 'equal') {
                        splitDescription = `1/${selectedBills.length} `;
                    } else if (splitType === 'percentage') {
                        const percentage = Math.round((splitAmounts[i] / itemPrice) * 100);
                        splitDescription = `${percentage}% `;
                    } else if (splitType === 'custom') {
                        // For custom splits, we'll use a dollar amount description
                        splitDescription = `$${splitAmounts[i].toFixed(2)} of `;
                    }

                    // Add split item to target order with modified name
                    targetOrder.items.push({
                        name: splitDescription + itemName,
                        price: splitAmounts[i],
                        quantity: 1
                    });

                    // Update or create order in backend
                    try {
                        if (targetOrder.id) {
                            const response = await fetch(`http://localhost:5000/orders/${targetOrder.id}`, {
                                method: 'PUT',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    items: targetOrder.items
                                })
                            });

                            if (!response.ok) {
                                const errorData = await response.json();
                                console.error('Error updating target order:', errorData);
                                alert('Failed to update target order. Please try again.');
                                return;
                            }
                        } else {
                            const response = await fetch('http://localhost:5000/orders', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    tableNumber: targetOrder.tableNumber,
                                    customerName: targetOrder.customerName,
                                    items: targetOrder.items
                                })
                            });

                            if (!response.ok) {
                                const errorData = await response.json();
                                console.error('Error creating target order:', errorData);
                                alert('Failed to create target order. Please try again.');
                                return;
                            }
                        }
                    } catch (error) {
                        console.error('Failed to update/create target order:', error);
                        alert('Failed to update/create target order. Please try again.');
                        return;
                    }
                }

                // Create a unique ID for this split operation
                const splitId = `split_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

                // Store split information for undo functionality
                splitItems[splitId] = {
                    originalOrderId: originalOrder.id,
                    originalItem: {
                        name: itemName,
                        price: itemPrice,
                        quantity: 1
                    },
                    targetOrders: selectedBills.map((billId, index) => {
                        const [tableNum, custName] = billId.split(' - ');
                        const targetOrder = orders.find(o =>
                            o.tableNumber === tableNum.replace('Table ', '') &&
                            o.customerName === custName
                        );
                        return {
                            orderId: targetOrder?.id,
                            tableNumber: tableNum.replace('Table ', ''),
                            customerName: custName,
                            splitAmount: splitAmounts[index]
                        };
                    }),
                    timestamp: Date.now() // Add timestamp for sorting
                };

                // Save split items to localStorage
                saveSplitItems();

                // Update UI
                // Track the last action for undo
                lastAction = {
                    item: selectedItem,
                    originalBill: originalBill,
                    selectedBills,
                    splitAmounts,
                    splitType,
                    isBillSplit: false,
                    splitId: splitId
                };

                // We don't need to update the UI here since we'll refresh the display
                // which will show the updated data from the database
                // Just remove the selected item from the UI
                if (selectedItem) {
                    selectedItem.remove();
                }
            } else {
                // Bill-level split is not implemented for database updates yet
                alert('Bill-level split is not fully implemented yet. Please use item-level split.');
                return;
            }

            // Reset selectedItem and close the modal
            selectedItem = null;
            splitModal.style.display = 'none';
            document.getElementById('undoSplit').style.display = 'block';

            // Refresh the display to show updated data
            try {
                // Wait a moment for the database updates to complete
                setTimeout(() => {
                    displayOrders();
                }, 500);
            } catch (displayError) {
                console.error('Error refreshing display:', displayError);
                // Don't show an alert here, as the split was successful
            }
        } catch (error) {
            console.error('Error in split operation:', error);
            // Only show alert for actual errors in the split operation
            if (error.message && error.message.includes('Failed to fetch')) {
                alert('Network error: Please check your connection and try again.');
            } else {
                alert('An error occurred during the split operation. Please try again.');
            }
        }
    });
}

// Undo a split operation
async function undoSplit(splitId) {
    try {
        console.log('Undoing split:', splitId);
        const splitInfo = splitItems[splitId];

        if (!splitInfo) {
            console.error('Split information not found for ID:', splitId);
            alert('Unable to undo split: Split information not found');
            return;
        }

        // Get current orders from backend
        const response = await fetch('http://localhost:5000/orders');
        const orders = await response.json();

        // Find the original order
        const originalOrder = orders.find(o => o.id === splitInfo.originalOrderId);

        if (!originalOrder) {
            console.error('Original order not found:', splitInfo.originalOrderId);
            alert('Unable to undo split: Original order not found');
            return;
        }

        // Check if this is a bill split or an item split
        if (splitInfo.type !== 'bill-equal') {
            // For item splits, handle as before
            // First, check if the original item is already in the order (might have been partially split)
            const existingItemIndex = originalOrder.items.findIndex(item => {
                // Check if this is a partial split of the same item
                // Look for items with the same base name (ignoring split prefixes like "1/2 " or "50% ")
                const itemName = item.name;
                const originalItemName = splitInfo.originalItem.name;

                // Check if the item name contains the original name (for partial splits)
                const isPartialSplit = itemName.includes(originalItemName) ||
                                      originalItemName.includes(itemName.replace(/^(\d+\/\d+\s|\d+%\s|\$\d+\.\d+\sof\s)/, ''));

                return isPartialSplit && Math.abs(item.price - splitInfo.originalItem.price) < 0.01;
            });

            if (existingItemIndex !== -1) {
                // If a partial split exists, remove it and add the full item
                originalOrder.items.splice(existingItemIndex, 1);
            }

            // Now add the original item
            originalOrder.items.push(splitInfo.originalItem);
        } else {
            // For bill splits, we'll handle this in the bill-equal section below
            // We need to collect all items from target orders first
            console.log('Bill split detected, will handle in bill-equal section');
        }

        // Update the original order in the database
        try {
            const updateResponse = await fetch(`http://localhost:5000/orders/${originalOrder.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    items: originalOrder.items
                })
            });

            if (!updateResponse.ok) {
                const errorData = await updateResponse.json();
                console.error('Error updating original order during undo:', errorData);
                alert('Failed to undo split: Could not update original order');
                return;
            }
        } catch (error) {
            console.error('Failed to update original order during undo:', error);
            alert('Failed to undo split: Could not update original order');
            return;
        }

        // For item splits, remove the split items from the target orders
        if (splitInfo.type !== 'bill-equal') {
            for (const targetOrderInfo of splitInfo.targetOrders) {
                if (!targetOrderInfo.orderId) continue;

                const targetOrder = orders.find(o => o.id === targetOrderInfo.orderId);
                if (!targetOrder) continue;

                // Find and remove the split item
                const itemIndex = targetOrder.items.findIndex(item => {
                    // Get the base name without split prefixes
                    const itemBaseName = item.name.replace(/^(\d+\/\d+\s|\d+%\s|\$\d+\.\d+\sof\s)/, '');
                    const originalBaseName = splitInfo.originalItem.name.replace(/^(\d+\/\d+\s|\d+%\s|\$\d+\.\d+\sof\s)/, '');

                    // Check if this is the split item we're looking for
                    return (itemBaseName === originalBaseName ||
                           item.name.includes(originalBaseName) ||
                           splitInfo.originalItem.name.includes(itemBaseName)) &&
                           Math.abs(item.price - targetOrderInfo.splitAmount) < 0.01;
                });

                if (itemIndex !== -1) {
                    targetOrder.items.splice(itemIndex, 1);

                    // Update the target order in the database
                    try {
                        const updateResponse = await fetch(`http://localhost:5000/orders/${targetOrder.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                items: targetOrder.items
                            })
                        });

                        if (!updateResponse.ok) {
                            console.error('Error updating target order during undo:', await updateResponse.json());
                        }
                    } catch (error) {
                        console.error('Failed to update target order during undo:', error);
                    }
                }
            }
        }

        // Check if this was a bill-equal split
        if (splitInfo.type === 'bill-equal') {
            console.log('Processing bill-equal split undo');

            // Clear the original order's items - we'll replace them with the restored items
            originalOrder.items = [];

            // For bill splits, we need to handle differently
            // First, get all the items from the target orders and add them back to the original order
            for (const targetOrderInfo of splitInfo.targetOrders) {
                console.log('Looking for target order:', targetOrderInfo);

                // Find the target order by ID or by table/customer name
                const targetOrder = orders.find(o => {
                    if (targetOrderInfo.id && o.id === targetOrderInfo.id) {
                        return true;
                    }
                    return o.tableNumber === targetOrderInfo.tableNumber &&
                           o.customerName === targetOrderInfo.customerName;
                });

                if (targetOrder) {
                    console.log('Found target order:', targetOrder);

                    if (targetOrder.items && targetOrder.items.length > 0) {
                        // For each item in the target order, add it back to the original order
                        // but first remove the split prefix (e.g., "1/3 ")
                        targetOrder.items.forEach(item => {
                            // Remove the split prefix and restore the original price
                            const originalName = item.name.replace(/^\d+\/\d+\s/, '');
                            // Calculate the original price by multiplying by the number of parts
                            // Add 1 for the original order plus the number of target orders
                            const parts = 1 + splitInfo.targetOrders.length;
                            const originalPrice = item.price * parts;

                            console.log(`Restoring item: ${originalName} with price: ${originalPrice}`);

                            // Add the item back to the original order
                            originalOrder.items.push({
                                name: originalName,
                                price: originalPrice,
                                quantity: item.quantity || 1
                            });
                        });

                        // Now delete the target order
                        try {
                            console.log(`Deleting target order: ${targetOrder.id}`);
                            const deleteResponse = await fetch(`http://localhost:5000/orders/${targetOrder.id}`, {
                                method: 'DELETE'
                            });

                            if (!deleteResponse.ok) {
                                console.error('Error deleting target order:', await deleteResponse.json());
                            }
                        } catch (error) {
                            console.error('Failed to delete target order during undo:', error);
                        }
                    }
                } else {
                    console.warn('Target order not found:', targetOrderInfo);
                }
            }

            // Also restore the original order's items by removing the split prefix
            const originalItems = [...originalOrder.items]; // Make a copy
            originalOrder.items = [];

            originalItems.forEach(item => {
                // Remove the split prefix and restore the original price
                const originalName = item.name.replace(/^\d+\/\d+\s/, '');
                // Calculate the original price by multiplying by the number of parts
                // Add 1 for the original order plus the number of target orders
                const parts = 1 + splitInfo.targetOrders.length;
                const originalPrice = item.price * parts;

                // Add the item back to the original order
                originalOrder.items.push({
                    name: originalName,
                    price: originalPrice,
                    quantity: item.quantity || 1
                });
            });

            // Update the original order
            try {
                console.log('Updating original order with restored items:', originalOrder.items);
                const updateResponse = await fetch(`http://localhost:5000/orders/${originalOrder.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        items: originalOrder.items
                    })
                });

                if (!updateResponse.ok) {
                    console.error('Error updating original order:', await updateResponse.json());
                }
            } catch (error) {
                console.error('Failed to update original order during bill split undo:', error);
            }
        }

        // Remove the split information
        delete splitItems[splitId];

        // Save updated split items to localStorage
        saveSplitItems();

        // Refresh the display
        try {
            displayOrders();
        } catch (displayError) {
            console.error('Error refreshing display after undo:', displayError);
            // Don't show an alert here, as the undo was successful
        }

    } catch (error) {
        console.error('Error undoing split:', error);
        // Only show alert for actual errors in the undo operation
        if (error.message && error.message.includes('Failed to fetch')) {
            alert('Network error: Please check your connection and try again.');
        } else {
            alert('An error occurred while undoing the split. Please try again.');
        }
    }
}

// Populate bills in the modal
function populateBills() {
    const billSelection = document.getElementById('billSelection');
    billSelection.innerHTML = '';

    // Get all customer bills
    const customerBills = document.querySelectorAll('.customer-bill');
    customerBills.forEach(bill => {
        const label = document.createElement('label');
        label.textContent = bill.querySelector('h4').textContent;
        label.dataset.value = bill.querySelector('h4').textContent;
        label.addEventListener('click', () => {
            label.classList.toggle('selected');
            const splitType = document.querySelector('input[name="splitType"]:checked')?.value;
            if (splitType === 'percentage') {
                populatePercentageFields();
            } else if (splitType === 'custom') {
                populateCustomFields();
            }
        });
        billSelection.appendChild(label);
    });
}

// Populate percentage fields
function populatePercentageFields() {
    const percentageFields = document.getElementById('percentageFields');
    percentageFields.innerHTML = '';
    const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected'));
    selectedBills.forEach(label => {
        const billId = label.dataset.value;
        const inputLabel = document.createElement('label');
        inputLabel.textContent = `${billId}: `;
        const input = document.createElement('input');
        input.type = 'number';
        input.min = '0';
        input.max = '100';
        input.placeholder = 'Percentage';
        percentageFields.appendChild(inputLabel);
        percentageFields.appendChild(input);
        percentageFields.appendChild(document.createElement('br'));
    });
}

// Populate custom fields
function populateCustomFields() {
    const customFields = document.getElementById('customFields');
    customFields.innerHTML = '';
    const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected'));
    selectedBills.forEach(label => {
        const billId = label.dataset.value;
        const inputLabel = document.createElement('label');
        inputLabel.textContent = `${billId}: `;
        const input = document.createElement('input');
        input.type = 'number';
        input.min = '0';
        input.placeholder = 'Amount';
        customFields.appendChild(inputLabel);
        customFields.appendChild(input);
        customFields.appendChild(document.createElement('br'));
    });
}

// Clear Table
async function clearTable() {
    console.log('Clear button clicked');
    console.log('Selected table:', selectedTable);

    if (!selectedTable) {
        alert('Please select a table first.');
        return;
    }

    const confirmation = confirm(`Are you sure you want to clear Table ${selectedTable}? This will remove all orders.`);
    if (!confirmation) {
        return;
    }

    try {
        console.log('Sending delete request to:', `http://localhost:5000/orders/table/${selectedTable}`);
        const response = await fetch(`http://localhost:5000/orders/table/${selectedTable}`, {
            method: 'DELETE',
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (response.ok) {
            alert(`Table ${selectedTable} has been cleared.`);
            displayOrders(); // Refresh the display
        } else {
            const errorData = await response.json();
            console.log('Error data:', errorData);
            alert('Failed to clear the table. Please try again.');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while clearing the table.');
    }
}

function showMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);
    setTimeout(() => messageDiv.remove(), 3000);
}

// Create bill element
function createBillElement(tableNumber, customerName, items) {
    const billElement = document.createElement('div');
    billElement.className = 'bill';
    billElement.innerHTML = `
        <div class="bill-header">
            <h3>Table ${tableNumber} - ${customerName}</h3>
            <button class="pay-btn" onclick="handlePay('${tableNumber}', '${customerName}')">Pay</button>
        </div>
        <div class="bill-items"></div>
        <div class="bill-total">
            <span>Total:</span>
            <span class="total-amount">$0.00</span>
        </div>
    `;

    const itemsContainer = billElement.querySelector('.bill-items');
    const totalAmountElement = billElement.querySelector('.total-amount');

    // Add items to bill
    items.forEach(item => {
        const itemElement = document.createElement('div');
        itemElement.className = 'bill-item';
        itemElement.innerHTML = `
            <span class="item-name">${item.name}</span>
            <span class="item-price">$${item.price.toFixed(2)}</span>
        `;
        itemsContainer.appendChild(itemElement);
    });

    // Calculate and update total
    const total = items.reduce((sum, item) => sum + item.price, 0);
    totalAmountElement.textContent = `$${total.toFixed(2)}`;

    return billElement;
}

// Handle backup
async function handleBackup() {
    try {
        const response = await fetch('http://localhost:5000/api/backup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (response.ok) {
            showMessage('Backup created successfully!');
            console.log('Backup created at:', result.backupPath);
        } else {
            throw new Error(result.error || 'Failed to create backup');
        }
    } catch (error) {
        console.error('Backup error:', error);
        showMessage('Failed to create backup: ' + error.message);
    }
}

// Update the bill split preview
function updateBillSplitPreview(originalOrder, parts) {
    const splitPreview = document.getElementById('splitPreview');
    const amountPerBill = document.getElementById('amountPerBill');

    // Clear the preview
    splitPreview.innerHTML = '';

    // Calculate total bill amount
    const totalAmount = originalOrder.items.reduce((total, item) => {
        return total + (item.price * (item.quantity || 1));
    }, 0);

    // Calculate amount per bill
    const amountPerPart = totalAmount / parts;

    // Update the amount per bill display
    amountPerBill.textContent = `$${formatPrice(amountPerPart)}`;

    // Show a preview of items
    const previewItems = originalOrder.items.slice(0, 3); // Show up to 3 items
    previewItems.forEach(item => {
        const li = document.createElement('li');
        li.textContent = `${item.name} - $${formatPrice(item.price)}`;
        splitPreview.appendChild(li);
    });

    // If there are more items, show a message
    if (originalOrder.items.length > 3) {
        const li = document.createElement('li');
        li.textContent = `... and ${originalOrder.items.length - 3} more item(s)`;
        splitPreview.appendChild(li);
    }
}

// Handle the bill split confirmation
async function confirmBillSplit(originalOrder, parts) {
    try {
        // Check if this order is already part of a split
        const isPartOfSplit = Object.values(splitItems).some(splitInfo => {
            // Check if it's the original order in a split
            if (splitInfo.originalOrderId === originalOrder.id) {
                return true;
            }

            // Check if it's a target order in a split
            return splitInfo.targetOrders.some(target =>
                target.id === originalOrder.id ||
                (target.tableNumber === originalOrder.tableNumber &&
                 target.customerName === originalOrder.customerName)
            );
        });

        if (isPartOfSplit) {
            alert('This bill is already part of a split. Please undo the existing split first.');
            return;
        }
        // Hide the modal
        const billSplitModal = document.getElementById('billSplitModal');
        billSplitModal.style.display = 'none';

        // Show loading message
        showMessage('Splitting bill...');

        // Calculate total bill amount
        const totalAmount = originalOrder.items.reduce((total, item) => {
            return total + (item.price * (item.quantity || 1));
        }, 0);

        // Calculate amount per bill
        const amountPerPart = totalAmount / parts;

        // Create new bills
        const newBills = [];

        for (let i = 1; i <= parts; i++) {
            // First bill will be the original bill with updated items
            if (i === 1) {
                // Create a copy of all items with adjusted prices
                const adjustedItems = originalOrder.items.map(item => {
                    // Calculate the adjusted price (original price / parts)
                    const adjustedPrice = item.price / parts;

                    return {
                        name: `1/${parts} ${item.name}`,
                        price: adjustedPrice,
                        quantity: item.quantity || 1
                    };
                });

                // Update the original order
                const updateResponse = await fetch(`http://localhost:5000/orders/${originalOrder.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        items: adjustedItems
                    })
                });

                if (!updateResponse.ok) {
                    throw new Error('Failed to update original order');
                }

                newBills.push({
                    id: originalOrder.id,
                    tableNumber: originalOrder.tableNumber,
                    customerName: originalOrder.customerName
                });
            } else {
                // For additional bills, create new orders with the same items but adjusted prices
                const newCustomerName = `${originalOrder.customerName} (${i})`;

                // Create a copy of all items with adjusted prices
                const adjustedItems = originalOrder.items.map(item => {
                    // Calculate the adjusted price (original price / parts)
                    const adjustedPrice = item.price / parts;

                    return {
                        name: `1/${parts} ${item.name}`,
                        price: adjustedPrice,
                        quantity: item.quantity || 1
                    };
                });

                // Create a new order
                const createResponse = await fetch('http://localhost:5000/orders', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tableNumber: originalOrder.tableNumber,
                        customerName: newCustomerName,
                        items: adjustedItems
                    })
                });

                if (!createResponse.ok) {
                    throw new Error('Failed to create new order');
                }

                const newOrder = await createResponse.json();
                newBills.push({
                    id: newOrder.id,
                    tableNumber: newOrder.tableNumber,
                    customerName: newOrder.customerName
                });
            }
        }

        // Create a unique split ID
        const splitId = 'bill-split-' + Date.now();

        // Track this split operation in the splitItems object
        splitItems[splitId] = {
            originalOrderId: originalOrder.id,
            targetOrders: newBills.slice(1), // Skip the first bill (original)
            type: 'bill-equal',
            timestamp: new Date().toISOString()
        };

        // Save the updated split items to localStorage
        saveSplitItems();

        // Show success message
        showMessage(`Bill split into ${parts} equal parts successfully!`);

        // Refresh the display
        displayOrders();

    } catch (error) {
        console.error('Error splitting bill:', error);
        showMessage('Failed to split bill: ' + error.message);
    }
}