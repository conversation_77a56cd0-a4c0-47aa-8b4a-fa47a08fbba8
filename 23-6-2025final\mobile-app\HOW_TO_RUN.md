# How to Run the Paradiz Hookah Lounge Mobile App

Follow these steps to run the mobile app on your computer:

## Prerequisites

1. Make sure you have Node.js installed (version 16.x or 18.x recommended)
2. Install the Expo CLI globally:
   ```
   npm install -g expo-cli
   ```

## Setup Steps

1. **Create placeholder images**
   Run the following command to create placeholder image files:
   ```
   node create-placeholder-images.js
   ```

2. **Install dependencies**
   ```
   npm install
   ```

3. **Start the development server**
   ```
   npm start
   ```
   or
   ```
   npx expo start
   ```

4. **Run on web browser**
   When the Expo CLI starts, press `w` to open the app in your web browser.

5. **Run on Android emulator**
   - Install Android Studio
   - Set up an Android Virtual Device
   - Start the emulator
   - When the Expo CLI starts, press `a` to open the app in the Android emulator

6. **Run on iOS simulator (Mac only)**
   - Install Xcode
   - When the Expo CLI starts, press `i` to open the app in the iOS simulator

7. **Run on your physical device**
   - Install the Expo Go app on your phone
   - Scan the QR code displayed in the terminal with your phone's camera

## Troubleshooting

### If you get errors about missing font files:

The app is configured to use Poppins font, but the font files are not included in the repository. You can:

1. Download the Poppins font from Google Fonts: https://fonts.google.com/specimen/Poppins
2. Place the following files in the `assets/fonts` directory:
   - Poppins-Regular.ttf
   - Poppins-Medium.ttf
   - Poppins-Bold.ttf

### If you get errors about missing image files:

The app requires several image files for icons and splash screens. You can:

1. Run the `create-placeholder-images.js` script to create placeholder files
2. Replace these placeholders with actual images before building the app

### If you get "Unable to find expo in this project" error:

Try running:
```
npm install
npx expo start
```

### If you get "Metro bundler" errors:

Try clearing the cache:
```
npx expo start --clear
```

## Next Steps

Once you have the app running, you can:

1. Customize the UI by editing the screen files in the `src/screens` directory
2. Connect to your actual API by updating the base URL in `src/api/api.ts`
3. Add real images and assets to the `assets` directory

For more detailed instructions, refer to the `README.md` and `INSTALLATION.md` files.
