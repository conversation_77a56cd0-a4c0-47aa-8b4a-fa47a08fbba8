/* General Styles */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --danger-color: #e74c3c;
    --danger-dark: #c0392b;
    --warning-color: #f39c12;
    --light-gray: #f4f4f4;
    --medium-gray: #e0e0e0;
    --dark-gray: #333;
    --text-color: #333;
    --border-radius: 5px;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-gray);
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

/* Header Styles */
header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

header h1 {
    font-size: 24px;
    display: flex;
    align-items: center;
}

header h1 i {
    margin-right: 10px;
}

.actions {
    display: flex;
    gap: 10px;
}

/* Button Styles */
button {
    padding: 8px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 14px;
    transition: background-color 0.2s;
}

button i {
    margin-right: 5px;
}

.primary-btn {
    background-color: var(--secondary-color);
    color: white;
}

.primary-btn:hover {
    background-color: var(--secondary-dark);
}

.danger-btn {
    background-color: var(--danger-color);
    color: white;
}

.danger-btn:hover {
    background-color: var(--danger-dark);
}

button:not(.primary-btn):not(.danger-btn) {
    background-color: white;
    color: var(--dark-gray);
    border: 1px solid var(--medium-gray);
}

button:not(.primary-btn):not(.danger-btn):hover {
    background-color: var(--light-gray);
}

/* Backup Types */
.backup-types {
    display: flex;
    padding: 15px 20px;
    border-bottom: 1px solid var(--medium-gray);
    gap: 10px;
}

.type-btn {
    padding: 6px 12px;
    border-radius: 20px;
}

.type-btn.active {
    background-color: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
}

/* Backup List */
.backup-list-container {
    padding: 20px;
    border-right: 1px solid var(--medium-gray);
    width: 60%;
    float: left;
}

.backup-list-container h2 {
    margin-bottom: 15px;
    font-size: 18px;
}

.backup-list {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
}

.backup-item {
    padding: 15px;
    border-bottom: 1px solid var(--medium-gray);
    cursor: pointer;
    transition: background-color 0.2s;
}

.backup-item:last-child {
    border-bottom: none;
}

.backup-item:hover {
    background-color: var(--light-gray);
}

.backup-item.selected {
    background-color: #e3f2fd;
    border-left: 4px solid var(--primary-color);
}

.backup-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.backup-name {
    font-weight: bold;
}

.backup-type {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 10px;
    background-color: var(--light-gray);
}

.backup-type.manual {
    background-color: #e3f2fd;
    color: var(--primary-color);
}

.backup-type.daily {
    background-color: #e8f5e9;
    color: var(--secondary-color);
}

.backup-type.incremental {
    background-color: #fff8e1;
    color: var(--warning-color);
}

.backup-info {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: #666;
}

.backup-size {
    font-size: 12px;
}

.loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.loading i {
    margin-right: 10px;
}

/* Backup Details */
.backup-details {
    padding: 20px;
    width: 40%;
    float: left;
}

.backup-details h2 {
    margin-bottom: 15px;
    font-size: 18px;
}

.details-content {
    padding: 15px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    min-height: 300px;
}

.details-header {
    margin-bottom: 20px;
}

.details-header h3 {
    margin-bottom: 5px;
}

.details-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.details-section {
    margin-bottom: 15px;
}

.details-section h4 {
    margin-bottom: 5px;
    font-size: 14px;
    color: #666;
}

.file-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    padding: 10px;
    font-size: 13px;
}

.file-item {
    padding: 3px 0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: var(--border-radius);
    width: 500px;
    max-width: 90%;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 24px;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group select, .form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.warning {
    background-color: #fff8e1;
    border-left: 4px solid var(--warning-color);
    padding: 15px;
    margin: 15px 0;
    display: flex;
    align-items: flex-start;
}

.warning i {
    color: var(--warning-color);
    margin-right: 10px;
    font-size: 20px;
}

.restore-details {
    margin: 15px 0;
}

.restore-details p {
    margin-bottom: 5px;
}

.restore-details span {
    font-weight: bold;
}

/* Clearfix */
.container::after {
    content: "";
    display: table;
    clear: both;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .backup-list-container, .backup-details {
        width: 100%;
        float: none;
        border-right: none;
    }

    .backup-details {
        border-top: 1px solid var(--medium-gray);
    }

    header {
        flex-direction: column;
        align-items: flex-start;
    }

    .actions {
        margin-top: 10px;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-message {
    background-color: white;
    padding: 20px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 18px;
}

/* Error Message */
.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid #c62828;
    margin: 20px 0;
    font-size: 16px;
}

/* Back Button */
.back-button-container {
    text-align: center;
    margin: 20px 0;
    padding: 0 20px;
}

.back-btn {
    background-color: var(--dark-gray);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.back-btn:hover {
    background-color: #555;
}

/* Tabs */
.details-tabs {
    display: flex;
    border-bottom: 1px solid var(--medium-gray);
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    background-color: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.tab-btn.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Orders Table */
.orders-container {
    overflow-x: auto;
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.orders-table th,
.orders-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.orders-table th {
    background-color: var(--light-gray);
    font-weight: bold;
}

.orders-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.order-details-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
}

.order-details-btn:hover {
    background-color: var(--primary-dark);
}

/* Order Details Modal */
.order-details-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
}

.order-details-content {
    background-color: white;
    margin: 50px auto;
    padding: 20px;
    width: 80%;
    max-width: 800px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    position: relative;
}

.order-items-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.order-items-table th,
.order-items-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.order-items-table th {
    background-color: var(--light-gray);
    font-weight: bold;
}

.order-total {
    text-align: right;
    margin-top: 20px;
    font-weight: bold;
    font-size: 18px;
}

/* Data Export Styles */
.data-export-section {
    padding: 20px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
}

.data-export-section h2 {
    font-size: 22px;
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.data-export-section h2 i {
    margin-right: 10px;
}

.section-description {
    margin-bottom: 20px;
    color: var(--dark-gray);
    font-size: 16px;
}

.export-options-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.export-form {
    flex: 1;
    min-width: 300px;
}

.export-info {
    flex: 1;
    min-width: 300px;
    background-color: var(--light-gray);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
}

.date-input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 16px;
}

.export-destinations {
    margin-top: 20px;
    margin-bottom: 20px;
}

.export-destinations h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--text-color);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.checkbox-item label {
    font-size: 16px;
    cursor: pointer;
}

.conditional-options {
    background-color: rgba(52, 152, 219, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    border-left: 3px solid var(--primary-color);
}

.help-text {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.help-text i {
    color: var(--primary-color);
}

.export-actions {
    margin-top: 20px;
}

.export-btn {
    background-color: var(--secondary-color);
    color: white;
    padding: 12px 20px;
    font-size: 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.export-btn:hover {
    background-color: var(--secondary-dark);
}

.export-contents {
    list-style-type: none;
    padding: 0;
    margin: 15px 0;
}

.export-contents li {
    margin-bottom: 10px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.export-contents li i {
    color: var(--secondary-color);
    margin-top: 3px;
}

.auto-export-settings {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--medium-gray);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--secondary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--secondary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Success and Error Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: var(--border-radius);
    max-width: 500px;
    position: relative;
}

.success-message, .error-message {
    text-align: center;
    padding: 20px 0;
}

.success-message i, .error-message i {
    font-size: 48px;
    margin-bottom: 15px;
}

.success-message i {
    color: var(--secondary-color);
}

.error-message i {
    color: var(--danger-color);
}

.success-message h2, .error-message h2 {
    margin-top: 0;
    margin-bottom: 10px;
}

.success-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.success-actions button {
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 16px;
    background-color: var(--primary-color);
    color: white;
    transition: background-color 0.2s;
}

.success-actions button:hover {
    background-color: var(--primary-dark);
}

.success-actions p {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--secondary-color);
    font-weight: bold;
}