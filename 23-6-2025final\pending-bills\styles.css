/* Pending Bills Styles */

.pending-bills-container {
    margin-bottom: 30px;
}

.pending-bills-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.total-section {
    display: flex;
    gap: 20px;
}

.total-item {
    display: flex;
    flex-direction: column;
}

.total-label {
    font-size: 14px;
    color: #666;
}

.total-value {
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

.actions-section {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 10px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: bold;
    transition: background-color 0.3s;
}

.action-btn:hover {
    background-color: #45a049;
}

.bills-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.bill-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s, box-shadow 0.3s;
}

.bill-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.bill-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.bill-id {
    font-weight: bold;
    color: #333;
}

.bill-date {
    color: #666;
    font-size: 14px;
}

.bill-customer {
    font-weight: bold;
    margin-bottom: 5px;
}

.bill-table {
    color: #666;
    margin-bottom: 10px;
}

.bill-items {
    margin-bottom: 10px;
    color: #666;
}

.bill-total {
    font-weight: bold;
    font-size: 18px;
    margin-top: auto;
    margin-bottom: 15px;
}

.bill-notes {
    font-style: italic;
    color: #666;
    margin-bottom: 15px;
    font-size: 14px;
    background-color: #f9f9f9;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #ddd;
}

.bill-actions {
    display: flex;
    gap: 10px;
}

.bill-btn {
    flex: 1;
    padding: 8px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    transition: background-color 0.3s;
}

.view-btn {
    background-color: #2196F3;
    color: white;
}

.view-btn:hover {
    background-color: #0b7dda;
}

.pay-btn {
    background-color: #4CAF50;
    color: white;
}

.pay-btn:hover {
    background-color: #45a049;
}

.payment-page-btn {
    background-color: #2196F3;
    color: white;
}

.payment-page-btn:hover {
    background-color: #0b7dda;
}

.delete-btn {
    background-color: #f44336;
    color: white;
}

.delete-btn:hover {
    background-color: #d32f2f;
}

.loading {
    grid-column: 1 / -1;
    text-align: center;
    padding: 50px;
    color: #666;
}

.loading i {
    font-size: 30px;
    margin-bottom: 10px;
    color: #2196F3;
}

.no-bills {
    grid-column: 1 / -1;
    text-align: center;
    padding: 50px;
    background-color: #f9f9f9;
    border-radius: 8px;
    color: #666;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}

/* Bill Details Styles */
.bill-details {
    padding: 10px;
}

.bill-details-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.bill-details-id {
    font-size: 20px;
    font-weight: bold;
}

.bill-details-date {
    color: #666;
}

.bill-details-section {
    margin-bottom: 20px;
}

.bill-details-section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.bill-details-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.bill-details-info p {
    margin: 5px 0;
}

.bill-details-items {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.bill-details-items th, .bill-details-items td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.bill-details-items th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.bill-details-total {
    text-align: right;
    font-weight: bold;
    font-size: 18px;
    margin-top: 10px;
}

.bill-details-notes {
    font-style: italic;
    color: #666;
    margin-top: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border-left: 3px solid #ddd;
}

.bill-details-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.bill-details-btn {
    flex: 1;
    padding: 10px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

/* Payment Modal Styles */
.payment-options {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.payment-btn {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    transition: all 0.3s;
}

.payment-btn i {
    font-size: 24px;
}

.payment-btn:hover {
    transform: translateY(-5px);
}

#pay-cash {
    background-color: #4CAF50;
    color: white;
}

#pay-cash:hover {
    background-color: #45a049;
}

#pay-card {
    background-color: #2196F3;
    color: white;
}

#pay-card:hover {
    background-color: #0b7dda;
}

/* Secondary navigation for back button */
.secondary-nav {
    margin-top: 30px;
}

.secondary-nav ul {
    display: flex;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .bills-list {
        grid-template-columns: 1fr;
    }

    .pending-bills-header {
        flex-direction: column;
        gap: 15px;
    }

    .total-section {
        width: 100%;
        justify-content: space-between;
    }

    .actions-section {
        width: 100%;
    }

    .action-btn {
        flex: 1;
        justify-content: center;
    }

    .bill-details-info {
        grid-template-columns: 1fr;
    }

    .payment-options {
        flex-direction: column;
    }

    .payment-btn {
        padding: 20px;
    }
}
