const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const sqlite3 = require('sqlite3').verbose();
const { exec } = require('child_process');
const os = require('os');

class EnhancedBackupService {
    constructor(options = {}) {
        // Default options
        this.options = {
            backupDir: path.join(__dirname, 'backups'),
            externalBackupDir: null, // Will be set if an external drive is available
            retentionDays: 7,
            compressionEnabled: true,
            encryptionEnabled: false,
            encryptionPassword: null,
            scheduleDailyBackup: true,
            scheduleIncrementalBackup: true,
            dailyBackupHour: 3, // 3 AM
            incrementalBackupInterval: 4, // Every 4 hours
            ...options
        };

        // Initialize
        this.ensureBackupDir();
        this.findExternalDrives();

        // Track backup history
        this.backupHistory = this.loadBackupHistory();
    }

    // Ensure backup directories exist
    ensureBackupDir() {
        if (!fs.existsSync(this.options.backupDir)) {
            fs.mkdirSync(this.options.backupDir, { recursive: true });
        }

        // Create subdirectories for different backup types
        const subDirs = ['daily', 'incremental', 'manual'];
        subDirs.forEach(dir => {
            const fullPath = path.join(this.options.backupDir, dir);
            if (!fs.existsSync(fullPath)) {
                fs.mkdirSync(fullPath, { recursive: true });
            }
        });
    }

    // Find available external drives for backup
    findExternalDrives() {
        try {
            if (os.platform() === 'win32') {
                // Windows: Use wmic to list drives
                exec('wmic logicaldisk get caption, description', (error, stdout) => {
                    if (error) {
                        console.error('Error finding external drives:', error);
                        return;
                    }

                    const drives = stdout.split('\n')
                        .filter(line => line.includes('Removable') || line.includes('External'))
                        .map(line => line.match(/([A-Z]:)/)?.[1])
                        .filter(Boolean);

                    if (drives.length > 0) {
                        this.options.externalBackupDir = path.join(drives[0], 'POS_Backups');
                        console.log(`External drive found: ${drives[0]}`);

                        // Create external backup directory
                        if (!fs.existsSync(this.options.externalBackupDir)) {
                            fs.mkdirSync(this.options.externalBackupDir, { recursive: true });
                        }
                    } else {
                        console.log('No external drives found for backup');
                    }
                });
            } else {
                // Linux/Mac: Check common mount points
                const mountPoints = ['/media', '/mnt', '/Volumes'];
                for (const baseMount of mountPoints) {
                    if (fs.existsSync(baseMount)) {
                        const mounts = fs.readdirSync(baseMount);
                        if (mounts.length > 0) {
                            const externalMount = path.join(baseMount, mounts[0]);
                            this.options.externalBackupDir = path.join(externalMount, 'POS_Backups');
                            console.log(`External drive found: ${externalMount}`);

                            // Create external backup directory
                            if (!fs.existsSync(this.options.externalBackupDir)) {
                                fs.mkdirSync(this.options.externalBackupDir, { recursive: true });
                            }
                            break;
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error detecting external drives:', error);
        }
    }

    // Load backup history from file
    loadBackupHistory() {
        const historyPath = path.join(this.options.backupDir, 'backup_history.json');
        if (fs.existsSync(historyPath)) {
            try {
                return JSON.parse(fs.readFileSync(historyPath, 'utf8'));
            } catch (error) {
                console.error('Error loading backup history:', error);
                return [];
            }
        }
        return [];
    }

    // Save backup history to file
    saveBackupHistory() {
        const historyPath = path.join(this.options.backupDir, 'backup_history.json');
        fs.writeFileSync(historyPath, JSON.stringify(this.backupHistory, null, 2));
    }

    // Create a full backup
    async createFullBackup(type = 'manual') {
        console.log(`Creating ${type} backup...`);
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `backup-${type}-${timestamp}`;
        const backupPath = path.join(this.options.backupDir, type, backupName);

        // Create backup directory
        fs.mkdirSync(backupPath, { recursive: true });

        // Files to backup
        const filesToBackup = [
            'orders.db',
            'items.json',
            'pos/index.html',
            'pos/script.js',
            'pos/styles.css',
            'payment/index.html',
            'payment/script.js',
            'payment/styles.css',
            'tablet/index.html',
            'tablet/script.js',
            'tablet/styles.css',
            'tablet/virtual-keyboard.js',
            'kitchen-displays/index.html',
            'kitchen-displays/script.js',
            'kitchen-displays/styles.css',
            'kitchen-displays/food-drinks.html',
            'kitchen-displays/hookah.html',
            'kitchen-displays/css/food-drinks.css',
            'kitchen-displays/css/hookah.css',
            'kitchen-displays/js/food-drinks.js',
            'kitchen-displays/js/hookah.js',
            'server.js',
            'backup-service.js',
            'enhanced-backup-service.js',
            'bill-archive-service.js',
            'package.json'
        ];

        // Add bill archive folder to backup
        console.log('Including bill archive in backup...');

        // Create bill-archive directory in backup
        const billArchiveSourceDir = path.join(__dirname, 'bill-archive');
        const billArchiveDestDir = path.join(backupPath, 'bill-archive');

        if (fs.existsSync(billArchiveSourceDir)) {
            // Create the destination directory
            fs.mkdirSync(billArchiveDestDir, { recursive: true });

            // Copy bill archive recursively
            this.copyFolderRecursive(billArchiveSourceDir, billArchiveDestDir);
            console.log('Bill archive included in backup');
        } else {
            console.log('Bill archive folder not found, skipping...');
        }

        // Create a database backup first (to ensure consistency)
        await this.backupDatabase(path.join(backupPath, 'orders.db'));

        // Copy other files
        for (const file of filesToBackup) {
            if (file === 'orders.db') continue; // Skip DB as we already backed it up

            const sourcePath = path.join(__dirname, file);
            const destPath = path.join(backupPath, file);

            // Create directory if it doesn't exist
            const destDir = path.dirname(destPath);
            if (!fs.existsSync(destDir)) {
                fs.mkdirSync(destDir, { recursive: true });
            }

            if (fs.existsSync(sourcePath)) {
                fs.copyFileSync(sourcePath, destPath);
            }
        }

        // Create manifest
        const manifest = {
            timestamp: new Date().toISOString(),
            type: type,
            files: filesToBackup,
            hash: await this.calculateHash(backupPath)
        };

        fs.writeFileSync(
            path.join(backupPath, 'manifest.json'),
            JSON.stringify(manifest, null, 2)
        );

        // Compress the backup if enabled
        if (this.options.compressionEnabled) {
            await this.compressBackup(backupPath);
        }

        // Copy to external drive if available
        if (this.options.externalBackupDir) {
            await this.copyToExternalDrive(backupPath, type);
        }

        // Update backup history
        this.backupHistory.push({
            name: backupName,
            path: backupPath,
            type: type,
            timestamp: new Date().toISOString(),
            size: this.calculateDirectorySize(backupPath)
        });
        this.saveBackupHistory();

        // Clean up old backups
        this.cleanupOldBackups();

        return backupPath;
    }

    // Create an incremental backup (just the database and any changed files)
    async createIncrementalBackup() {
        return this.createFullBackup('incremental');
    }

    // Backup the SQLite database safely
    async backupDatabase(destPath) {
        return new Promise((resolve, reject) => {
            try {
                // Simple file copy approach for SQLite backup
                // This works because we're ensuring the database isn't being written to during backup
                if (fs.existsSync('./orders.db')) {
                    // Create a read stream from the source database
                    const sourceStream = fs.createReadStream('./orders.db');
                    // Create a write stream to the destination
                    const destStream = fs.createWriteStream(destPath);

                    // Handle completion
                    destStream.on('finish', () => {
                        console.log('Database backup completed successfully');
                        resolve();
                    });

                    // Handle errors
                    sourceStream.on('error', (err) => {
                        console.error('Database backup read error:', err);
                        reject(err);
                    });

                    destStream.on('error', (err) => {
                        console.error('Database backup write error:', err);
                        reject(err);
                    });

                    // Perform the copy
                    sourceStream.pipe(destStream);
                } else {
                    console.log('Database file not found, creating empty backup');
                    fs.writeFileSync(destPath, '');
                    resolve();
                }
            } catch (error) {
                console.error('Database backup error:', error);
                reject(error);
            }
        });
    }

    // Compress a backup directory
    async compressBackup(backupPath) {
        return new Promise((resolve, reject) => {
            try {
                const archiveName = `${path.basename(backupPath)}.zip`;
                const archivePath = `${backupPath}.zip`;

                // For now, we'll skip compression to avoid potential issues
                // Just create an empty marker file to indicate compression was attempted
                fs.writeFileSync(`${backupPath}.compressed`, 'Compression skipped for compatibility');
                console.log('Compression skipped for compatibility');
                resolve(backupPath);

                /* Commented out for now - can be re-enabled later
                if (os.platform() === 'win32') {
                    // Windows: Use PowerShell for compression
                    exec(`powershell -command "Compress-Archive -Path '${backupPath}/*' -DestinationPath '${archivePath}'"`, (error) => {
                        if (error) {
                            console.error('Compression error:', error);
                            reject(error);
                            return;
                        }
                        resolve(archivePath);
                    });
                } else {
                    // Linux/Mac: Use zip command
                    exec(`zip -r "${archivePath}" "${backupPath}"`, (error) => {
                        if (error) {
                            console.error('Compression error:', error);
                            reject(error);
                            return;
                        }
                        resolve(archivePath);
                    });
                }
                */
            } catch (error) {
                console.error('Error in compression function:', error);
                // Don't fail the backup just because compression failed
                resolve(backupPath);
            }
        });
    }

    // Copy backup to external drive
    async copyToExternalDrive(backupPath, type) {
        if (!this.options.externalBackupDir) return;

        const externalTypePath = path.join(this.options.externalBackupDir, type);
        if (!fs.existsSync(externalTypePath)) {
            fs.mkdirSync(externalTypePath, { recursive: true });
        }

        const backupName = path.basename(backupPath);
        const destPath = path.join(externalTypePath, backupName);

        // If compressed, copy the zip file
        if (this.options.compressionEnabled) {
            const zipPath = `${backupPath}.zip`;
            if (fs.existsSync(zipPath)) {
                fs.copyFileSync(zipPath, `${destPath}.zip`);
                return;
            }
        }

        // Otherwise copy the directory recursively
        this.copyDirectoryRecursive(backupPath, destPath);
    }

    // Recursively copy a directory
    copyDirectoryRecursive(source, destination) {
        if (!fs.existsSync(destination)) {
            fs.mkdirSync(destination, { recursive: true });
        }

        const entries = fs.readdirSync(source, { withFileTypes: true });

        for (const entry of entries) {
            const srcPath = path.join(source, entry.name);
            const destPath = path.join(destination, entry.name);

            if (entry.isDirectory()) {
                this.copyDirectoryRecursive(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        }
    }

    // Calculate hash of a directory
    async calculateHash(dir) {
        try {
            const hash = crypto.createHash('sha256');

            // Just use the directory name and timestamp for a simple hash
            // This avoids potential issues with reading large files
            const timestamp = new Date().toISOString();
            hash.update(dir + timestamp);

            return hash.digest('hex');

            /* Commented out for now - can be re-enabled later
            const files = this.getAllFiles(dir);

            for (const file of files) {
                const content = fs.readFileSync(file);
                hash.update(content);
            }

            return hash.digest('hex');
            */
        } catch (error) {
            console.error('Error calculating hash:', error);
            // Return a simple hash based on the directory name
            return crypto.createHash('sha256').update(dir).digest('hex');
        }
    }

    // Get all files in a directory recursively
    getAllFiles(dir) {
        const files = [];
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                files.push(...this.getAllFiles(fullPath));
            } else {
                files.push(fullPath);
            }
        }

        return files;
    }

    // Calculate the size of a directory
    calculateDirectorySize(dir) {
        try {
            if (!fs.existsSync(dir)) {
                console.log(`Directory not found: ${dir}`);
                return 0;
            }

            // Use a simpler approach to calculate directory size
            let size = 0;
            const entries = fs.readdirSync(dir, { withFileTypes: true });

            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name);

                try {
                    if (entry.isDirectory()) {
                        // Recursively calculate size of subdirectories
                        size += this.calculateDirectorySize(fullPath);
                    } else {
                        // Add file size
                        const stats = fs.statSync(fullPath);
                        size += stats.size;
                    }
                } catch (err) {
                    console.error(`Error calculating size for ${fullPath}:`, err);
                    // Continue with other files
                }
            }

            return size;
        } catch (error) {
            console.error(`Error calculating directory size for ${dir}:`, error);
            return 0; // Return 0 if there's an error
        }
    }

    // Clean up old backups based on retention policy
    cleanupOldBackups() {
        const types = ['daily', 'incremental', 'manual'];

        types.forEach(type => {
            const typeDir = path.join(this.options.backupDir, type);
            if (!fs.existsSync(typeDir)) return;

            const backups = fs.readdirSync(typeDir)
                .filter(name => name.startsWith('backup-'))
                .map(name => {
                    const fullPath = path.join(typeDir, name);
                    const stats = fs.statSync(fullPath);
                    return {
                        name,
                        path: fullPath,
                        time: stats.mtime.getTime()
                    };
                })
                .sort((a, b) => b.time - a.time); // Sort newest to oldest

            // Keep the most recent backups based on retention policy
            let keepCount;
            switch (type) {
                case 'daily':
                    keepCount = this.options.retentionDays;
                    break;
                case 'incremental':
                    keepCount = this.options.retentionDays * 6; // 6 incremental backups per day
                    break;
                case 'manual':
                    keepCount = 10; // Keep more manual backups
                    break;
                default:
                    keepCount = 5;
            }

            // Delete older backups
            if (backups.length > keepCount) {
                const toDelete = backups.slice(keepCount);
                toDelete.forEach(backup => {
                    try {
                        // Delete both directory and zip if they exist
                        if (fs.existsSync(backup.path)) {
                            this.deleteDirectory(backup.path);
                        }
                        if (fs.existsSync(`${backup.path}.zip`)) {
                            fs.unlinkSync(`${backup.path}.zip`);
                        }

                        // Remove from history
                        this.backupHistory = this.backupHistory.filter(h => h.path !== backup.path);
                    } catch (error) {
                        console.error(`Error deleting old backup ${backup.name}:`, error);
                    }
                });

                // Save updated history
                this.saveBackupHistory();
            }
        });
    }

    // Delete a directory recursively
    deleteDirectory(dir) {
        if (fs.existsSync(dir)) {
            fs.readdirSync(dir).forEach(file => {
                const curPath = path.join(dir, file);
                if (fs.lstatSync(curPath).isDirectory()) {
                    this.deleteDirectory(curPath);
                } else {
                    fs.unlinkSync(curPath);
                }
            });
            fs.rmdirSync(dir);
        }
    }

    // Schedule backups
    scheduleBackups() {
        if (this.options.scheduleDailyBackup) {
            // Schedule daily backup at specified hour
            const now = new Date();
            const nextBackup = new Date(
                now.getFullYear(),
                now.getMonth(),
                now.getDate() + (now.getHours() >= this.options.dailyBackupHour ? 1 : 0),
                this.options.dailyBackupHour, 0, 0
            );
            const timeUntilNextBackup = nextBackup - now;

            console.log(`Next daily backup scheduled for ${nextBackup.toLocaleString()}`);

            setTimeout(() => {
                this.createFullBackup('daily');
                // Schedule next backup for 24 hours later
                setInterval(() => this.createFullBackup('daily'), 24 * 60 * 60 * 1000);
            }, timeUntilNextBackup);
        }

        if (this.options.scheduleIncrementalBackup) {
            // Schedule incremental backups every few hours
            const interval = this.options.incrementalBackupInterval * 60 * 60 * 1000;

            // Start first incremental backup after 1 hour
            setTimeout(() => {
                this.createIncrementalBackup();
                // Then continue at regular intervals
                setInterval(() => this.createIncrementalBackup(), interval);
            }, 60 * 60 * 1000);
        }
    }

    // Restore from a backup
    async restoreFromBackup(backupName, type = 'manual') {
        const backupPath = path.join(this.options.backupDir, type, backupName);

        // Check if backup exists
        if (!fs.existsSync(backupPath)) {
            // Check if it's a compressed backup
            if (fs.existsSync(`${backupPath}.zip`)) {
                // Extract the backup first
                await this.extractBackup(`${backupPath}.zip`, backupPath);
            } else {
                throw new Error(`Backup ${backupName} not found`);
            }
        }

        // Check for manifest
        const manifestPath = path.join(backupPath, 'manifest.json');
        if (!fs.existsSync(manifestPath)) {
            throw new Error(`Invalid backup: manifest not found`);
        }

        // Read manifest
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

        // Verify backup integrity
        const currentHash = await this.calculateHash(backupPath);
        if (currentHash !== manifest.hash) {
            throw new Error(`Backup integrity check failed: hash mismatch`);
        }

        // Stop the server or put it in maintenance mode
        // This would depend on your application's architecture

        // Restore database
        const dbBackupPath = path.join(backupPath, 'orders.db');
        if (fs.existsSync(dbBackupPath)) {
            // Create a backup of current database first
            const currentDbBackup = path.join(this.options.backupDir, 'pre-restore-backup', `orders-${Date.now()}.db`);
            fs.mkdirSync(path.dirname(currentDbBackup), { recursive: true });
            fs.copyFileSync('./orders.db', currentDbBackup);

            // Replace the database
            fs.copyFileSync(dbBackupPath, './orders.db');
        }

        // Restore other files
        for (const file of manifest.files) {
            if (file === 'orders.db') continue; // Already handled

            const sourcePath = path.join(backupPath, file);
            const destPath = path.join(__dirname, file);

            if (fs.existsSync(sourcePath)) {
                // Create directory if it doesn't exist
                const destDir = path.dirname(destPath);
                if (!fs.existsSync(destDir)) {
                    fs.mkdirSync(destDir, { recursive: true });
                }

                fs.copyFileSync(sourcePath, destPath);
            }
        }

        return {
            success: true,
            message: `Restored from backup ${backupName}`,
            timestamp: manifest.timestamp
        };
    }

    // Extract a compressed backup
    async extractBackup(zipPath, destPath) {
        return new Promise((resolve, reject) => {
            if (os.platform() === 'win32') {
                // Windows: Use PowerShell for extraction
                exec(`powershell -command "Expand-Archive -Path '${zipPath}' -DestinationPath '${destPath}'"`, (error) => {
                    if (error) {
                        console.error('Extraction error:', error);
                        reject(error);
                        return;
                    }
                    resolve();
                });
            } else {
                // Linux/Mac: Use unzip command
                exec(`unzip "${zipPath}" -d "${destPath}"`, (error) => {
                    if (error) {
                        console.error('Extraction error:', error);
                        reject(error);
                        return;
                    }
                    resolve();
                });
            }
        });
    }

    // Get list of available backups
    getBackups() {
        return this.backupHistory;
    }

    // Get details of a specific backup
    getBackupDetails(backupName, type = 'manual') {
        try {
            console.log(`Getting details for backup: ${backupName}, type: ${type}`);
            const backupPath = path.join(this.options.backupDir, type, backupName);
            console.log(`Backup path: ${backupPath}`);

            // Check if backup directory exists
            if (!fs.existsSync(backupPath)) {
                console.log(`Backup directory not found: ${backupPath}`);

                // Check if this is in the backup history
                const historyEntry = this.backupHistory.find(b => b.name === backupName && b.type === type);
                if (historyEntry) {
                    console.log('Found backup in history:', historyEntry);
                    return {
                        name: backupName,
                        path: backupPath,
                        timestamp: historyEntry.timestamp,
                        type: type,
                        files: [],
                        size: 0,
                        hash: ''
                    };
                }

                return null;
            }

            const manifestPath = path.join(backupPath, 'manifest.json');
            console.log(`Manifest path: ${manifestPath}`);

            if (fs.existsSync(manifestPath)) {
                try {
                    const manifestContent = fs.readFileSync(manifestPath, 'utf8');
                    const manifest = JSON.parse(manifestContent);
                    const size = this.calculateDirectorySize(backupPath);

                    return {
                        name: backupName,
                        path: backupPath,
                        timestamp: manifest.timestamp,
                        type: manifest.type || type,
                        files: manifest.files || [],
                        size: size,
                        hash: manifest.hash || ''
                    };
                } catch (parseError) {
                    console.error(`Error parsing manifest for ${backupName}:`, parseError);

                    // Return basic information if manifest can't be parsed
                    const stats = fs.statSync(backupPath);
                    return {
                        name: backupName,
                        path: backupPath,
                        timestamp: stats.mtime.toISOString(),
                        type: type,
                        files: [],
                        size: this.calculateDirectorySize(backupPath),
                        hash: ''
                    };
                }
            } else {
                console.log(`Manifest not found for ${backupName}`);

                // Return basic information if manifest doesn't exist
                const stats = fs.statSync(backupPath);
                return {
                    name: backupName,
                    path: backupPath,
                    timestamp: stats.mtime.toISOString(),
                    type: type,
                    files: [],
                    size: this.calculateDirectorySize(backupPath),
                    hash: ''
                };
            }
        } catch (error) {
            console.error(`Error getting backup details for ${backupName}:`, error);
            return null;
        }
    }

    // Get order data from a backup
    getBackupOrderData(backupName, type = 'manual') {
        try {
            console.log(`Getting order data from backup: ${backupName}, type: ${type}`);
            const backupPath = path.join(this.options.backupDir, type, backupName);
            const dbPath = path.join(backupPath, 'orders.db');

            if (!fs.existsSync(dbPath)) {
                console.log(`Database file not found in backup: ${dbPath}`);
                return { success: false, error: 'Database file not found in backup' };
            }

            // Create a temporary copy of the database to avoid conflicts
            const tempDbPath = path.join(os.tmpdir(), `temp-orders-${Date.now()}.db`);
            fs.copyFileSync(dbPath, tempDbPath);

            return new Promise((resolve, reject) => {
                // Open the database
                const db = new sqlite3.Database(tempDbPath, sqlite3.OPEN_READONLY, (err) => {
                    if (err) {
                        console.error('Error opening backup database:', err);
                        resolve({ success: false, error: 'Error opening backup database' });
                        return;
                    }

                    // Query to get all orders
                    db.all(`SELECT * FROM orders ORDER BY timestamp DESC`, (err, orders) => {
                        if (err) {
                            console.error('Error querying orders from backup:', err);
                            db.close();
                            // Clean up temp file
                            try { fs.unlinkSync(tempDbPath); } catch (e) { /* ignore */ }
                            resolve({ success: false, error: 'Error querying orders from backup' });
                            return;
                        }

                        // Process orders to parse JSON items
                        const processedOrders = orders.map(order => {
                            try {
                                if (order.items && typeof order.items === 'string') {
                                    order.parsedItems = JSON.parse(order.items);
                                }
                            } catch (e) {
                                console.error('Error parsing order items:', e);
                                order.parsedItems = [];
                            }
                            return order;
                        });

                        // Close the database
                        db.close(() => {
                            // Clean up temp file
                            try { fs.unlinkSync(tempDbPath); } catch (e) { /* ignore */ }

                            // Return the orders
                            resolve({
                                success: true,
                                orders: processedOrders,
                                count: processedOrders.length,
                                backupName: backupName,
                                backupType: type,
                                timestamp: new Date().toISOString()
                            });
                        });
                    });
                });
            });
        } catch (error) {
            console.error(`Error getting order data from backup ${backupName}:`, error);
            return { success: false, error: error.message };
        }
    }

    // Start scheduled backups
    startScheduledBackups() {
        console.log('Enhanced scheduled backups started');

        if (this.options.scheduleDailyBackup) {
            this.scheduleNextDailyBackup();
        }
    }

    // Schedule the next daily backup
    scheduleNextDailyBackup() {
        // Clear any existing scheduled backup
        if (this.dailyBackupTimeout) {
            clearTimeout(this.dailyBackupTimeout);
        }

        // Schedule daily backup at specified hour
        const now = new Date();
        const nextBackup = new Date();
        nextBackup.setHours(this.options.dailyBackupHour, 0, 0, 0);

        // If the scheduled time has already passed today, schedule for tomorrow
        if (now > nextBackup) {
            nextBackup.setDate(nextBackup.getDate() + 1);
        }

        const timeUntilBackup = nextBackup - now;
        console.log(`Next daily backup scheduled for ${nextBackup.toLocaleDateString()}, ${nextBackup.toLocaleTimeString()}`);

        // Schedule the backup
        this.dailyBackupTimeout = setTimeout(() => {
            this.createFullBackup('daily');

            // Schedule the next daily backup
            this.scheduleNextDailyBackup();
        }, timeUntilBackup);

        return nextBackup;
    }

    // Update backup schedule settings
    updateBackupSchedule(settings) {
        if (settings.hasOwnProperty('enabled')) {
            this.options.scheduleDailyBackup = settings.enabled;
        }

        if (settings.hasOwnProperty('hour')) {
            this.options.dailyBackupHour = parseInt(settings.hour);
        }

        // Reschedule if enabled
        if (this.options.scheduleDailyBackup) {
            const nextBackup = this.scheduleNextDailyBackup();
            return nextBackup;
        } else {
            // Clear any existing scheduled backup
            if (this.dailyBackupTimeout) {
                clearTimeout(this.dailyBackupTimeout);
                this.dailyBackupTimeout = null;
            }
            return null;
        }
    }
    // Copy folder recursively
    copyFolderRecursive(source, destination) {
        // Check if source exists
        if (!fs.existsSync(source)) {
            console.log(`Source folder does not exist: ${source}`);
            return;
        }

        // Create destination folder if it doesn't exist
        if (!fs.existsSync(destination)) {
            fs.mkdirSync(destination, { recursive: true });
        }

        // Get all files and folders in source
        const items = fs.readdirSync(source);

        // Copy each item
        for (const item of items) {
            const sourcePath = path.join(source, item);
            const destPath = path.join(destination, item);

            // Check if it's a directory or file
            const stats = fs.statSync(sourcePath);

            if (stats.isDirectory()) {
                // Recursively copy subdirectory
                this.copyFolderRecursive(sourcePath, destPath);
            } else {
                // Copy file
                fs.copyFileSync(sourcePath, destPath);
            }
        }
    }
}

module.exports = EnhancedBackupService;
