const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class BackupService {
    constructor() {
        this.backupDir = path.join(__dirname, 'backups');
        this.ensureBackupDir();
    }

    ensureBackupDir() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
        }
    }

    async createBackup() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `backup-${timestamp}`;
        const backupPath = path.join(this.backupDir, backupName);

        // Create backup directory
        fs.mkdirSync(backupPath, { recursive: true });

        // Files to backup
        const filesToBackup = [
            'orders.db',
            'pos/index.html',
            'pos/script.js',
            'pos/styles.css',
            'payment/index.html',
            'payment/script.js',
            'payment/styles.css',
            'tablet/index.html',
            'tablet/script.js',
            'tablet/styles.css',
            'kitchen-displays/index.html',
            'kitchen-displays/script.js',
            'kitchen-displays/styles.css'
        ];

        // Copy files
        for (const file of filesToBackup) {
            const sourcePath = path.join(__dirname, file);
            const destPath = path.join(backupPath, file);

            // Create directory if it doesn't exist
            const destDir = path.dirname(destPath);
            if (!fs.existsSync(destDir)) {
                fs.mkdirSync(destDir, { recursive: true });
            }

            if (fs.existsSync(sourcePath)) {
                fs.copyFileSync(sourcePath, destPath);
            }
        }

        // Create manifest
        const manifest = {
            timestamp: new Date().toISOString(),
            files: filesToBackup,
            hash: await this.calculateHash(backupPath)
        };

        fs.writeFileSync(
            path.join(backupPath, 'manifest.json'),
            JSON.stringify(manifest, null, 2)
        );

        return backupPath;
    }

    async calculateHash(dir) {
        const hash = crypto.createHash('sha256');
        const files = this.getAllFiles(dir);

        for (const file of files) {
            const content = fs.readFileSync(file);
            hash.update(content);
        }

        return hash.digest('hex');
    }

    getAllFiles(dir) {
        const files = [];
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                files.push(...this.getAllFiles(fullPath));
            } else {
                files.push(fullPath);
            }
        }

        return files;
    }

    scheduleBackups() {
        // Schedule daily backups at 2 AM
        const now = new Date();
        const nextBackup = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate() + 1,
            2, 0, 0
        );
        const timeUntilNextBackup = nextBackup - now;

        setTimeout(() => {
            this.createBackup();
            // Schedule next backup for 24 hours later
            setInterval(() => this.createBackup(), 24 * 60 * 60 * 1000);
        }, timeUntilNextBackup);
    }
}

module.exports = BackupService; 