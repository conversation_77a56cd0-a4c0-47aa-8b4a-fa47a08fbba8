{"id": 340, "tableNumber": "1", "customerName": "11", "items": [{"name": "Watermelon", "price": 25, "quantity": 1, "notes": ""}, {"name": "<PERSON><PERSON>", "price": 7.99, "quantity": 1, "notes": ""}, {"name": "Tea", "price": 3, "quantity": 1, "notes": ""}, {"name": "Barbican", "price": 4.99, "quantity": 1, "notes": ""}], "coverCharge": "[object Object]", "paid": 1, "status": "pending", "kitchenStatus": "pending", "report": null, "timestamp": "2025-04-11T16:51:04.466Z", "paymentMethod": "card", "transactionId": null, "payment": {"paymentMethod": "card", "tip": 0, "subtotal": 40.980000000000004, "tax": 3.8, "taxRate": 0.09272816007808687, "gst": 2.05, "gstRate": 0.05002440214738897, "pst": 1.75, "pstRate": 0.0427037579306979, "total": 44.78, "transactionId": null}, "archiveTimestamp": "2025-04-11T16:51:11.288Z"}