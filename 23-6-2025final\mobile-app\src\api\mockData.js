// Mock data for testing the notification system

// Mock notifications
export const mockNotifications = [
  {
    id: '1',
    title: 'Live Music Night - This Friday!',
    body: 'Join us for live music and special hookah flavors',
    data: { type: 'event', eventId: '1' },
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    read: false
  },
  {
    id: '2',
    title: 'Your Reservation is Confirmed',
    body: 'Table for 4 on Saturday, 8:00 PM',
    data: { type: 'reservation', reservationId: '123' },
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    read: false
  },
  {
    id: '3',
    title: 'New Flavor Alert!',
    body: 'Try our new Blueberry Mint hookah flavor',
    data: { type: 'promotion', promotionId: '456' },
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    read: false
  },
  {
    id: '4',
    title: 'Weekend Special',
    body: 'Get 15% off on all food items this weekend',
    data: { type: 'promotion', promotionId: '789' },
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
    read: true
  }
];

// Mock events
export const mockEvents = [
  {
    id: '1',
    title: 'Live Music Night',
    description: 'Join us for an unforgettable evening featuring live music by local artist Alex Rivera. Enjoy special hookah flavors and cocktails created just for this event.',
    date: '2025-04-19',
    time: '8:00 PM',
    location: 'Paradiz Hookah Lounge - Main Area',
    coverCharge: '$10 per person (includes one drink)',
    specialMenu: 'Exclusive hookah flavors and cocktails available only during this event',
    imageUrl: null,
    reminderSet: false,
    reminderNotificationId: null,
    performer: {
      name: 'Alex Rivera',
      description: 'Alex Rivera is a talented local musician known for his unique blend of acoustic covers and original compositions. His music creates the perfect ambiance for a relaxing evening.'
    }
  },
  {
    id: '2',
    title: 'Hookah Tasting Event',
    description: 'Sample our premium hookah flavors with expert guidance from our staff. Learn about different tobacco blends and flavor profiles.',
    date: '2025-05-10',
    time: '7:00 PM',
    location: 'Paradiz Hookah Lounge - VIP Section',
    coverCharge: '$15 per person',
    specialMenu: 'Tasting menu with 5 premium hookah flavors',
    imageUrl: null,
    reminderSet: false,
    reminderNotificationId: null,
    performer: null
  }
];

// Mock API interceptors
export const setupMockInterceptors = (api) => {
  // Mock API response for notifications
  api.interceptors.response.use(
    (response) => {
      // Only intercept if we're in development and the URL matches
      if (response.config.url.includes('/api/notifications')) {
        if (response.config.method === 'get') {
          console.log('Mocking GET /api/notifications');
          return {
            ...response,
            data: [...mockNotifications]
          };
        }
      }
      
      // Mock event details
      if (response.config.url.match(/\/api\/events\/\d+$/)) {
        const eventId = response.config.url.split('/').pop();
        const event = mockEvents.find(e => e.id === eventId);
        
        if (event) {
          console.log(`Mocking GET /api/events/${eventId}`);
          return {
            ...response,
            data: {...event}
          };
        }
      }
      
      // Mock events list
      if (response.config.url === '/api/events') {
        console.log('Mocking GET /api/events');
        return {
          ...response,
          data: [...mockEvents]
        };
      }
      
      return response;
    },
    (error) => {
      // If the API call fails, check if we should mock it
      const url = error.config.url;
      
      if (url.includes('/api/notifications')) {
        console.log('Mocking failed notification request');
        return Promise.resolve({
          data: [...mockNotifications],
          status: 200
        });
      }
      
      if (url.match(/\/api\/events\/\d+$/)) {
        const eventId = url.split('/').pop();
        const event = mockEvents.find(e => e.id === eventId);
        
        if (event) {
          console.log(`Mocking failed GET /api/events/${eventId}`);
          return Promise.resolve({
            data: {...event},
            status: 200
          });
        }
      }
      
      if (url === '/api/events') {
        console.log('Mocking failed GET /api/events');
        return Promise.resolve({
          data: [...mockEvents],
          status: 200
        });
      }
      
      return Promise.reject(error);
    }
  );
  
  // Mock API request for marking notifications as read
  api.interceptors.request.use(
    (config) => {
      if (config.url.match(/\/api\/notifications\/\d+\/read$/)) {
        const notificationId = config.url.split('/')[3];
        console.log(`Mocking PUT /api/notifications/${notificationId}/read`);
        
        // Find and mark the notification as read in our mock data
        const notification = mockNotifications.find(n => n.id === notificationId);
        if (notification) {
          notification.read = true;
        }
        
        // Return a mock response instead of making the actual request
        return {
          ...config,
          adapter: () => {
            return Promise.resolve({
              data: { success: true },
              status: 200,
              statusText: 'OK',
              headers: {},
              config: config,
              request: {}
            });
          }
        };
      }
      
      if (config.url === '/api/notifications/read-all') {
        console.log('Mocking PUT /api/notifications/read-all');
        
        // Mark all notifications as read in our mock data
        mockNotifications.forEach(notification => {
          notification.read = true;
        });
        
        // Return a mock response
        return {
          ...config,
          adapter: () => {
            return Promise.resolve({
              data: { success: true },
              status: 200,
              statusText: 'OK',
              headers: {},
              config: config,
              request: {}
            });
          }
        };
      }
      
      if (config.url.match(/\/api\/events\/\d+\/reminder$/)) {
        const eventId = config.url.split('/')[3];
        console.log(`Mocking POST /api/events/${eventId}/reminder`);
        
        // Find the event in our mock data
        const event = mockEvents.find(e => e.id === eventId);
        if (event && config.data) {
          const data = typeof config.data === 'string' ? JSON.parse(config.data) : config.data;
          event.reminderSet = data.setReminder;
          if (data.notificationId) {
            event.reminderNotificationId = data.notificationId;
          } else {
            event.reminderNotificationId = null;
          }
        }
        
        // Return a mock response
        return {
          ...config,
          adapter: () => {
            return Promise.resolve({
              data: { success: true },
              status: 200,
              statusText: 'OK',
              headers: {},
              config: config,
              request: {}
            });
          }
        };
      }
      
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};
