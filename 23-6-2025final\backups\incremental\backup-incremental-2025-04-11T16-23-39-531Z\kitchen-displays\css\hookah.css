body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.header {
    background-color: #6a1b9a;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    color: white;
}

.orders-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.order-card {
    border-left: 4px solid #6a1b9a;
    display: flex;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    gap: 10px;
}

.order-info {
    display: flex;
    align-items: center;
    width: 250px;
}

.table-number {
    font-weight: bold;
    width: 100px;
}

.customer-name {
    color: #666;
}

.order-items {
    display: flex;
    gap: 5px;
    flex: 1;
    overflow-x: auto;
    padding: 5px 0;
}

.order-item {
    padding: 5px 8px;
    background-color: #f8f8f8;
    border-radius: 4px;
    white-space: nowrap;
}

.order-footer {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 150px;
}

.order-timer {
    font-weight: bold;
    color: #666;
    width: 60px;
}

.complete-btn {
    padding: 5px 10px;
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.no-orders {
    padding: 20px;
    text-align: center;
    color: #666;
}

/* ... (rest of CSS is similar to food-drinks.css but with purple colors) */