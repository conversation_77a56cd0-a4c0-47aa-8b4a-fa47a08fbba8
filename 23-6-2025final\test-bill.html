<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Bill Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Test Bill Generation with Tax</h1>
    <p>Click the button below to generate a test bill with tax information:</p>
    
    <button id="generate-btn">Generate Test Bill</button>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('generate-btn').addEventListener('click', function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Generating test bill...';
            
            fetch('http://localhost:5000/api/test/generate-bill')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h3>Success!</h3>
                                <p>${data.message}</p>
                                <p>Bill ID: ${data.billId}</p>
                                <p>Archive Result: ${JSON.stringify(data.archiveResult, null, 2)}</p>
                                <p>You can now check the bill archive to see this bill with tax information.</p>
                                <p><a href="bills.html" target="_blank">Open Bill Archive</a></p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="error">
                                <h3>Error</h3>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>Error</h3>
                            <p>Failed to generate test bill: ${error.message}</p>
                        </div>
                    `;
                });
        });
    </script>
</body>
</html>
