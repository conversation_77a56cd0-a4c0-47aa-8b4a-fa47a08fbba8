/* General Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
}

.header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.header h1 {
    margin: 0;
    font-size: 24px;
}

/* Orders Container */
.orders-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    max-width: 100%;
    overflow-x: auto;
}

.order-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.2s;
    display: flex;
    flex-direction: column;
    min-width: 300px;
    max-width: 100%;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.order-content {
    margin-bottom: 10px;
    flex-grow: 1;
}

.order-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.table-number {
    font-size: 18px;
    font-weight: bold;
}

.customer-name {
    font-size: 14px;
    color: #666;
}

.order-items {
    margin: 10px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.order-item {
    padding: 5px 10px;
    background-color: #f8f8f8;
    border-radius: 4px;
    margin: 0;
}

.item-name {
    font-weight: 500;
}

.item-notes {
    font-size: 12px;
    color: #888;
    font-style: italic;
    margin-left: 5px;
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.order-timer {
    font-size: 14px;
    color: #666;
}

.complete-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.complete-btn:hover {
    background-color: #3e8e41;
}

/* Status Messages */
.no-orders, .error {
    text-align: center;
    padding: 20px;
    width: 100%;
    color: #666;
    font-size: 16px;
}

.error {
    color: #d32f2f;
} 