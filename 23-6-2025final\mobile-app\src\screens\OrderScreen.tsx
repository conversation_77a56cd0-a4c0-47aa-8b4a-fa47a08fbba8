import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, Alert } from 'react-native';
import { <PERSON>, Button, Card, Divider, ActivityIndicator } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { orderApi } from '../api/api';

const OrderScreen = () => {
  const navigation = useNavigation();
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // In a real app, this would come from a cart context or state management
  useEffect(() => {
    // Simulate loading cart items
    setLoading(true);
    setTimeout(() => {
      setCartItems([
        { id: 1, name: 'Double Apple Hookah', price: 25.99, quantity: 1 },
        { id: 2, name: 'Mint Hookah', price: 25.99, quantity: 1 },
        { id: 3, name: 'Hummus Plate', price: 8.99, quantity: 1 },
        { id: 4, name: 'Turkish Coffee', price: 4.99, quantity: 2 },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const increaseQuantity = (itemId) => {
    setCartItems(
      cartItems.map((item) =>
        item.id === itemId ? { ...item, quantity: item.quantity + 1 } : item
      )
    );
  };

  const decreaseQuantity = (itemId) => {
    setCartItems(
      cartItems.map((item) =>
        item.id === itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      )
    );
  };

  const removeItem = (itemId) => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => {
            setCartItems(cartItems.filter((item) => item.id !== itemId));
          },
          style: 'destructive',
        },
      ]
    );
  };

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + item.price * item.quantity, 0);
  };

  const calculateTax = () => {
    return calculateSubtotal() * 0.05; // 5% tax
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  const handlePlaceOrder = async () => {
    if (cartItems.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to your order');
      return;
    }

    setSubmitting(true);
    try {
      // In a real app, this would call the API to place the order
      // const response = await orderApi.createOrder({
      //   items: cartItems.map(item => ({
      //     id: item.id,
      //     quantity: item.quantity
      //   }))
      // });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert(
        'Order Placed',
        'Your order has been placed successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              setCartItems([]);
              navigation.navigate('Home');
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error placing order:', error);
      Alert.alert('Error', 'Failed to place your order');
    } finally {
      setSubmitting(false);
    }
  };

  const renderCartItem = ({ item }) => (
    <Card style={styles.cartItem}>
      <Card.Content>
        <View style={styles.itemHeader}>
          <Text style={styles.itemName}>{item.name}</Text>
          <Ionicons
            name="close-circle-outline"
            size={24}
            color="#E53E3E"
            onPress={() => removeItem(item.id)}
          />
        </View>
        <View style={styles.itemDetails}>
          <View style={styles.quantityControls}>
            <Button
              mode="text"
              onPress={() => decreaseQuantity(item.id)}
              disabled={item.quantity <= 1}
              style={styles.quantityButton}
              contentStyle={styles.quantityButtonContent}
            >
              <Ionicons
                name="remove"
                size={16}
                color={item.quantity <= 1 ? '#A0AEC0' : '#6B46C1'}
              />
            </Button>
            <Text style={styles.quantityText}>{item.quantity}</Text>
            <Button
              mode="text"
              onPress={() => increaseQuantity(item.id)}
              style={styles.quantityButton}
              contentStyle={styles.quantityButtonContent}
            >
              <Ionicons name="add" size={16} color="#6B46C1" />
            </Button>
          </View>
          <Text style={styles.itemPrice}>${(item.price * item.quantity).toFixed(2)}</Text>
        </View>
      </Card.Content>
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6B46C1" />
        <Text style={styles.loadingText}>Loading your order...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.cartContainer}>
        {cartItems.length > 0 ? (
          <FlatList
            data={cartItems}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.cartList}
          />
        ) : (
          <View style={styles.emptyCartContainer}>
            <Ionicons name="cart-outline" size={64} color="#A0AEC0" />
            <Text style={styles.emptyCartText}>Your cart is empty</Text>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('Menu')}
              style={styles.browseButton}
            >
              Browse Menu
            </Button>
          </View>
        )}
      </View>

      {cartItems.length > 0 && (
        <Card style={styles.summaryCard}>
          <Card.Content>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>${calculateSubtotal().toFixed(2)}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Tax (5%)</Text>
              <Text style={styles.summaryValue}>${calculateTax().toFixed(2)}</Text>
            </View>
            <Divider style={styles.divider} />
            <View style={styles.summaryRow}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>${calculateTotal().toFixed(2)}</Text>
            </View>

            <Button
              mode="contained"
              onPress={handlePlaceOrder}
              style={styles.placeOrderButton}
              loading={submitting}
              disabled={submitting}
            >
              Place Order
            </Button>
          </Card.Content>
        </Card>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#4A5568',
  },
  cartContainer: {
    flex: 1,
  },
  cartList: {
    padding: 16,
  },
  cartItem: {
    marginBottom: 12,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D3748',
    flex: 1,
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    margin: 0,
    padding: 0,
  },
  quantityButtonContent: {
    margin: 0,
    padding: 0,
  },
  quantityText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 8,
    minWidth: 24,
    textAlign: 'center',
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B46C1',
  },
  summaryCard: {
    margin: 16,
    elevation: 4,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#4A5568',
  },
  summaryValue: {
    fontSize: 16,
    color: '#2D3748',
  },
  divider: {
    marginVertical: 12,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B46C1',
  },
  placeOrderButton: {
    marginTop: 16,
    backgroundColor: '#6B46C1',
    paddingVertical: 8,
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyCartText: {
    fontSize: 18,
    color: '#4A5568',
    marginVertical: 16,
  },
  browseButton: {
    marginTop: 16,
    backgroundColor: '#6B46C1',
  },
});

export default OrderScreen;
