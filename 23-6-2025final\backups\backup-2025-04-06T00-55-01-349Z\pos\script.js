// Track the currently selected table
let selectedTable = null;
let currentOrders = []; // Store the currently displayed orders
let selectedItem = null; // Track the selected item for splitting
let lastAction = null; // Track the last split action for undo

// Function to format price to 2 decimal places
function formatPrice(price) {
    return Number(price).toFixed(2);
}

// Initialize the app
document.addEventListener('DOMContentLoaded', function () {
    setupTableButtons(); // Initialize table buttons
    displayOrders(); // Display orders when the page loads

    // Add clear button event listener
    const clearTableBtn = document.getElementById('clear-table-btn');
    if (clearTableBtn) {
        clearTableBtn.addEventListener('click', clearTable);
        console.log('Clear button listener added');
    }

    // Add refresh orders button event listener
    const refreshOrdersBtn = document.getElementById('refresh-orders-btn');
    if (refreshOrdersBtn) {
        refreshOrdersBtn.addEventListener('click', () => {
            window.location.reload();
        });
        console.log('Refresh orders button listener added');
    }

    // Add backup button event listener
    const backupBtn = document.getElementById('backup-btn');
    if (backupBtn) {
        backupBtn.addEventListener('click', () => {
            // Redirect to food-drinks display page
            window.location.href = '../kitchen-displays/food-drinks.html';
        });
        console.log('Food-Drink button listener added');
    }

    // Add backup viewer button event listener
    const backupViewerBtn = document.getElementById('backup-viewer-btn');
    if (backupViewerBtn) {
        backupViewerBtn.addEventListener('click', () => {
            // Redirect to main page
            window.location.href = '../index.html';
        });
        console.log('Main button listener added');
    }

    // Add back to ordering button event listener
    const backToOrderingBtn = document.getElementById('back-to-ordering-btn');
    if (backToOrderingBtn) {
        backToOrderingBtn.addEventListener('click', () => {
            window.location.href = '../tablet/index.html';
        });
        console.log('Back to ordering button listener added');
    }

    // Add event listeners for split modal
    setupSplitModal();

    // Add event listener for Pay Table button
    document.getElementById('pay-table-btn').addEventListener('click', () => {
        // Redirect to hookah display page
        window.location.href = '../kitchen-displays/hookah.html';
    });
});

// Setup table buttons
function setupTableButtons() {
    const tableButtonsRow1 = document.getElementById('table-buttons-row1');
    const tableButtonsRow2 = document.getElementById('table-buttons-row2');

    // Generate buttons for tables 1 to 8
    for (let i = 1; i <= 8; i++) {
        const button = document.createElement('button');
        button.className = 'table-btn';
        button.textContent = `Table ${i}`;
        button.addEventListener('click', () => pullUpTable(i.toString()));
        tableButtonsRow1.appendChild(button);
    }

    // Generate buttons for tables 9 to 15
    for (let i = 9; i <= 15; i++) {
        const button = document.createElement('button');
        button.className = 'table-btn';
        button.textContent = `Table ${i}`;
        button.addEventListener('click', () => pullUpTable(i.toString()));
        tableButtonsRow2.appendChild(button);
    }
}

// Pull Up Table
function pullUpTable(tableNumber) {
    selectedTable = tableNumber; // Update the selected table
    console.log('Selected table:', selectedTable);
    highlightSelectedTableButton(tableNumber); // Highlight the selected table button
    displayOrders(); // Refresh the table summary
}

// Highlight the Selected Table Button
function highlightSelectedTableButton(tableNumber) {
    // Remove highlight from all buttons
    document.querySelectorAll('.table-btn').forEach(button => {
        button.classList.remove('selected');
    });

    // Find and highlight the selected button
    const buttons = document.querySelectorAll('.table-btn');
    buttons.forEach(button => {
        if (button.textContent === `Table ${tableNumber}`) {
            button.classList.add('selected');
        }
    });
}

// Display Orders
async function displayOrders() {
    const customerBills = document.getElementById('customer-bills');
    customerBills.innerHTML = '';

    try {
        console.log('Fetching orders from server...');
        const response = await fetch('http://localhost:5000/orders');
        console.log('Response status:', response.status);
        const orders = await response.json();
        console.log('Orders received:', orders);

        // Always update table button colors, even if no orders are found
        updateTableButtonColors(orders);

        if (orders.length === 0) {
            console.log('No orders found');
            customerBills.innerHTML = `<p>No orders found.</p>`;
            return;
        }

        // Filter orders for the selected table
        const tableOrders = selectedTable ? orders.filter(order => order.tableNumber === selectedTable) : orders;
        console.log('Filtered orders for table:', tableOrders);

        if (tableOrders.length === 0) {
            console.log('No orders found for selected table');
            customerBills.innerHTML = `<p>No orders found for Table ${selectedTable}.</p>`;
            return;
        }

        // Group orders by table number and customer name
        const groupedOrders = {};
        tableOrders.forEach(order => {
            // Create a case-insensitive key for grouping
            const key = `${order.tableNumber}-${order.customerName.toLowerCase()}`;
            
            // Check if we already have an order with this table and customer name (case-insensitive)
            const existingKey = Object.keys(groupedOrders).find(k => 
                k.toLowerCase() === key
            );
            
            if (existingKey) {
                // Use the existing key to add items to the existing group
                const existingGroup = groupedOrders[existingKey];
                
                // Add items to the grouped order
                order.items.forEach(item => {
                    const existingItem = existingGroup.items.find(i => 
                        i.name === item.name && 
                        Math.abs(i.price - item.price) < 0.01
                    );
                    
                    if (existingItem) {
                        existingItem.quantity = (existingItem.quantity || 1) + (item.quantity || 1);
                    } else {
                        existingGroup.items.push({
                            ...item,
                            quantity: item.quantity || 1
                        });
                    }
                });
                
                // If any order is unpaid, mark the entire group as unpaid
                if (!order.paid) {
                    existingGroup.paid = false;
                }
            } else {
                // Create a new group
                groupedOrders[key] = {
                    tableNumber: order.tableNumber,
                    customerName: order.customerName,
                    items: [],
                    paid: true // Will be set to false if any order is unpaid
                };
                
                // Add items to the new group
                order.items.forEach(item => {
                    groupedOrders[key].items.push({
                        ...item,
                        quantity: item.quantity || 1
                    });
                });
                
                // If any order is unpaid, mark the entire group as unpaid
                if (!order.paid) {
                    groupedOrders[key].paid = false;
                }
            }
        });

        // Calculate total bill amount
        let totalBillAmount = 0;
        Object.values(groupedOrders).forEach(groupedOrder => {
            const total = groupedOrder.items.reduce((total, item) => total + (item.price * (item.quantity || 1)), 0);
            totalBillAmount += total;
        });

        // Update total bill amount display
        const totalBillAmountElement = document.getElementById('table-total-amount');
        if (totalBillAmountElement) {
            totalBillAmountElement.textContent = `$${formatPrice(totalBillAmount)}`;
        }

        // Add click handler for total bill button
        const totalBillBtn = document.querySelector('.total-bill-btn');
        if (totalBillBtn) {
            totalBillBtn.onclick = () => {
                if (selectedTable) {
                    // Get all unpaid orders for the selected table
                    const unpaidOrders = Object.values(groupedOrders).filter(order => !order.paid);
                    if (unpaidOrders.length > 0) {
                        // Combine all items from unpaid orders
                        const combinedItems = unpaidOrders.reduce((items, order) => {
                            order.items.forEach(item => {
                                const existingItem = items.find(i => 
                                    i.name === item.name && 
                                    Math.abs(i.price - item.price) < 0.01
                                );
                                if (existingItem) {
                                    existingItem.quantity = (existingItem.quantity || 1) + (item.quantity || 1);
                                } else {
                                    items.push({...item});
                                }
                            });
                            return items;
                        }, []);

                        // Create order data for payment
                        const orderData = {
                            tableNumber: selectedTable,
                            customerName: `Table ${selectedTable}`,
                            items: combinedItems
                        };
                        
                        // Convert order data to URL-safe string
                        const orderJson = encodeURIComponent(JSON.stringify(orderData));
                        
                        // Open new payment page with order data
                        window.location.href = `../payment/index.html?orderData=${orderJson}`;
                    } else {
                        alert('No unpaid orders found for this table');
                    }
                } else {
                    alert('Please select a table first');
                }
            };
        }

        // Display grouped orders
        Object.values(groupedOrders).forEach(groupedOrder => {
            const orderDiv = document.createElement('div');
            orderDiv.className = `customer-bill ${groupedOrder.paid ? 'paid' : ''}`;
            const total = groupedOrder.items.reduce((total, item) => total + (item.price * (item.quantity || 1)), 0);
            orderDiv.innerHTML = `
                <h4>Table ${groupedOrder.tableNumber} - ${groupedOrder.customerName}</h4>
                <ul>
                    ${groupedOrder.items.map(item => `
                        <li>
                            <button class="item-button" data-item="${item.name}" data-price="${item.price}" onclick="selectItemForSplit(this)">
                                <span class="item-name">${item.name}</span>
                                <span class="item-price">$${formatPrice(item.price)}</span>
                            </button>
                        </li>
                    `).join('')}
                </ul>
                <p class="total-amount" onclick="${!groupedOrder.paid ? `handlePay('${groupedOrder.tableNumber}', '${groupedOrder.customerName}')` : ''}" style="${!groupedOrder.paid ? 'cursor: pointer;' : ''}">Total: $${formatPrice(total)}</p>
                ${groupedOrder.paid ? '<p class="paid-status">Paid</p>' : `
                    <div class="pay-split-buttons">
                        <button class="pay-btn" onclick="handlePay('${groupedOrder.tableNumber}', '${groupedOrder.customerName}')">Pay</button>
                        <button class="split-btn" onclick="handleSplit('${groupedOrder.tableNumber}', '${groupedOrder.customerName}')">Split</button>
                    </div>
                `}
            `;
            customerBills.appendChild(orderDiv);
        });
    } catch (error) {
        console.error('Error:', error);
        customerBills.innerHTML = `<p>Failed to load orders. Please try again later.</p>`;
    }
}

// Update table button colors based on unpaid bills
function updateTableButtonColors(orders) {
    const tableButtons = document.querySelectorAll('.table-btn');
    tableButtons.forEach(button => {
        const tableNumber = button.textContent.replace('Table ', ''); // Extract table number
        const hasUnpaidOrders = orders.some(order => order.tableNumber === tableNumber && !order.paid);

        if (hasUnpaidOrders) {
            button.classList.add('unpaid'); // Add orange color for unpaid bills
        } else {
            button.classList.remove('unpaid'); // Reset to blue if no unpaid bills
        }
    });
}

// Handle Pay Button Click
function handlePay(tableNumber, customerName) {
    console.log(`Pay button clicked for Table ${tableNumber} - ${customerName}`);
    
    // Get all items from the DOM for this bill
    const billElements = document.querySelectorAll('.customer-bill');
    const billElement = Array.from(billElements).find(bill => {
        const header = bill.querySelector('h4');
        return header && header.textContent === `Table ${tableNumber} - ${customerName}`;
    });
    
    if (!billElement) {
        console.error('Bill element not found in DOM');
        alert('Error: Could not find bill details');
        return;
    }

    // Get all items from the bill
    const items = Array.from(billElement.querySelectorAll('.item-button')).map(item => {
        const itemText = item.textContent.trim();
        const priceMatch = itemText.match(/\$(\d+\.\d+)/);
        const quantityMatch = itemText.match(/x(\d+)/);
        const nameMatch = itemText.match(/^(.+?)(?:\s+x\d+|\s+\$)/);
        
        return {
            name: nameMatch ? nameMatch[1].trim() : itemText,
            price: priceMatch ? parseFloat(priceMatch[1]) : parseFloat(item.getAttribute('data-price')),
            quantity: quantityMatch ? parseInt(quantityMatch[1]) : 1
        };
    });

    if (items.length === 0) {
        console.error('No items found in bill');
        alert('Error: No items found in bill');
        return;
    }

    // Prepare order data for payment confirmation
    const orderData = {
        tableNumber: tableNumber,
        customerName: customerName,
        items: items
    };
    
    // Convert order data to URL-safe string
    const orderJson = encodeURIComponent(JSON.stringify(orderData));
    
    // Open new payment page with order data
    window.location.href = `../payment/index.html?orderData=${orderJson}`;
}

// Handle Split Button Click
async function handleSplit(tableNumber, customerName) {
    try {
        // Get current orders from backend
        const response = await fetch('http://localhost:5000/orders');
        const orders = await response.json();

        // Find the original order
        const originalOrder = orders.find(o => 
            o.tableNumber === tableNumber && 
            o.customerName === customerName
        );

        if (!originalOrder) {
            alert('Order not found');
            return;
        }

        // Get all unpaid bills for the same table
        const tableBills = orders.filter(o => 
            o.tableNumber === tableNumber && 
            !o.paid
        );

        // Create bill selection options
        const billSelection = document.getElementById('billSelection');
        billSelection.innerHTML = '';
        tableBills.forEach(bill => {
            if (bill.customerName !== customerName) {
                const label = document.createElement('label');
                label.className = 'bill-option';
                label.innerHTML = `
                    <input type="checkbox" value="${bill.tableNumber}-${bill.customerName}">
                    Table ${bill.tableNumber} - ${bill.customerName}
                `;
                billSelection.appendChild(label);
            }
        });

        // Show split modal
        const splitModal = document.getElementById('splitModal');
        splitModal.style.display = 'block';

        // Handle Confirm Split Button Click
        const confirmSplitButton = document.getElementById('confirmSplit');
        confirmSplitButton.onclick = async () => {
            const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected')).map(label => label.dataset.value);
            if (selectedBills.length === 0) {
                alert('Please select at least one bill to split with.');
                return;
            }

            const splitType = document.querySelector('input[name="splitType"]:checked').value;
            let splitAmounts = [];

            if (selectedItem) {
                // Item-level split
                const itemPrice = parseFloat(selectedItem.getAttribute('data-price'));
                const itemName = selectedItem.getAttribute('data-item');
                
                if (splitType === 'equal') {
                    const splitAmount = itemPrice / selectedBills.length;
                    splitAmounts = selectedBills.map(() => splitAmount);
                } else if (splitType === 'percentage') {
                    const percentageInputs = document.querySelectorAll('#percentageFields input');
                    let totalPercentage = 0;
                    percentageInputs.forEach(input => {
                        totalPercentage += parseFloat(input.value) || 0;
                    });

                    if (totalPercentage !== 100) {
                        alert('The total percentage must equal 100%.');
                        return;
                    }

                    splitAmounts = Array.from(percentageInputs).map(input => {
                        const percentage = parseFloat(input.value) || 0;
                        return (itemPrice * percentage) / 100;
                    });
                } else if (splitType === 'custom') {
                    const customInputs = document.querySelectorAll('#customFields input');
                    let totalAmount = 0;
                    customInputs.forEach(input => {
                        totalAmount += parseFloat(input.value) || 0;
                    });

                    if (Math.abs(totalAmount - itemPrice) > 0.01) {
                        alert('The total custom amounts must equal the item price.');
                        return;
                    }

                    splitAmounts = Array.from(customInputs).map(input => parseFloat(input.value) || 0);
                }

                // Remove the split item from original order
                const itemIndex = originalOrder.items.findIndex(i => 
                    i.name === itemName && 
                    Math.abs(i.price - itemPrice) < 0.01
                );
                if (itemIndex !== -1) {
                    originalOrder.items.splice(itemIndex, 1);
                }

                // Update original order in backend
                await fetch(`http://localhost:5000/orders/${originalOrder.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ...originalOrder,
                        items: JSON.stringify(originalOrder.items)
                    })
                });

                // Add split items to selected bills
                for (let i = 0; i < selectedBills.length; i++) {
                    const [tableNum, custName] = selectedBills[i].split(' - ');
                    let targetOrder = orders.find(o => 
                        o.tableNumber === tableNum.replace('Table ', '') && 
                        o.customerName === custName
                    );

                    if (!targetOrder) {
                        // Create new order if it doesn't exist
                        targetOrder = {
                            tableNumber: tableNum.replace('Table ', ''),
                            customerName: custName,
                            items: []
                        };
                    }

                    // Add split item to target order
                    targetOrder.items.push({
                        name: itemName,
                        price: splitAmounts[i],
                        quantity: 1
                    });

                    // Update or create order in backend
                    if (targetOrder.id) {
                        await fetch(`http://localhost:5000/orders/${targetOrder.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                ...targetOrder,
                                items: JSON.stringify(targetOrder.items)
                            })
                        });
                    } else {
                        await fetch('http://localhost:5000/orders', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                ...targetOrder,
                                items: JSON.stringify(targetOrder.items)
                            })
                        });
                    }
                }
            }

            // Refresh the display
            displayOrders();
            
            // Close the modal
            splitModal.style.display = 'none';
            selectedItem = null;
        };

    } catch (error) {
        console.error('Error handling split:', error);
        alert('Failed to process split. Please try again.');
    }
}

// Select Item for Split
function selectItemForSplit(itemButton) {
    selectedItem = itemButton;
    document.getElementById('selectedItemName').textContent = itemButton.getAttribute('data-item');
    const splitModal = document.getElementById('splitModal');
    splitModal.style.display = 'flex';
    populateBills();
}

// Setup Split Modal
function setupSplitModal() {
    const splitModal = document.getElementById('splitModal');
    const closeModal = document.querySelector('.close');
    const splitOptions = document.querySelectorAll('input[name="splitType"]');
    const confirmSplitButton = document.getElementById('confirmSplit');
    const undoSplitButton = document.getElementById('undoSplit');

    // Close modal when clicking the "X" button
    closeModal.addEventListener('click', () => {
        splitModal.style.display = 'none';
    });

    // Close modal when clicking outside the modal
    window.addEventListener('click', (event) => {
        if (event.target === splitModal) {
            splitModal.style.display = 'none';
        }
    });

    // Handle split type change
    splitOptions.forEach(option => {
        option.addEventListener('change', () => {
            // Remove selected class from all labels
            document.querySelectorAll('#splitOptions label').forEach(label => {
                label.classList.remove('selected');
            });
            
            // Add selected class to the clicked label
            const selectedLabel = option.parentElement;
            selectedLabel.classList.add('selected');
            
            const percentageInputs = document.getElementById('percentageInputs');
            const customInputs = document.getElementById('customInputs');
            
            if (option.value === 'percentage') {
                percentageInputs.style.display = 'block';
                customInputs.style.display = 'none';
                populatePercentageFields();
            } else if (option.value === 'custom') {
                percentageInputs.style.display = 'none';
                customInputs.style.display = 'block';
                populateCustomFields();
            } else {
                percentageInputs.style.display = 'none';
                customInputs.style.display = 'none';
            }
        });
    });

    // Handle undo split button click
    undoSplitButton.addEventListener('click', () => {
        if (!lastAction) return;

        const { item, originalBill, selectedBills, splitAmounts } = lastAction;

        // Revert the totals for selected bills
        selectedBills.forEach((billId, index) => {
            const bills = document.querySelectorAll('.customer-bill');
            const bill = Array.from(bills).find(b => b.querySelector('h4').textContent === billId);
            
            if (bill) {
                // Remove the split item from the bill
                const itemsList = bill.querySelector('ul');
                const lastItem = itemsList.lastElementChild;
                if (lastItem) {
                    lastItem.remove();
                }

                // Update the total
                const totalElement = bill.querySelector('p');
                let currentTotal = parseFloat(totalElement.textContent.replace('Total: $', ''));
                currentTotal -= splitAmounts[index];
                totalElement.textContent = `Total: $${currentTotal.toFixed(2)}`;
            }
        });

        // Add the item back to the original bill
        const originalTotalElement = originalBill.querySelector('p');
        let originalTotal = parseFloat(originalTotalElement.textContent.replace('Total: $', ''));
        originalTotal += parseFloat(item.getAttribute('data-price'));
        originalTotalElement.textContent = `Total: $${originalTotal.toFixed(2)}`;
        originalBill.querySelector('ul').appendChild(item);

        // Reset last action and hide undo button
        lastAction = null;
        undoSplitButton.style.display = 'none';
    });

    // Set initial selected state for equal split
    const equalSplitLabel = document.querySelector('label[for="equal"]');
    if (equalSplitLabel) {
        equalSplitLabel.classList.add('selected');
    }

    // Handle Confirm Split Button Click
    confirmSplitButton.addEventListener('click', () => {
        const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected')).map(label => label.dataset.value);
        if (selectedBills.length === 0) {
            alert('Please select at least one bill to split with.');
            return;
        }

        const splitType = document.querySelector('input[name="splitType"]:checked').value;
        let splitAmounts = [];

        if (selectedItem) {
            // Item-level split
            const itemPrice = parseFloat(selectedItem.getAttribute('data-price'));
            if (splitType === 'equal') {
                const splitAmount = itemPrice / selectedBills.length;
                splitAmounts = selectedBills.map(() => splitAmount);
            } else if (splitType === 'percentage') {
                const percentageInputs = document.querySelectorAll('#percentageFields input');
                let totalPercentage = 0;
                percentageInputs.forEach(input => {
                    totalPercentage += parseFloat(input.value) || 0;
                });

                if (totalPercentage !== 100) {
                    alert('The total percentage must equal 100%.');
                    return;
                }

                splitAmounts = Array.from(percentageInputs).map(input => {
                    const percentage = parseFloat(input.value) || 0;
                    return (itemPrice * percentage) / 100;
                });
            } else if (splitType === 'custom') {
                const customInputs = document.querySelectorAll('#customFields input');
                let totalAmount = 0;
                customInputs.forEach(input => {
                    totalAmount += parseFloat(input.value) || 0;
                });

                if (Math.abs(totalAmount - itemPrice) > 0.01) {
                    alert('The total custom amounts must equal the item price.');
                    return;
                }

                splitAmounts = Array.from(customInputs).map(input => parseFloat(input.value) || 0);
            }

            // Track the last action for undo
            lastAction = {
                item: selectedItem,
                originalBill: selectedItem.closest('.customer-bill'),
                selectedBills,
                splitAmounts,
                splitType,
                isBillSplit: false
            };

            // Add split item to each selected bill
            selectedBills.forEach((billId, index) => {
                const bills = document.querySelectorAll('.customer-bill');
                const bill = Array.from(bills).find(b => b.querySelector('h4').textContent === billId);
                
                if (bill) {
                    const itemName = selectedItem.getAttribute('data-item');
                    let splitDescription;
                    if (splitType === 'equal') {
                        splitDescription = `1/${selectedBills.length} ${itemName} - $${splitAmounts[index].toFixed(2)}`;
                    } else if (splitType === 'percentage') {
                        const percentage = (splitAmounts[index] / itemPrice * 100).toFixed(0);
                        splitDescription = `${percentage}% ${itemName} - $${splitAmounts[index].toFixed(2)}`;
                    } else {
                        splitDescription = `$${splitAmounts[index].toFixed(2)} ${itemName}`;
                    }

                    const itemsList = bill.querySelector('ul');
                    const newItem = document.createElement('li');
                    newItem.innerHTML = `<button class="item-button">${splitDescription}</button>`;
                    itemsList.appendChild(newItem);

                    // Update the total
                    const totalElement = bill.querySelector('p');
                    let currentTotal = parseFloat(totalElement.textContent.replace('Total: $', ''));
                    currentTotal += splitAmounts[index];
                    totalElement.textContent = `Total: $${currentTotal.toFixed(2)}`;
                }
            });

            // Remove the original item from its bill
            const originalBill = selectedItem.closest('.customer-bill');
            const originalTotalElement = originalBill.querySelector('p');
            let originalTotal = parseFloat(originalTotalElement.textContent.replace('Total: $', ''));
            originalTotal -= parseFloat(selectedItem.getAttribute('data-price'));
            originalTotalElement.textContent = `Total: $${originalTotal.toFixed(2)}`;
            selectedItem.remove();
        } else {
            // Bill-level split
            const bills = document.querySelectorAll('.customer-bill');
            const originalBill = Array.from(bills).find(b => b.querySelector('h4').textContent === selectedBills[0]);
            if (!originalBill) {
                alert('Could not find the original bill.');
                return;
            }
            const totalAmount = parseFloat(originalBill.querySelector('p').textContent.replace('Total: $', ''));
            
            if (splitType === 'equal') {
                const splitAmount = totalAmount / selectedBills.length;
                splitAmounts = selectedBills.map(() => splitAmount);
            } else if (splitType === 'percentage') {
                const percentageInputs = document.querySelectorAll('#percentageFields input');
                let totalPercentage = 0;
                percentageInputs.forEach(input => {
                    totalPercentage += parseFloat(input.value) || 0;
                });

                if (totalPercentage !== 100) {
                    alert('The total percentage must equal 100%.');
                    return;
                }

                splitAmounts = Array.from(percentageInputs).map(input => {
                    const percentage = parseFloat(input.value) || 0;
                    return (totalAmount * percentage) / 100;
                });
            } else if (splitType === 'custom') {
                const customInputs = document.querySelectorAll('#customFields input');
                let totalCustomAmount = 0;
                customInputs.forEach(input => {
                    totalCustomAmount += parseFloat(input.value) || 0;
                });

                if (Math.abs(totalCustomAmount - totalAmount) > 0.01) {
                    alert('The total custom amounts must equal the bill total.');
                    return;
                }

                splitAmounts = Array.from(customInputs).map(input => parseFloat(input.value) || 0);
            }

            // Track the last action for undo
            lastAction = {
                originalBill,
                selectedBills,
                splitAmounts,
                splitType,
                isBillSplit: true
            };

            // Add split amount to each selected bill
            selectedBills.forEach((billId, index) => {
                const bill = Array.from(bills).find(b => b.querySelector('h4').textContent === billId);
                
                if (bill) {
                    const totalElement = bill.querySelector('p');
                    let currentTotal = parseFloat(totalElement.textContent.replace('Total: $', ''));
                    currentTotal += splitAmounts[index];
                    totalElement.textContent = `Total: $${currentTotal.toFixed(2)}`;
                }
            });

            // Clear the original bill
            originalBill.querySelector('ul').innerHTML = '';
            originalBill.querySelector('p').textContent = 'Total: $0.00';
        }

        // Reset selectedItem and close the modal
        selectedItem = null;
        splitModal.style.display = 'none';
        document.getElementById('undoSplit').style.display = 'block';
    });
}

// Populate bills in the modal
function populateBills() {
    const billSelection = document.getElementById('billSelection');
    billSelection.innerHTML = '';
    
    // Get all customer bills
    const customerBills = document.querySelectorAll('.customer-bill');
    customerBills.forEach(bill => {
        const label = document.createElement('label');
        label.textContent = bill.querySelector('h4').textContent;
        label.dataset.value = bill.querySelector('h4').textContent;
        label.addEventListener('click', () => {
            label.classList.toggle('selected');
            const splitType = document.querySelector('input[name="splitType"]:checked')?.value;
            if (splitType === 'percentage') {
                populatePercentageFields();
            } else if (splitType === 'custom') {
                populateCustomFields();
            }
        });
        billSelection.appendChild(label);
    });
}

// Populate percentage fields
function populatePercentageFields() {
    const percentageFields = document.getElementById('percentageFields');
    percentageFields.innerHTML = '';
    const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected'));
    selectedBills.forEach(label => {
        const billId = label.dataset.value;
        const inputLabel = document.createElement('label');
        inputLabel.textContent = `${billId}: `;
        const input = document.createElement('input');
        input.type = 'number';
        input.min = '0';
        input.max = '100';
        input.placeholder = 'Percentage';
        percentageFields.appendChild(inputLabel);
        percentageFields.appendChild(input);
        percentageFields.appendChild(document.createElement('br'));
    });
}

// Populate custom fields
function populateCustomFields() {
    const customFields = document.getElementById('customFields');
    customFields.innerHTML = '';
    const selectedBills = Array.from(document.querySelectorAll('#billSelection label.selected'));
    selectedBills.forEach(label => {
        const billId = label.dataset.value;
        const inputLabel = document.createElement('label');
        inputLabel.textContent = `${billId}: `;
        const input = document.createElement('input');
        input.type = 'number';
        input.min = '0';
        input.placeholder = 'Amount';
        customFields.appendChild(inputLabel);
        customFields.appendChild(input);
        customFields.appendChild(document.createElement('br'));
    });
}

// Clear Table
async function clearTable() {
    console.log('Clear button clicked');
    console.log('Selected table:', selectedTable);

    if (!selectedTable) {
        alert('Please select a table first.');
        return;
    }

    const confirmation = confirm(`Are you sure you want to clear Table ${selectedTable}? This will remove all orders.`);
    if (!confirmation) {
        return;
    }

    try {
        console.log('Sending delete request to:', `http://localhost:5000/orders/table/${selectedTable}`);
        const response = await fetch(`http://localhost:5000/orders/table/${selectedTable}`, {
            method: 'DELETE',
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (response.ok) {
            alert(`Table ${selectedTable} has been cleared.`);
            displayOrders(); // Refresh the display
        } else {
            const errorData = await response.json();
            console.log('Error data:', errorData);
            alert('Failed to clear the table. Please try again.');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while clearing the table.');
    }
}

function showMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);
    setTimeout(() => messageDiv.remove(), 3000);
}

// Create bill element
function createBillElement(tableNumber, customerName, items) {
    const billElement = document.createElement('div');
    billElement.className = 'bill';
    billElement.innerHTML = `
        <div class="bill-header">
            <h3>Table ${tableNumber} - ${customerName}</h3>
            <button class="pay-btn" onclick="handlePay('${tableNumber}', '${customerName}')">Pay</button>
        </div>
        <div class="bill-items"></div>
        <div class="bill-total">
            <span>Total:</span>
            <span class="total-amount">$0.00</span>
        </div>
    `;
    
    const itemsContainer = billElement.querySelector('.bill-items');
    const totalAmountElement = billElement.querySelector('.total-amount');
    
    // Add items to bill
    items.forEach(item => {
        const itemElement = document.createElement('div');
        itemElement.className = 'bill-item';
        itemElement.innerHTML = `
            <span class="item-name">${item.name}</span>
            <span class="item-price">$${item.price.toFixed(2)}</span>
        `;
        itemsContainer.appendChild(itemElement);
    });
    
    // Calculate and update total
    const total = items.reduce((sum, item) => sum + item.price, 0);
    totalAmountElement.textContent = `$${total.toFixed(2)}`;
    
    return billElement;
}

// Handle backup
async function handleBackup() {
    try {
        const response = await fetch('http://localhost:5000/api/backup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        
        if (response.ok) {
            showMessage('Backup created successfully!');
            console.log('Backup created at:', result.backupPath);
        } else {
            throw new Error(result.error || 'Failed to create backup');
        }
    } catch (error) {
        console.error('Backup error:', error);
        showMessage('Failed to create backup: ' + error.message);
    }
}