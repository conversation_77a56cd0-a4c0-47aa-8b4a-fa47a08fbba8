document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const billsList = document.getElementById('bills-list');
    const totalCount = document.getElementById('total-count');
    const totalAmount = document.getElementById('total-amount');
    const refreshBtn = document.getElementById('refresh-btn');
    const backBtn = document.getElementById('back-btn');
    const billModal = document.getElementById('bill-modal');
    const billDetails = document.getElementById('bill-details');
    const paymentModal = document.getElementById('payment-modal');
    const payCashBtn = document.getElementById('pay-cash');
    const payCardBtn = document.getElementById('pay-card');

    // Close buttons for modals
    const closeButtons = document.querySelectorAll('.close');

    // Global variables
    let pendingBills = [];
    let selectedBillId = null;

    // Initialize
    loadPendingBills();

    // Event Listeners
    refreshBtn.addEventListener('click', loadPendingBills);

    backBtn.addEventListener('click', function() {
        window.location.href = '../settings/index.html';
    });

    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            billModal.style.display = 'none';
            paymentModal.style.display = 'none';
        });
    });

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === billModal) {
            billModal.style.display = 'none';
        }
        if (event.target === paymentModal) {
            paymentModal.style.display = 'none';
        }
    });

    payCashBtn.addEventListener('click', function() {
        processPayment('cash');
    });

    payCardBtn.addEventListener('click', function() {
        processPayment('card');
    });

    /**
     * Load pending bills from the server
     */
    function loadPendingBills() {
        showLoading();

        fetch('/api/pending-bills')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    pendingBills = data.bills;
                    displayPendingBills(pendingBills);
                    updateTotals(pendingBills);
                } else {
                    showNoBills('Error loading bills: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error loading pending bills:', error);
                showNoBills('Error loading bills. Please try again.');
            });
    }

    /**
     * Display pending bills in the UI
     * @param {Array} bills - Array of pending bills
     */
    function displayPendingBills(bills) {
        if (!bills || bills.length === 0) {
            showNoBills('No pending bills found');
            return;
        }

        let html = '';

        bills.forEach(bill => {
            const date = new Date(bill.createdAt).toLocaleString();
            const total = calculateTotal(bill.items);

            html += `
                <div class="bill-card">
                    <div class="bill-header">
                        <div class="bill-id">Bill #${bill.id.substring(0, 8)}</div>
                        <div class="bill-date">${date}</div>
                    </div>
                    <div class="bill-customer">Customer: ${bill.customerName}</div>
                    <div class="bill-table">Table: ${bill.tableNumber}</div>
                    <div class="bill-items">${bill.items.length} item(s)</div>
                    <div class="bill-total">Total: $${total.toFixed(2)}</div>
                    ${bill.notes ? `<div class="bill-notes">${bill.notes}</div>` : ''}
                    <div class="bill-actions">
                        <button class="bill-btn view-btn" onclick="viewBill('${bill.id}')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="bill-btn pay-btn" onclick="showPaymentModal('${bill.id}')">
                            <i class="fas fa-money-bill-wave"></i> Pay
                        </button>
                        <button class="bill-btn payment-page-btn" onclick="goToPaymentPage('${bill.tableNumber}', '${bill.customerName}')">
                            <i class="fas fa-credit-card"></i> Payment Page
                        </button>
                        <button class="bill-btn delete-btn" onclick="deleteBill('${bill.id}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        billsList.innerHTML = html;
    }

    /**
     * Update totals in the UI
     * @param {Array} bills - Array of pending bills
     */
    function updateTotals(bills) {
        const count = bills.length;
        let amount = 0;

        bills.forEach(bill => {
            amount += calculateTotal(bill.items);
        });

        totalCount.textContent = count;
        totalAmount.textContent = `$${amount.toFixed(2)}`;
    }

    /**
     * Calculate total from items
     * @param {Array} items - Order items
     * @returns {number} Total amount
     */
    function calculateTotal(items) {
        if (!items || !Array.isArray(items)) return 0;
        return items.reduce((sum, item) => {
            const price = parseFloat(item.price) || 0;
            const quantity = parseInt(item.quantity) || 1;
            return sum + (price * quantity);
        }, 0);
    }

    /**
     * Show loading state
     */
    function showLoading() {
        billsList.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading pending bills...</p>
            </div>
        `;
    }

    /**
     * Show no bills message
     * @param {string} message - Message to display
     */
    function showNoBills(message) {
        billsList.innerHTML = `
            <div class="no-bills">
                <i class="fas fa-exclamation-circle"></i>
                <p>${message}</p>
            </div>
        `;

        totalCount.textContent = '0';
        totalAmount.textContent = '$0.00';
    }

    /**
     * View bill details
     * @param {string} billId - Bill ID
     */
    window.viewBill = function(billId) {
        fetch(`/api/pending-bills/${billId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayBillDetails(data.bill);
                    billModal.style.display = 'block';
                } else {
                    alert('Error loading bill details: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error loading bill details:', error);
                alert('Error loading bill details. Please try again.');
            });
    };

    /**
     * Display bill details in modal
     * @param {Object} bill - Bill data
     */
    function displayBillDetails(bill) {
        const date = new Date(bill.createdAt).toLocaleString();
        const total = calculateTotal(bill.items);

        let html = `
            <div class="bill-details-header">
                <div class="bill-details-id">Bill #${bill.id}</div>
                <div class="bill-details-date">${date}</div>
            </div>

            <div class="bill-details-section">
                <h3>Customer Information</h3>
                <div class="bill-details-info">
                    <p><strong>Name:</strong> ${bill.customerName}</p>
                    <p><strong>Table:</strong> ${bill.tableNumber}</p>
                </div>
            </div>

            <div class="bill-details-section">
                <h3>Items</h3>
                <table class="bill-details-items">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        bill.items.forEach(item => {
            const price = parseFloat(item.price) || 0;
            const quantity = parseInt(item.quantity) || 1;
            const itemTotal = price * quantity;

            html += `
                <tr>
                    <td>${item.name}</td>
                    <td>${quantity}</td>
                    <td>$${price.toFixed(2)}</td>
                    <td>$${itemTotal.toFixed(2)}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>

                <div class="bill-details-total">
                    Total: $${total.toFixed(2)}
                </div>
            </div>
        `;

        if (bill.notes) {
            html += `
                <div class="bill-details-notes">
                    <strong>Notes:</strong> ${bill.notes}
                </div>
            `;
        }

        html += `
            <div class="bill-details-actions">
                <button class="bill-details-btn pay-btn" onclick="showPaymentModal('${bill.id}')">
                    <i class="fas fa-money-bill-wave"></i> Process Payment
                </button>
                <button class="bill-details-btn payment-page-btn" onclick="goToPaymentPage('${bill.tableNumber}', '${bill.customerName}')">
                    <i class="fas fa-credit-card"></i> Go to Payment Page
                </button>
                <button class="bill-details-btn delete-btn" onclick="deleteBill('${bill.id}')">
                    <i class="fas fa-trash"></i> Delete Bill
                </button>
            </div>
        `;

        billDetails.innerHTML = html;
    }

    /**
     * Show payment modal
     * @param {string} billId - Bill ID
     */
    window.showPaymentModal = function(billId) {
        selectedBillId = billId;
        billModal.style.display = 'none';
        paymentModal.style.display = 'block';
    };

    /**
     * Process payment for a bill
     * @param {string} method - Payment method (cash or card)
     */
    function processPayment(method) {
        if (!selectedBillId) {
            alert('No bill selected for payment');
            return;
        }

        const paymentBtn = method === 'cash' ? payCashBtn : payCardBtn;
        const originalText = paymentBtn.innerHTML;

        // Show loading state
        paymentBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> Processing...`;
        paymentBtn.disabled = true;

        fetch(`/api/pending-bills/${selectedBillId}/pay`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                paymentMethod: method
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    paymentModal.style.display = 'none';
                    alert('Payment processed successfully');
                    loadPendingBills(); // Refresh the list
                } else {
                    alert('Error processing payment: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error processing payment:', error);
                alert('Error processing payment. Please try again.');
            })
            .finally(() => {
                // Restore button state
                paymentBtn.innerHTML = originalText;
                paymentBtn.disabled = false;
                selectedBillId = null;
            });
    }

    /**
     * Go to payment page for a specific table and customer
     * @param {string} tableNumber - Table number
     * @param {string} customerName - Customer name
     */
    window.goToPaymentPage = function(tableNumber, customerName) {
        // Create a form to post the data to the payment page
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '../payment/index.html';
        form.style.display = 'none';

        // Add table number input
        const tableInput = document.createElement('input');
        tableInput.type = 'hidden';
        tableInput.name = 'tableNumber';
        tableInput.value = tableNumber;
        form.appendChild(tableInput);

        // Add customer name input
        const customerInput = document.createElement('input');
        customerInput.type = 'hidden';
        customerInput.name = 'customerName';
        customerInput.value = customerName;
        form.appendChild(customerInput);

        // Add the form to the document and submit it
        document.body.appendChild(form);
        form.submit();
    };

    /**
     * Delete a bill
     * @param {string} billId - Bill ID
     */
    window.deleteBill = function(billId) {
        if (confirm('Are you sure you want to delete this bill?')) {
            fetch(`/api/pending-bills/${billId}`, {
                method: 'DELETE'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        billModal.style.display = 'none';
                        alert('Bill deleted successfully');
                        loadPendingBills(); // Refresh the list
                    } else {
                        alert('Error deleting bill: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting bill:', error);
                    alert('Error deleting bill. Please try again.');
                });
        }
    };
});
