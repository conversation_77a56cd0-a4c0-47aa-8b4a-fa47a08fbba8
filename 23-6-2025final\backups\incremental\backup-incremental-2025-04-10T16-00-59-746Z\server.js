const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const QRCode = require('qrcode');
const BackupService = require('./backup-service');
const EnhancedBackupService = require('./enhanced-backup-service');

// Create or open the SQLite database
const db = new sqlite3.Database('./orders.db', (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
    } else {
        console.log('Connected to the SQLite database.');
        // Create the orders table if it doesn't exist
        db.run(`
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tableNumber TEXT NOT NULL,
                customerName TEXT NOT NULL,
                items TEXT NOT NULL,
                coverCharge TEXT,
                paid BOOLEAN DEFAULT 0,
                status TEXT DEFAULT 'pending',
                kitchenStatus TEXT,
                report TEXT,
                paymentMethod TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Check if columns exist
        db.all("PRAGMA table_info(orders)", (err, rows) => {
            if (err) {
                console.error('Error checking table structure:', err);
                return;
            }

            const columnsToAdd = [
                { name: 'coverCharge', type: 'TEXT' },
                { name: 'timestamp', type: 'DATETIME DEFAULT CURRENT_TIMESTAMP' },
                { name: 'kitchenStatus', type: 'TEXT' },
                { name: 'paymentMethod', type: 'TEXT' },
                { name: 'transactionId', type: 'TEXT' }
            ];

            columnsToAdd.forEach(column => {
                const hasColumn = rows.some(row => row.name === column.name);
                if (!hasColumn) {
                    console.log(`Adding ${column.name} column...`);
                    db.run(`ALTER TABLE orders ADD COLUMN ${column.name} ${column.type}`);
                }
            });
        });
    }
});

const app = express();

// Configure CORS
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type']
}));

app.use(bodyParser.json());

// Log requests
app.use((req, res, next) => {
    console.log(`${req.method} ${req.url}`);
    next();
});

// Serve static files
app.use(express.static(path.join(__dirname)));
app.use('/tablet', express.static(path.join(__dirname, 'tablet')));
app.use('/pos', express.static(path.join(__dirname, 'pos')));
app.use('/kitchen-displays', express.static(path.join(__dirname, 'kitchen-displays')));
app.use('/payment', express.static(path.join(__dirname, 'payment')));
app.use('/backup-viewer', express.static(path.join(__dirname, 'backup-viewer')));
app.use('/backup-manager', express.static(path.join(__dirname, 'backup-manager')));
app.use('/reports', express.static(path.join(__dirname, 'reports')));
app.use('/backup', express.static(path.join(__dirname, 'backup')));

// Redirect /payment to the new payment page
app.get('/payment', (req, res) => {
    res.redirect('/payment/payment-new.html' + (req.query.orderData ? '?orderData=' + req.query.orderData : ''));
});

// Initialize backup services
const backupService = new BackupService(); // Keep for backward compatibility
console.log('Legacy backup service initialized');

// Initialize enhanced backup service with custom options
const enhancedBackupService = new EnhancedBackupService({
    retentionDays: 7,           // Keep 7 days of backups
    compressionEnabled: true,   // Enable compression to save space
    dailyBackupHour: 3,         // Run daily backup at 3 AM
    incrementalBackupInterval: 4 // Run incremental backup every 4 hours
});
console.log('Enhanced backup service initialized');

// Start scheduled backups
enhancedBackupService.scheduleBackups();
console.log('Enhanced scheduled backups started');

// API Endpoints

// Create a new order
app.post('/orders', (req, res) => {
    const { tableNumber, customerName, items, coverCharge } = req.body;
    const timestamp = new Date().toISOString();

    const sql = `
        INSERT INTO orders (tableNumber, customerName, items, coverCharge, timestamp)
        VALUES (?, ?, ?, ?, ?)
    `;

    db.run(sql, [tableNumber, customerName, JSON.stringify(items), coverCharge, timestamp], function(err) {
        if (err) {
            console.error('Error creating order:', err);
            return res.status(500).json({ error: err.message });
        }

        res.status(201).json({
            id: this.lastID,
            tableNumber,
            customerName,
            items,
            coverCharge,
            timestamp,
            paid: false
        });
    });
});

// Get all unpaid orders
app.get('/orders', (req, res) => {
    // Get the showPaid parameter from the query string (default to false)
    const showPaid = req.query.showPaid === 'true';

    // If showPaid is true, return all orders, otherwise only return unpaid orders
    const sql = showPaid
        ? `SELECT * FROM orders ORDER BY timestamp DESC`
        : `SELECT * FROM orders WHERE paid = 0 ORDER BY timestamp DESC`;
    db.all(sql, [], (err, rows) => {
        if (err) return res.status(500).json({ error: err.message });

        const orders = rows.map(order => {
            try {
                let timestamp;
                if (order.timestamp) {
                    const parsedDate = new Date(order.timestamp);
                    if (!isNaN(parsedDate.getTime())) {
                        timestamp = parsedDate.toISOString();
                    } else {
                        timestamp = new Date().toISOString();
                        db.run('UPDATE orders SET timestamp = ? WHERE id = ?', [timestamp, order.id]);
                    }
                } else {
                    timestamp = new Date().toISOString();
                    db.run('UPDATE orders SET timestamp = ? WHERE id = ?', [timestamp, order.id]);
                }

                let items = [];
                try {
                    items = JSON.parse(order.items);
                    items = items.map(item => ({
                        name: item.name || '',
                        price: parseFloat(item.price) || 0,
                        notes: item.notes || ''
                    }));
                } catch (e) {
                    console.error('Error parsing items:', e);
                }

                let kitchenStatus = null;
                try {
                    // Check if the kitchen status is a valid JSON string
                    if (order.kitchenStatus && order.kitchenStatus.startsWith('{')) {
                        kitchenStatus = JSON.parse(order.kitchenStatus);
                    } else {
                        // If it's not JSON, use it as is
                        kitchenStatus = order.kitchenStatus;
                    }
                } catch (e) {
                    console.error('Error parsing kitchen status:', e);
                    // If parsing fails, use the raw value
                    kitchenStatus = order.kitchenStatus;
                }

                return {
                    id: order.id,
                    tableNumber: order.tableNumber,
                    customerName: order.customerName,
                    items: items,
                    status: order.status,
                    kitchenStatus: kitchenStatus,
                    timestamp: timestamp,
                    paid: Boolean(order.paid)
                };
            } catch (e) {
                console.error('Error parsing order:', e);
                return null;
            }
        }).filter(order => order !== null);

        res.status(200).json(orders);
    });
});

// Get specific order details
app.get('/api/order/:tableNumber/:customerName', (req, res) => {
    const { tableNumber, customerName } = req.params;

    db.get(
        `SELECT * FROM orders WHERE tableNumber = ? AND customerName = ? AND paid = 0`,
        [tableNumber, customerName],
        (err, order) => {
            if (err) {
                return res.status(500).json({ error: err.message });
            }
            if (!order) {
                return res.status(404).json({ error: 'Order not found' });
            }

            try {
                order.items = JSON.parse(order.items);
                res.json(order);
            } catch (error) {
                res.status(500).json({ error: 'Error parsing order items' });
            }
        }
    );
});

// Update kitchen status
app.put('/orders/:id/kitchen', (req, res) => {
    const { id } = req.params;
    const { status, type } = req.body;

    if (!status || !type) {
        return res.status(400).json({ error: 'Missing status or type' });
    }

    db.get('SELECT kitchenStatus FROM orders WHERE id = ?', [id], (err, row) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }

        if (!row) {
            return res.status(404).json({ error: 'Order not found' });
        }

        let currentStatus = {};
        try {
            // Check if the kitchen status is a valid JSON string
            if (row.kitchenStatus && row.kitchenStatus.startsWith('{')) {
                currentStatus = JSON.parse(row.kitchenStatus);
            } else if (row.kitchenStatus) {
                // If it's not JSON but has a value, create an object with the existing type
                currentStatus = { [type]: row.kitchenStatus };
            }
        } catch (e) {
            console.error('Error parsing kitchen status:', e);
            // If parsing fails, create a new object with the existing type
            if (row.kitchenStatus) {
                currentStatus = { [type]: row.kitchenStatus };
            }
        }

        currentStatus[type] = status;
        const newStatus = JSON.stringify(currentStatus);

        db.run(
            `UPDATE orders SET kitchenStatus = ? WHERE id = ?`,
            [newStatus, id],
            function(err) {
                if (err) {
                    return res.status(500).json({ error: err.message });
                }
                res.status(200).json({
                    message: 'Kitchen status updated',
                    kitchenStatus: currentStatus
                });
            }
        );
    });
});

// Update order by ID
app.put('/orders/:id', (req, res) => {
    const { id } = req.params;
    const { items, tableNumber, customerName } = req.body;

    // Validate required fields
    if (!items) {
        return res.status(400).json({ error: 'Items are required' });
    }

    // Make sure items is a string (JSON)
    const itemsJson = typeof items === 'string' ? items : JSON.stringify(items);

    db.run(
        `UPDATE orders SET items = ? WHERE id = ?`,
        [itemsJson, id],
        function(err) {
            if (err) {
                console.error('Error updating order:', err);
                return res.status(500).json({ error: err.message });
            }

            if (this.changes === 0) {
                return res.status(404).json({ error: 'Order not found' });
            }

            res.status(200).json({
                message: 'Order updated successfully',
                id: id
            });
        }
    );
});

// Mark order as paid
app.put('/orders/:tableNumber/:customerName', (req, res) => {
    const { tableNumber, customerName } = req.params;
    db.run(
        `UPDATE orders SET paid = 1 WHERE tableNumber = ? AND customerName = ?`,
        [tableNumber, customerName],
        function(err) {
            if (err) return res.status(500).json({ error: err.message });
            res.status(200).json({ message: 'Order marked as paid' });
        }
    );
});

// Clear table orders
app.delete('/orders/table/:tableNumber', (req, res) => {
    const { tableNumber } = req.params;
    db.run(
        `DELETE FROM orders WHERE tableNumber = ?`,
        [tableNumber],
        function(err) {
            if (err) return res.status(500).json({ error: err.message });
            res.status(200).json({ message: 'Table orders cleared' });
        }
    );
});

// Process payment
app.post('/process-payment', (req, res) => {
    const { orderData, paymentMethod } = req.body;
    console.log('Processing payment:', orderData.tableNumber, orderData.customerName, paymentMethod);

    // Update the order in the database
    db.run(
        `UPDATE orders SET paid = 1, paymentMethod = ? WHERE tableNumber = ? AND customerName = ?`,
        [paymentMethod, orderData.tableNumber, orderData.customerName],
        function(err) {
            if (err) {
                console.error('Error updating order:', err);
                return res.status(500).json({ error: err.message });
            }

            console.log(`Payment processed for table ${orderData.tableNumber}, customer ${orderData.customerName}`);
            res.status(200).json({
                success: true,
                message: 'Payment processed successfully',
                tableNumber: orderData.tableNumber,
                customerName: orderData.customerName,
                paymentMethod: paymentMethod
            });
        }
    );
});

// Backup endpoint
app.post('/api/backup', async (req, res) => {
    try {
        console.log('Creating backup...');
        const backupType = req.query.type || 'manual';

        // Use enhanced backup service
        const backupPath = await enhancedBackupService.createFullBackup(backupType);
        console.log('Backup created at:', backupPath);

        res.json({
            success: true,
            message: 'Backup created successfully',
            backupPath: backupPath,
            backupType: backupType
        });
    } catch (error) {
        console.error('Backup error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get backup information
app.get('/api/backups', (req, res) => {
    console.log('Fetching backup information...');
    try {
        // Use enhanced backup service to get backups
        const backups = enhancedBackupService.getBackups();
        console.log(`Found ${backups.length} backups`);
        res.json(backups);
    } catch (error) {
        console.error('Error fetching backups:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get backup storage information
app.get('/api/backup-storage', (req, res) => {
    console.log('Fetching backup storage information...');
    try {
        // Calculate next scheduled backup time
        let nextScheduledBackup = null;
        if (enhancedBackupService.options.scheduleDailyBackup) {
            const now = new Date();
            const nextBackup = new Date();
            nextBackup.setHours(enhancedBackupService.options.dailyBackupHour, 0, 0, 0);

            // If the scheduled time has already passed today, schedule for tomorrow
            if (now > nextBackup) {
                nextBackup.setDate(nextBackup.getDate() + 1);
            }

            nextScheduledBackup = nextBackup.toISOString();
        }

        // Get storage information from enhanced backup service
        const storageInfo = {
            primary: 'Local',
            external: enhancedBackupService.options.externalBackupDir ? 'External Drive' : null,
            cloud: null, // For future MyCloud integration
            nextScheduledBackup: nextScheduledBackup,
            backupSettings: {
                enabled: enhancedBackupService.options.scheduleDailyBackup,
                hour: enhancedBackupService.options.dailyBackupHour
            }
        };

        res.json({
            success: true,
            storage: storageInfo
        });
    } catch (error) {
        console.error('Error fetching backup storage info:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Update backup schedule
app.post('/api/backup-schedule', (req, res) => {
    try {
        const { enabled, hour, minute, ampm } = req.body;

        // Convert hour to 24-hour format if needed
        let hourValue = parseInt(hour);
        if (ampm === 'pm' && hourValue < 12) {
            hourValue += 12;
        } else if (ampm === 'am' && hourValue === 12) {
            hourValue = 0;
        }

        console.log(`Updating backup schedule: enabled=${enabled}, hour=${hourValue}`);

        // Update the backup schedule
        const nextBackup = enhancedBackupService.updateBackupSchedule({
            enabled: enabled,
            hour: hourValue
        });

        res.json({
            success: true,
            message: 'Backup schedule updated successfully',
            nextScheduledBackup: nextBackup ? nextBackup.toISOString() : null
        });
    } catch (error) {
        console.error('Error updating backup schedule:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get backup information (legacy endpoint)
app.get('/api/backups-legacy', (req, res) => {
    console.log('Fetching legacy backup information...');
    const backupDir = path.join(__dirname, 'backups');
    const backups = [];

    if (fs.existsSync(backupDir)) {
        console.log('Backups directory exists');
        const backupFolders = fs.readdirSync(backupDir)
            .filter(folder => folder.startsWith('backup-'))
            .sort((a, b) => b.localeCompare(a)); // Sort by date, newest first

        console.log('Found backup folders:', backupFolders);

        backupFolders.forEach(folder => {
            const manifestPath = path.join(backupDir, folder, 'manifest.json');
            if (fs.existsSync(manifestPath)) {
                try {
                    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
                    const backupPath = path.join(backupDir, folder);
                    let size = 0;

                    // Calculate total size of backup
                    const files = fs.readdirSync(backupPath, { withFileTypes: true });
                    for (const file of files) {
                        const filePath = path.join(backupPath, file.name);
                        if (file.isDirectory()) {
                            const subFiles = fs.readdirSync(filePath, { withFileTypes: true });
                            for (const subFile of subFiles) {
                                const subFilePath = path.join(filePath, subFile.name);
                                size += fs.statSync(subFilePath).size;
                            }
                        } else {
                            size += fs.statSync(filePath).size;
                        }
                    }

                    backups.push({
                        name: folder,
                        timestamp: manifest.timestamp,
                        fileCount: manifest.files.length,
                        hash: manifest.hash,
                        size: size
                    });
                } catch (error) {
                    console.error(`Error reading manifest for ${folder}:`, error);
                }
            }
        });
    } else {
        console.log('Backups directory does not exist');
    }

    console.log('Sending backup information:', backups);
    res.json(backups);
});

// Get detailed backup information
app.get('/api/backups/:backupName', (req, res) => {
    const { backupName } = req.params;
    const backupType = req.query.type || 'manual';

    console.log(`Fetching details for backup: ${backupName}, type: ${backupType}`);

    try {
        // Direct implementation without relying on the enhanced backup service
        const backupPath = path.join(__dirname, 'backups', backupType, backupName);
        console.log(`Looking for backup at: ${backupPath}`);

        if (!fs.existsSync(backupPath)) {
            console.log(`Backup directory not found: ${backupPath}`);
            return res.status(404).json({
                success: false,
                error: `Backup ${backupName} not found`
            });
        }

        // Get basic information about the backup
        const stats = fs.statSync(backupPath);
        const manifestPath = path.join(backupPath, 'manifest.json');

        let files = [];
        let timestamp = stats.mtime.toISOString();
        let hash = '';

        // Try to read the manifest if it exists
        if (fs.existsSync(manifestPath)) {
            try {
                const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
                files = manifest.files || [];
                timestamp = manifest.timestamp || timestamp;
                hash = manifest.hash || '';
            } catch (manifestError) {
                console.error(`Error reading manifest for ${backupName}:`, manifestError);
                // Continue with default values if manifest can't be read
            }
        } else {
            console.log(`No manifest found for ${backupName}, using directory listing`);
            // If no manifest, try to list files in the directory
            try {
                const getFilesRecursively = (dir) => {
                    let results = [];
                    const list = fs.readdirSync(dir);

                    list.forEach(file => {
                        const fullPath = path.join(dir, file);
                        const relativePath = path.relative(backupPath, fullPath);

                        if (fs.statSync(fullPath).isDirectory()) {
                            results = results.concat(getFilesRecursively(fullPath));
                        } else {
                            results.push(relativePath);
                        }
                    });

                    return results;
                };

                files = getFilesRecursively(backupPath);
            } catch (listError) {
                console.error(`Error listing files for ${backupName}:`, listError);
                // Continue with empty files array
            }
        }

        // Calculate size
        let size = 0;
        try {
            const calculateSize = (dir) => {
                let total = 0;
                const items = fs.readdirSync(dir);

                for (const item of items) {
                    const itemPath = path.join(dir, item);
                    const itemStats = fs.statSync(itemPath);

                    if (itemStats.isDirectory()) {
                        total += calculateSize(itemPath);
                    } else {
                        total += itemStats.size;
                    }
                }

                return total;
            };

            size = calculateSize(backupPath);
        } catch (sizeError) {
            console.error(`Error calculating size for ${backupName}:`, sizeError);
            // Continue with size = 0
        }

        // Return the backup details
        const backupDetails = {
            name: backupName,
            path: backupPath,
            timestamp: timestamp,
            type: backupType,
            files: files,
            size: size,
            hash: hash
        };

        console.log(`Successfully retrieved details for ${backupName}`);
        res.json(backupDetails);
    } catch (error) {
        console.error('Error fetching backup details:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Restore from backup
app.post('/api/restore/:backupName', async (req, res) => {
    const { backupName } = req.params;
    const backupType = req.query.type || 'manual';

    try {
        // Use enhanced backup service to restore
        const result = await enhancedBackupService.restoreFromBackup(backupName, backupType);
        res.json({
            success: true,
            message: `Restored from backup ${backupName}`,
            details: result
        });
    } catch (error) {
        console.error('Error restoring from backup:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Legacy backup detail endpoint
app.get('/api/backups-legacy/:backupName', (req, res) => {
    const { backupName } = req.params;
    const backupPath = path.join(__dirname, 'backups', backupName);
    const manifestPath = path.join(backupPath, 'manifest.json');

    if (!fs.existsSync(manifestPath)) {
        return res.status(404).json({ error: 'Backup not found' });
    }

    try {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        const size = calculateBackupSize(backupPath);

        res.json({
            name: backupName,
            timestamp: manifest.timestamp,
            fileCount: manifest.files.length,
            size: size,
            files: manifest.files,
            hash: manifest.hash
        });
    } catch (error) {
        console.error('Error reading backup details:', error);
        res.status(500).json({ error: 'Failed to read backup details' });
    }
});

// Helper function to calculate backup size
function calculateBackupSize(dir) {
    let size = 0;
    const files = fs.readdirSync(dir, { withFileTypes: true });

    for (const file of files) {
        const fullPath = path.join(dir, file.name);
        if (file.isDirectory()) {
            size += calculateBackupSize(fullPath);
        } else {
            size += fs.statSync(fullPath).size;
        }
    }

    return size;
}

// Add route for main page
app.get('/', (req, res) => {
    res.redirect('/pos');
});

// Add route for POS page
app.get('/pos', (req, res) => {
    res.sendFile(path.join(__dirname, 'pos', 'index.html'));
});

// Items API Endpoints

// Get all items
app.get('/api/items', (req, res) => {
    try {
        const searchTerm = req.query.search ? req.query.search.toLowerCase() : '';
        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Flatten the items from all categories
        const allItems = [];

        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            const categoryName = categoryKey;

            category.items.forEach(item => {
                // Add category information to each item
                const itemWithCategory = {
                    ...item,
                    category: categoryName,
                    categoryDisplayName: category.name
                };

                // If there's a search term, filter items
                if (!searchTerm ||
                    item.name.toLowerCase().includes(searchTerm) ||
                    category.name.toLowerCase().includes(searchTerm) ||
                    (item.description && item.description.toLowerCase().includes(searchTerm))) {
                    allItems.push(itemWithCategory);
                }
            });
        });

        res.json(allItems);
    } catch (error) {
        console.error('Error reading items:', error);
        res.status(500).json({ error: 'Failed to read items' });
    }
});

// Get a specific item
app.get('/api/items/:id', (req, res) => {
    try {
        const itemId = req.params.id;
        console.log('Server: Looking for item with ID:', itemId);

        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Search for the item in all categories
        let foundItem = null;
        let foundCategory = null;

        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            console.log(`Server: Searching in category ${categoryKey} with ${category.items.length} items`);

            // Log some sample IDs from this category
            if (category.items.length > 0) {
                console.log('Server: Sample item IDs in this category:',
                    category.items.slice(0, 3).map(item => item.id || 'no-id'));
            }

            const item = category.items.find(item => {
                const matches = (item.id === itemId) || (item.name === itemId);
                if (matches) {
                    console.log('Server: Found matching item:', item);
                }
                return matches;
            });

            if (item) {
                foundItem = { ...item, category: categoryKey, categoryDisplayName: category.name };
                foundCategory = categoryKey;
                console.log('Server: Item found in category:', categoryKey);
            }
        });

        if (foundItem) {
            console.log('Server: Returning found item:', foundItem);
            res.json(foundItem);
        } else {
            console.log('Server: Item not found with ID:', itemId);
            res.status(404).json({ error: 'Item not found' });
        }
    } catch (error) {
        console.error('Server: Error reading item:', error);
        res.status(500).json({ error: 'Failed to read item' });
    }
});

// Add a new item
app.post('/api/items', (req, res) => {
    try {
        const newItem = req.body;
        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Validate required fields
        if (!newItem.name || !newItem.category || newItem.price === undefined) {
            return res.status(400).json({ error: 'Name, category, and price are required' });
        }

        // Check if the category exists
        if (!itemsData.categories[newItem.category]) {
            return res.status(400).json({ error: 'Category does not exist' });
        }

        // Generate a unique ID
        const categoryPrefix = newItem.category.charAt(0);
        const existingIds = [];

        Object.keys(itemsData.categories).forEach(categoryKey => {
            itemsData.categories[categoryKey].items.forEach(item => {
                if (item.id) {
                    existingIds.push(item.id);
                }
            });
        });

        let newId;
        let counter = 1;
        do {
            newId = `${categoryPrefix}${counter}`;
            counter++;
        } while (existingIds.includes(newId));

        // Create the item object
        const itemToAdd = {
            id: newId,
            name: newItem.name,
            price: parseFloat(newItem.price),
            description: newItem.description || ''
        };

        // Add the item to the category
        itemsData.categories[newItem.category].items.push(itemToAdd);

        // Save the updated data
        fs.writeFileSync('items.json', JSON.stringify(itemsData, null, 2));

        res.status(201).json({
            ...itemToAdd,
            category: newItem.category,
            categoryDisplayName: itemsData.categories[newItem.category].name
        });
    } catch (error) {
        console.error('Error adding item:', error);
        res.status(500).json({ error: 'Failed to add item' });
    }
});

// Update an item
app.put('/api/items/:id', (req, res) => {
    try {
        const itemId = req.params.id;
        console.log('Server: Updating item with ID:', itemId);

        const updatedItem = req.body;
        console.log('Server: Update data received:', updatedItem);

        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Validate required fields
        if (!updatedItem.name || updatedItem.price === undefined) {
            console.log('Server: Validation failed - missing name or price');
            return res.status(400).json({ error: 'Name and price are required' });
        }

        // Find the item in all categories
        let found = false;
        let originalCategory = null;
        let originalItemIndex = -1;

        // First, find the item and its original category
        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            console.log(`Server: Searching for item to update in category ${categoryKey}`);

            // Try to find by ID first
            let itemIndex = category.items.findIndex(item => item.id === itemId);

            // If not found by ID, try by name
            if (itemIndex === -1) {
                itemIndex = category.items.findIndex(item => item.name === itemId);
                if (itemIndex !== -1) {
                    console.log(`Server: Found item by name in category ${categoryKey} at index ${itemIndex}`);
                }
            } else {
                console.log(`Server: Found item by ID in category ${categoryKey} at index ${itemIndex}`);
            }

            if (itemIndex !== -1) {
                originalCategory = categoryKey;
                originalItemIndex = itemIndex;
                found = true;
            }
        });

        if (!found) {
            console.log('Server: Item not found for update with ID:', itemId);
            return res.status(404).json({ error: 'Item not found' });
        }

        console.log(`Server: Item found in category ${originalCategory} at index ${originalItemIndex}`);

        // If category is changing, remove from old and add to new
        if (updatedItem.category && updatedItem.category !== originalCategory) {
            // Check if the new category exists
            if (!itemsData.categories[updatedItem.category]) {
                return res.status(400).json({ error: 'New category does not exist' });
            }

            // Find and remove the item from the original category
            const itemIndex = itemsData.categories[originalCategory].items.findIndex(
                item => item.id === itemId || (item.name === itemId)
            );

            if (itemIndex !== -1) {
                const removedItem = itemsData.categories[originalCategory].items.splice(itemIndex, 1)[0];

                // Update the item properties
                const updatedItemObj = {
                    id: removedItem.id,
                    name: updatedItem.name,
                    price: parseFloat(updatedItem.price),
                    description: updatedItem.description || removedItem.description || ''
                };

                // Add to the new category
                itemsData.categories[updatedItem.category].items.push(updatedItemObj);
            }
        } else {
            // Just update the item in its current category
            const itemIndex = itemsData.categories[originalCategory].items.findIndex(
                item => item.id === itemId || (item.name === itemId)
            );

            if (itemIndex !== -1) {
                itemsData.categories[originalCategory].items[itemIndex] = {
                    id: itemsData.categories[originalCategory].items[itemIndex].id,
                    name: updatedItem.name,
                    price: parseFloat(updatedItem.price),
                    description: updatedItem.description || itemsData.categories[originalCategory].items[itemIndex].description || ''
                };
            }
        }

        // Save the updated data
        fs.writeFileSync('items.json', JSON.stringify(itemsData, null, 2));

        // Determine the final category for the response
        const finalCategory = updatedItem.category || originalCategory;

        res.json({
            id: itemId,
            name: updatedItem.name,
            price: parseFloat(updatedItem.price),
            description: updatedItem.description || '',
            category: finalCategory,
            categoryDisplayName: itemsData.categories[finalCategory].name
        });
    } catch (error) {
        console.error('Error updating item:', error);
        res.status(500).json({ error: 'Failed to update item' });
    }
});

// Delete an item
app.delete('/api/items/:id', (req, res) => {
    try {
        const itemId = req.params.id;
        console.log('Server: Deleting item with ID:', itemId);

        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Find and remove the item from its category
        let found = false;
        let deletedFrom = null;

        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            console.log(`Server: Searching for item to delete in category ${categoryKey}`);

            // Try to find by ID first
            let itemIndex = category.items.findIndex(item => item.id === itemId);

            // If not found by ID, try by name
            if (itemIndex === -1) {
                itemIndex = category.items.findIndex(item => item.name === itemId);
                if (itemIndex !== -1) {
                    console.log(`Server: Found item by name in category ${categoryKey} at index ${itemIndex}`);
                }
            } else {
                console.log(`Server: Found item by ID in category ${categoryKey} at index ${itemIndex}`);
            }

            if (itemIndex !== -1) {
                const deletedItem = category.items.splice(itemIndex, 1)[0];
                console.log(`Server: Deleted item from category ${categoryKey}:`, deletedItem);
                found = true;
                deletedFrom = categoryKey;
            }
        });

        if (!found) {
            console.log('Server: Item not found for deletion with ID:', itemId);
            return res.status(404).json({ error: 'Item not found' });
        }

        // Save the updated data
        fs.writeFileSync('items.json', JSON.stringify(itemsData, null, 2));
        console.log(`Server: Item successfully deleted from category ${deletedFrom}`);

        res.json({ message: 'Item deleted successfully' });
    } catch (error) {
        console.error('Server: Error deleting item:', error);
        res.status(500).json({ error: 'Failed to delete item' });
    }
});

// Get all categories
app.get('/api/categories', (req, res) => {
    try {
        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        const categories = Object.keys(itemsData.categories).map(key => ({
            id: key,
            name: itemsData.categories[key].name
        }));

        res.json(categories);
    } catch (error) {
        console.error('Error reading categories:', error);
        res.status(500).json({ error: 'Failed to read categories' });
    }
});

// Get orders for reporting
app.get('/api/orders/report', (req, res) => {
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    console.log(`Fetching orders for report from ${startDate} to ${endDate}`);

    // Validate dates
    if (!startDate || !endDate) {
        return res.status(400).json({
            success: false,
            error: 'Start date and end date are required'
        });
    }

    // Add one day to end date to include the entire end date
    const endDateObj = new Date(endDate);
    endDateObj.setDate(endDateObj.getDate() + 1);
    const adjustedEndDate = endDateObj.toISOString().split('T')[0];

    // Query orders within date range
    const sql = `
        SELECT * FROM orders
        WHERE timestamp >= ? AND timestamp < ?
        ORDER BY timestamp DESC
    `;

    db.all(sql, [startDate, adjustedEndDate], (err, rows) => {
        if (err) {
            console.error('Error fetching orders for report:', err);
            return res.status(500).json({
                success: false,
                error: err.message
            });
        }

        console.log(`Found ${rows.length} orders for the report`);

        res.json({
            success: true,
            orders: rows
        });
    });
});

// Process payment
app.post('/process-payment', (req, res) => {
    const { orderData, paymentMethod } = req.body;

    // Generate a unique transaction ID
    const transactionId = generateTransactionId();

    // For both cash and card payments, update the order status
    const sql = `
        UPDATE orders
        SET paid = 1,
            status = 'completed',
            paymentMethod = ?,
            transactionId = ?
        WHERE tableNumber = ? AND customerName = ? AND (paid = 0 OR paymentMethod IS NULL)
    `;

    db.run(sql, [paymentMethod, transactionId, orderData.tableNumber, orderData.customerName], function(err) {
        if (err) {
            console.error('Error processing payment:', err);
            return res.status(500).json({ error: err.message });
        }

        if (this.changes === 0) {
            // Check if the order is already paid
            db.get(
                'SELECT paid, paymentMethod FROM orders WHERE tableNumber = ? AND customerName = ?',
                [orderData.tableNumber, orderData.customerName],
                (err, row) => {
                    if (err) {
                        return res.status(500).json({ error: err.message });
                    }
                    if (row && row.paid) {
                        return res.status(200).json({
                            message: 'Order already paid',
                            paymentMethod: row.paymentMethod
                        });
                    }
                    return res.status(404).json({ error: 'No orders found for this table and customer' });
                }
            );
            return;
        }

        res.status(200).json({
            success: true,
            message: 'Payment processed successfully',
            tableNumber: orderData.tableNumber,
            customerName: orderData.customerName,
            paymentMethod: paymentMethod,
            transactionId: transactionId
        });
    });
});

// Get orders from a specific backup
app.get('/backup/:backupName/orders', (req, res) => {
    const backupName = req.params.backupName;
    const backupType = req.query.type || 'manual';
    const backupPath = path.join(__dirname, 'backups', backupType, backupName);
    const backupDbPath = path.join(backupPath, 'orders.db');

    console.log('Loading orders from backup:', backupName);
    console.log('Backup path:', backupPath);
    console.log('Database path:', backupDbPath);

    if (!fs.existsSync(backupDbPath)) {
        console.error('Backup database not found at:', backupDbPath);
        return res.status(404).json({ error: 'Backup database not found' });
    }

    // Create a temporary connection to the backup database
    const backupDb = new sqlite3.Database(backupDbPath);

    // First check if the orders table exists
    backupDb.get("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'", (err, row) => {
        if (err) {
            console.error('Error checking for orders table:', err);
            backupDb.close();
            return res.status(500).json({ error: err.message });
        }

        if (!row) {
            console.error('Orders table not found in backup database');
            backupDb.close();
            return res.status(404).json({ error: 'Orders table not found in backup' });
        }

        // Check the schema of the orders table
        backupDb.all("PRAGMA table_info(orders)", (err, columns) => {
            if (err) {
                console.error('Error checking table schema:', err);
                backupDb.close();
                return res.status(500).json({ error: err.message });
            }

            console.log('Table schema:', columns);

            // Determine which columns exist
            const hasTotalColumn = columns.some(col => col.name === 'total');
            const hasTipColumn = columns.some(col => col.name === 'tip');

            // Build the query based on available columns
            let query = `
                SELECT id, tableNumber, customerName, items, paymentMethod, status, timestamp
            `;

            if (hasTotalColumn) {
                query += `, total`;
            }

            if (hasTipColumn) {
                query += `, tip`;
            }

            query += ` FROM orders ORDER BY timestamp DESC`;

            // Get all orders
            backupDb.all(query, (err, rows) => {
                if (err) {
                    console.error('Error fetching orders:', err);
                    backupDb.close();
                    return res.status(500).json({ error: err.message });
                }

                console.log(`Found ${rows.length} orders in backup`);

                // Process the results
                const orders = rows.map(row => {
                    console.log('Processing order:', row.id);
                    let items = [];
                    try {
                        items = JSON.parse(row.items);
                        console.log('Successfully parsed items for order:', row.id);
                    } catch (e) {
                        console.error('Error parsing items for order:', row.id, e);
                    }

                    // Calculate total from items if total column doesn't exist
                    let total = row.total;
                    if (total === undefined) {
                        total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                    }

                    return {
                        id: row.id,
                        tableNumber: row.tableNumber,
                        customerName: row.customerName,
                        paymentMethod: row.paymentMethod,
                        status: row.status,
                        total: total,
                        tip: row.tip || 0,
                        items: items,
                        timestamp: row.timestamp
                    };
                });

                console.log('Returning orders:', orders);
                backupDb.close();
                res.json({ orders });
            });
        });
    });
});

// Generate QR code for transaction ID
app.get('/api/qrcode/:transactionId', async (req, res) => {
    try {
        const { transactionId } = req.params;

        if (!transactionId) {
            return res.status(400).json({
                success: false,
                error: 'Transaction ID is required'
            });
        }

        // Generate QR code as data URL
        const qrCodeDataUrl = await QRCode.toDataURL(transactionId, {
            width: 200,
            margin: 1,
            color: {
                dark: '#000000',
                light: '#ffffff'
            }
        });

        res.json({
            success: true,
            qrCode: qrCodeDataUrl
        });
    } catch (error) {
        console.error('Error generating QR code:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate QR code'
        });
    }
});

// Get transaction details by ID
app.get('/api/transaction/:transactionId', (req, res) => {
    const { transactionId } = req.params;

    if (!transactionId) {
        return res.status(400).json({
            success: false,
            error: 'Transaction ID is required'
        });
    }

    db.get(
        'SELECT * FROM orders WHERE transactionId = ?',
        [transactionId],
        (err, order) => {
            if (err) {
                return res.status(500).json({ error: err.message });
            }
            if (!order) {
                return res.status(404).json({ error: 'Transaction not found' });
            }

            try {
                order.items = JSON.parse(order.items);
                res.json({
                    success: true,
                    transaction: order
                });
            } catch (error) {
                res.status(500).json({ error: 'Error parsing order items' });
            }
        }
    );
});

// WhatsApp API endpoint
app.post('/send-whatsapp', (req, res) => {
    const { phoneNumber, message, senderNumber } = req.body;

    if (!phoneNumber || !message) {
        return res.status(400).json({
            success: false,
            error: 'Phone number and message are required'
        });
    }

    // Get the sender's WhatsApp number from the request or from saved business info
    let sender = senderNumber;

    // If no sender number provided, try to get from business info
    if (!sender && fs.existsSync('whatsapp-business-info.json')) {
        try {
            const businessInfo = JSON.parse(fs.readFileSync('whatsapp-business-info.json', 'utf8'));
            sender = businessInfo.senderWhatsapp || 'Default Business Number';
        } catch (error) {
            console.error('Error reading business info:', error);
            sender = 'Default Business Number';
        }
    }

    console.log(`Sending WhatsApp message from ${sender} to ${phoneNumber}`);

    // In a real implementation, this would connect to the WhatsApp Business API
    // For now, we'll simulate a successful send

    // Log the message content for debugging
    console.log('Message content:', message);

    // Simulate API delay
    setTimeout(() => {
        res.json({
            success: true,
            message: 'WhatsApp message sent successfully',
            sender: sender
        });
    }, 1000);
});

// WhatsApp template API endpoint
app.post('/api/whatsapp-template', (req, res) => {
    const { template, businessInfo } = req.body;

    if (!template) {
        return res.status(400).json({
            success: false,
            error: 'Template is required'
        });
    }

    // In a real implementation, this would save to a database
    // For now, we'll just save to files
    try {
        // Save template
        fs.writeFileSync('whatsapp-template.txt', template, 'utf8');

        // Save business info if provided
        if (businessInfo) {
            fs.writeFileSync('whatsapp-business-info.json', JSON.stringify(businessInfo, null, 2), 'utf8');
        }

        res.json({
            success: true,
            message: 'WhatsApp settings saved successfully'
        });
    } catch (error) {
        console.error('Error saving WhatsApp settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to save settings'
        });
    }
});

// Get WhatsApp template and business info
app.get('/api/whatsapp-template', (req, res) => {
    try {
        // Default template
        const defaultTemplate = `{{#logoTop}}
{{logoUrl}}
{{/logoTop}}

*{{businessName}}*
{{#businessTagline}}{{businessTagline}}{{/businessTagline}}

Date: {{date}} | Time: {{time}}
Table: {{tableNumber}} | Customer: {{customerName}}

*Items:*
{{items}}

*Summary:*
Subtotal: ${{subtotal}}
GST (5%): ${{gst}}
PST (7%): ${{pst}}
*Total: ${{total}}*

Payment Method: {{paymentMethod}}
{{#transactionId}}Transaction ID: {{transactionId}}{{/transactionId}}

Thank you for your business!
{{#contactInfo}}Contact us: {{contactInfo}}{{/contactInfo}}

{{#qrCode}}
Scan to verify receipt:
{{qrCode}}
{{/qrCode}}

{{#logoBottom}}
{{logoUrl}}
{{/logoBottom}}`;

        // Default business info
        const defaultBusinessInfo = {
            name: 'Paradiz Hookah Lounge',
            tagline: 'The Best Hookah Experience',
            logoUrl: '',
            logoPosition: 'top',
            contactInfo: '',
            senderWhatsapp: ''
        };

        // Response object
        const response = {
            success: true,
            template: defaultTemplate,
            businessInfo: defaultBusinessInfo
        };

        // Check if template file exists
        if (fs.existsSync('whatsapp-template.txt')) {
            response.template = fs.readFileSync('whatsapp-template.txt', 'utf8');
        }

        // Check if business info file exists
        if (fs.existsSync('whatsapp-business-info.json')) {
            try {
                response.businessInfo = JSON.parse(fs.readFileSync('whatsapp-business-info.json', 'utf8'));
            } catch (parseError) {
                console.error('Error parsing business info:', parseError);
                // Keep default business info if parsing fails
            }
        }

        res.json(response);
    } catch (error) {
        console.error('Error reading WhatsApp settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to read WhatsApp settings'
        });
    }
});

// Function to generate a unique transaction ID
function generateTransactionId() {
    const timestamp = new Date().getTime();
    const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `TRX-${timestamp}-${randomPart}`;
}

const PORT = 5000;
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});