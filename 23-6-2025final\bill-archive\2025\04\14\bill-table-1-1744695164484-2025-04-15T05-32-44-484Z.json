{"id": "table-1-1744695164484", "tableNumber": "1", "customerName": "Table 1 (Combined)", "timestamp": "2025-04-15T05:32:44.484Z", "items": [{"name": "Raspberry + Guava", "price": 25, "quantity": 1, "notes": ""}, {"name": "Veggie Burger", "price": 1.11, "quantity": 1, "notes": ""}, {"name": "Wings", "price": 9.99, "quantity": 1, "notes": ""}, {"name": "Grape + Double apple", "price": 25, "quantity": 1, "notes": ""}, {"name": "<PERSON><PERSON>", "price": 7.99, "quantity": 1, "notes": ""}], "payment": {"paymentMethod": "card", "tip": 0, "subtotal": 69.09, "tax": 3.45, "taxRate": 0.04993486756404689, "gst": 3.45, "gstRate": 0.04993486756404689, "pst": 0, "pstRate": 0, "total": 72.54, "transactionId": null}, "archiveTimestamp": "2025-04-15T05:32:44.486Z"}