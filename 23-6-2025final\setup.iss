[Setup]
AppName=Paradiz POS System
AppVersion=1.0
DefaultDirName={pf}\ParadizPOS
DefaultGroupName=Paradiz POS
OutputDir=installer
OutputBaseFilename=paradiz-pos-setup

[Files]
; Application files
Source: "server.js"; DestDir: "{app}"
Source: "package.json"; DestDir: "{app}"
Source: "start_pos.bat"; DestDir: "{app}"
Source: "*.html"; DestDir: "{app}"
Source: "*.css"; DestDir: "{app}"
Source: "*.js"; DestDir: "{app}"
; Include all subdirectories
Source: "pos\*"; DestDir: "{app}\pos"; Flags: recursesubdirs
Source: "kitchen-displays\*"; DestDir: "{app}\kitchen-displays"; Flags: recursesubdirs
Source: "payment\*"; DestDir: "{app}\payment"; Flags: recursesubdirs
Source: "static\*"; DestDir: "{app}\static"; Flags: recursesubdirs
Source: "tablet\*"; DestDir: "{app}\tablet"; Flags: recursesubdirs

[Icons]
Name: "{group}\Paradiz POS"; Filename: "{app}\start_pos.bat"
Name: "{commondesktop}\Paradiz POS"; Filename: "{app}\start_pos.bat"

[Run]
; Install Node.js if not present
Filename: "https://nodejs.org/dist/latest-v18.x/node-v18.x.x-x64.msi"; Parameters: "/quiet"; Check: not IsNodeInstalled; Flags: shellexec waituntilterminated

[Code]
function IsNodeInstalled: Boolean;
var
  ResultCode: Integer;
begin
  Exec('cmd.exe', '/c where node', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
  Result := (ResultCode = 0);
end;