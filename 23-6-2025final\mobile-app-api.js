/**
 * Mobile App API Endpoints
 * This file contains the API endpoints for the mobile app to connect to the POS system
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// JWT Secret Key - should be stored in environment variables in production
const JWT_SECRET = 'paradiz-hookah-lounge-secret-key';

// Initialize Mobile App API
function initializeMobileAppAPI(app, db) {
    console.log('Initializing Mobile App API...');

    // Middleware to verify JWT token
    const authenticateToken = (req, res, next) => {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];
        
        if (!token) {
            return res.status(401).json({ success: false, message: 'Authentication required' });
        }
        
        jwt.verify(token, JWT_SECRET, (err, user) => {
            if (err) {
                return res.status(403).json({ success: false, message: 'Invalid or expired token' });
            }
            
            req.user = user;
            next();
        });
    };

    // Authentication Endpoints
    
    // Register new user
    app.post('/api/auth/register', async (req, res) => {
        try {
            const { name, email, password } = req.body;
            
            // Validate input
            if (!name || !email || !password) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Name, email, and password are required' 
                });
            }
            
            // Check if user already exists
            const existingUser = await db.get('SELECT * FROM mobile_users WHERE email = ?', [email]);
            
            if (existingUser) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'User with this email already exists' 
                });
            }
            
            // Hash password
            const hashedPassword = await bcrypt.hash(password, 10);
            
            // Create user
            const result = await db.run(
                'INSERT INTO mobile_users (name, email, password, created_at) VALUES (?, ?, ?, datetime("now"))',
                [name, email, hashedPassword]
            );
            
            // Generate token
            const token = jwt.sign(
                { id: result.lastID, name, email }, 
                JWT_SECRET, 
                { expiresIn: '30d' }
            );
            
            res.json({
                success: true,
                user: { id: result.lastID, name, email },
                token
            });
        } catch (error) {
            console.error('Error registering user:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to register user' 
            });
        }
    });
    
    // Login
    app.post('/api/auth/login', async (req, res) => {
        try {
            const { email, password } = req.body;
            
            // Validate input
            if (!email || !password) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Email and password are required' 
                });
            }
            
            // Find user
            const user = await db.get('SELECT * FROM mobile_users WHERE email = ?', [email]);
            
            if (!user) {
                return res.status(401).json({ 
                    success: false, 
                    message: 'Invalid email or password' 
                });
            }
            
            // Check password
            const passwordMatch = await bcrypt.compare(password, user.password);
            
            if (!passwordMatch) {
                return res.status(401).json({ 
                    success: false, 
                    message: 'Invalid email or password' 
                });
            }
            
            // Generate token
            const token = jwt.sign(
                { id: user.id, name: user.name, email: user.email }, 
                JWT_SECRET, 
                { expiresIn: '30d' }
            );
            
            res.json({
                success: true,
                user: { id: user.id, name: user.name, email: user.email },
                token
            });
        } catch (error) {
            console.error('Error logging in:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to log in' 
            });
        }
    });
    
    // Menu Endpoints
    
    // Get menu categories
    app.get('/api/menu/categories', async (req, res) => {
        try {
            const categories = await db.all('SELECT * FROM categories ORDER BY name');
            
            res.json({
                success: true,
                categories
            });
        } catch (error) {
            console.error('Error getting menu categories:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get menu categories' 
            });
        }
    });
    
    // Get menu items
    app.get('/api/menu/items', async (req, res) => {
        try {
            const { category } = req.query;
            
            let query = 'SELECT * FROM items WHERE active = 1';
            let params = [];
            
            if (category) {
                query += ' AND category_id = ?';
                params.push(category);
            }
            
            query += ' ORDER BY name';
            
            const items = await db.all(query, params);
            
            res.json({
                success: true,
                items
            });
        } catch (error) {
            console.error('Error getting menu items:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get menu items' 
            });
        }
    });
    
    // Get menu item details
    app.get('/api/menu/items/:id', async (req, res) => {
        try {
            const { id } = req.params;
            
            const item = await db.get('SELECT * FROM items WHERE id = ?', [id]);
            
            if (!item) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Item not found' 
                });
            }
            
            res.json({
                success: true,
                item
            });
        } catch (error) {
            console.error('Error getting menu item details:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get menu item details' 
            });
        }
    });
    
    // Search menu items
    app.get('/api/menu/search', async (req, res) => {
        try {
            const { q } = req.query;
            
            if (!q) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Search query is required' 
                });
            }
            
            const items = await db.all(
                'SELECT * FROM items WHERE active = 1 AND (name LIKE ? OR description LIKE ?) ORDER BY name',
                [`%${q}%`, `%${q}%`]
            );
            
            res.json({
                success: true,
                items
            });
        } catch (error) {
            console.error('Error searching menu items:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to search menu items' 
            });
        }
    });
    
    // Reservation Endpoints
    
    // Get availability
    app.get('/api/reservations/availability', async (req, res) => {
        try {
            const { date } = req.query;
            
            if (!date) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Date is required' 
                });
            }
            
            // Get existing reservations for the date
            const reservations = await db.all(
                'SELECT time FROM reservations WHERE date = ?',
                [date]
            );
            
            // Define available time slots
            const allTimeSlots = [
                '17:00', '17:30', '18:00', '18:30', '19:00', '19:30',
                '20:00', '20:30', '21:00', '21:30', '22:00', '22:30'
            ];
            
            // Filter out booked time slots
            // In a real system, you would also check table availability
            const bookedTimes = reservations.map(r => r.time);
            const availableSlots = allTimeSlots.filter(time => !bookedTimes.includes(time));
            
            res.json({
                success: true,
                availableSlots
            });
        } catch (error) {
            console.error('Error getting reservation availability:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get reservation availability' 
            });
        }
    });
    
    // Create reservation
    app.post('/api/reservations', authenticateToken, async (req, res) => {
        try {
            const { date, time, partySize, specialRequests } = req.body;
            const userId = req.user.id;
            
            // Validate input
            if (!date || !time || !partySize) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Date, time, and party size are required' 
                });
            }
            
            // Check if the time slot is available
            const existingReservation = await db.get(
                'SELECT * FROM reservations WHERE date = ? AND time = ?',
                [date, time]
            );
            
            if (existingReservation) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'This time slot is no longer available' 
                });
            }
            
            // Create reservation
            const result = await db.run(
                `INSERT INTO reservations (
                    user_id, date, time, party_size, special_requests, status, created_at
                ) VALUES (?, ?, ?, ?, ?, 'confirmed', datetime("now"))`,
                [userId, date, time, partySize, specialRequests || '']
            );
            
            res.json({
                success: true,
                reservation: {
                    id: result.lastID,
                    date,
                    time,
                    partySize,
                    specialRequests: specialRequests || '',
                    status: 'confirmed'
                }
            });
        } catch (error) {
            console.error('Error creating reservation:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to create reservation' 
            });
        }
    });
    
    // Get user reservations
    app.get('/api/reservations/user', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            
            const reservations = await db.all(
                `SELECT * FROM reservations 
                WHERE user_id = ? AND date >= date('now') 
                ORDER BY date, time`,
                [userId]
            );
            
            res.json({
                success: true,
                reservations
            });
        } catch (error) {
            console.error('Error getting user reservations:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get reservations' 
            });
        }
    });
    
    // Cancel reservation
    app.delete('/api/reservations/:id', authenticateToken, async (req, res) => {
        try {
            const { id } = req.params;
            const userId = req.user.id;
            
            // Check if reservation exists and belongs to user
            const reservation = await db.get(
                'SELECT * FROM reservations WHERE id = ? AND user_id = ?',
                [id, userId]
            );
            
            if (!reservation) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Reservation not found' 
                });
            }
            
            // Update reservation status
            await db.run(
                "UPDATE reservations SET status = 'cancelled' WHERE id = ?",
                [id]
            );
            
            res.json({
                success: true,
                message: 'Reservation cancelled successfully'
            });
        } catch (error) {
            console.error('Error cancelling reservation:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to cancel reservation' 
            });
        }
    });
    
    // Order Endpoints
    
    // Create order
    app.post('/api/orders', async (req, res) => {
        try {
            const { tableId, sessionId, items } = req.body;
            
            // Validate input
            if (!tableId || !sessionId || !items || !items.length) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Table ID, session ID, and items are required' 
                });
            }
            
            // Create order
            const result = await db.run(
                `INSERT INTO orders (
                    table_number, order_source, status, created_at
                ) VALUES (?, 'mobile_app', 'new', datetime("now"))`,
                [tableId]
            );
            
            const orderId = result.lastID;
            
            // Add order items
            for (const item of items) {
                await db.run(
                    `INSERT INTO order_items (
                        order_id, item_id, quantity, price, customizations
                    ) VALUES (?, ?, ?, ?, ?)`,
                    [orderId, item.id, item.quantity, item.price, JSON.stringify(item.customizations || [])]
                );
            }
            
            // Get the created order
            const order = await db.get('SELECT * FROM orders WHERE id = ?', [orderId]);
            
            res.json({
                success: true,
                order
            });
        } catch (error) {
            console.error('Error creating order:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to create order' 
            });
        }
    });
    
    // Get table order
    app.get('/api/orders/table/:tableId', async (req, res) => {
        try {
            const { tableId } = req.params;
            const { sessionId } = req.query;
            
            // Validate input
            if (!tableId || !sessionId) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Table ID and session ID are required' 
                });
            }
            
            // Get active order for table
            const order = await db.get(
                `SELECT * FROM orders 
                WHERE table_number = ? AND status IN ('new', 'in_progress') 
                ORDER BY created_at DESC LIMIT 1`,
                [tableId]
            );
            
            res.json({
                success: true,
                order: order || null
            });
        } catch (error) {
            console.error('Error getting table order:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get table order' 
            });
        }
    });
    
    // Add items to order
    app.post('/api/orders/:id/items', async (req, res) => {
        try {
            const { id } = req.params;
            const { items } = req.body;
            
            // Validate input
            if (!items || !items.length) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Items are required' 
                });
            }
            
            // Check if order exists
            const order = await db.get('SELECT * FROM orders WHERE id = ?', [id]);
            
            if (!order) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Order not found' 
                });
            }
            
            // Add order items
            for (const item of items) {
                await db.run(
                    `INSERT INTO order_items (
                        order_id, item_id, quantity, price, customizations
                    ) VALUES (?, ?, ?, ?, ?)`,
                    [id, item.id, item.quantity, item.price, JSON.stringify(item.customizations || [])]
                );
            }
            
            // Update order status if needed
            if (order.status === 'new') {
                await db.run(
                    "UPDATE orders SET status = 'in_progress' WHERE id = ?",
                    [id]
                );
            }
            
            // Get updated order
            const updatedOrder = await db.get('SELECT * FROM orders WHERE id = ?', [id]);
            
            res.json({
                success: true,
                order: updatedOrder
            });
        } catch (error) {
            console.error('Error adding items to order:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to add items to order' 
            });
        }
    });
    
    // Get user orders
    app.get('/api/orders/user', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            
            const orders = await db.all(
                `SELECT o.* FROM orders o
                JOIN mobile_order_users mou ON o.id = mou.order_id
                WHERE mou.user_id = ?
                ORDER BY o.created_at DESC`,
                [userId]
            );
            
            res.json({
                success: true,
                orders
            });
        } catch (error) {
            console.error('Error getting user orders:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get orders' 
            });
        }
    });
    
    // Get order details
    app.get('/api/orders/:id', async (req, res) => {
        try {
            const { id } = req.params;
            
            // Get order
            const order = await db.get('SELECT * FROM orders WHERE id = ?', [id]);
            
            if (!order) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Order not found' 
                });
            }
            
            // Get order items
            const items = await db.all(
                `SELECT oi.*, i.name, i.description 
                FROM order_items oi
                JOIN items i ON oi.item_id = i.id
                WHERE oi.order_id = ?`,
                [id]
            );
            
            res.json({
                success: true,
                order: {
                    ...order,
                    items
                }
            });
        } catch (error) {
            console.error('Error getting order details:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get order details' 
            });
        }
    });
    
    // Pay for order
    app.post('/api/orders/:id/pay', authenticateToken, async (req, res) => {
        try {
            const { id } = req.params;
            const { paymentMethod, amount, tip } = req.body;
            const userId = req.user.id;
            
            // Validate input
            if (!paymentMethod || !amount) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Payment method and amount are required' 
                });
            }
            
            // Check if order exists
            const order = await db.get('SELECT * FROM orders WHERE id = ?', [id]);
            
            if (!order) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Order not found' 
                });
            }
            
            // Update order payment status
            await db.run(
                `UPDATE orders SET 
                    payment_status = 'paid',
                    payment_method = ?,
                    payment_amount = ?,
                    tip_amount = ?,
                    status = 'completed',
                    updated_at = datetime("now")
                WHERE id = ?`,
                [paymentMethod, amount, tip || 0, id]
            );
            
            // Associate order with user if not already
            const existingAssociation = await db.get(
                'SELECT * FROM mobile_order_users WHERE order_id = ? AND user_id = ?',
                [id, userId]
            );
            
            if (!existingAssociation) {
                await db.run(
                    'INSERT INTO mobile_order_users (order_id, user_id) VALUES (?, ?)',
                    [id, userId]
                );
            }
            
            res.json({
                success: true,
                message: 'Payment processed successfully'
            });
        } catch (error) {
            console.error('Error processing payment:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to process payment' 
            });
        }
    });
    
    // User Profile Endpoints
    
    // Get user profile
    app.get('/api/users/profile', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            
            const user = await db.get(
                'SELECT id, name, email, created_at FROM mobile_users WHERE id = ?',
                [userId]
            );
            
            if (!user) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'User not found' 
                });
            }
            
            res.json({
                success: true,
                user
            });
        } catch (error) {
            console.error('Error getting user profile:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get user profile' 
            });
        }
    });
    
    // Update user profile
    app.put('/api/users/profile', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            const { name, email } = req.body;
            
            // Validate input
            if (!name || !email) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Name and email are required' 
                });
            }
            
            // Check if email is already taken by another user
            const existingUser = await db.get(
                'SELECT * FROM mobile_users WHERE email = ? AND id != ?',
                [email, userId]
            );
            
            if (existingUser) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Email is already taken' 
                });
            }
            
            // Update user
            await db.run(
                'UPDATE mobile_users SET name = ?, email = ? WHERE id = ?',
                [name, email, userId]
            );
            
            res.json({
                success: true,
                user: { id: userId, name, email }
            });
        } catch (error) {
            console.error('Error updating user profile:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to update user profile' 
            });
        }
    });
    
    // Favorites Endpoints
    
    // Get user favorites
    app.get('/api/users/favorites', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            
            const favorites = await db.all(
                `SELECT i.* FROM items i
                JOIN user_favorites uf ON i.id = uf.item_id
                WHERE uf.user_id = ?
                ORDER BY i.name`,
                [userId]
            );
            
            res.json({
                success: true,
                favorites
            });
        } catch (error) {
            console.error('Error getting user favorites:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to get favorites' 
            });
        }
    });
    
    // Add favorite
    app.post('/api/users/favorites', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            const { itemId } = req.body;
            
            // Validate input
            if (!itemId) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Item ID is required' 
                });
            }
            
            // Check if item exists
            const item = await db.get('SELECT * FROM items WHERE id = ?', [itemId]);
            
            if (!item) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Item not found' 
                });
            }
            
            // Check if already a favorite
            const existingFavorite = await db.get(
                'SELECT * FROM user_favorites WHERE user_id = ? AND item_id = ?',
                [userId, itemId]
            );
            
            if (existingFavorite) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Item is already a favorite' 
                });
            }
            
            // Add favorite
            await db.run(
                'INSERT INTO user_favorites (user_id, item_id) VALUES (?, ?)',
                [userId, itemId]
            );
            
            res.json({
                success: true,
                message: 'Item added to favorites'
            });
        } catch (error) {
            console.error('Error adding favorite:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to add favorite' 
            });
        }
    });
    
    // Remove favorite
    app.delete('/api/users/favorites/:itemId', authenticateToken, async (req, res) => {
        try {
            const userId = req.user.id;
            const { itemId } = req.params;
            
            // Remove favorite
            await db.run(
                'DELETE FROM user_favorites WHERE user_id = ? AND item_id = ?',
                [userId, itemId]
            );
            
            res.json({
                success: true,
                message: 'Item removed from favorites'
            });
        } catch (error) {
            console.error('Error removing favorite:', error);
            res.status(500).json({ 
                success: false, 
                message: 'Failed to remove favorite' 
            });
        }
    });

    console.log('Mobile App API initialized successfully');
}

module.exports = initializeMobileAppAPI;
