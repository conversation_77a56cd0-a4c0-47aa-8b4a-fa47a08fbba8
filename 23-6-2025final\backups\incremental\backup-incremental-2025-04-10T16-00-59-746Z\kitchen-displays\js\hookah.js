document.addEventListener('DOMContentLoaded', () => {
    console.log('Hookah display initialized');
    initializeDisplay();
});

function initializeDisplay() {
    // First immediate update
    updateOrders();

    // Set up regular polling
    setInterval(updateOrders, 5000);
    setInterval(updateTimers, 1000);

    console.log('Display initialized with auto-refresh');
}

// Format time
function formatTime(timestamp) {
    if (!timestamp) return "0:00";
    try {
        const now = new Date();
        const start = new Date(timestamp);

        // Validate the dates
        if (isNaN(start.getTime()) || isNaN(now.getTime())) {
            console.warn('Invalid date:', { timestamp, now });
            return "0:00";
        }

        // Calculate time difference in seconds
        const diff = Math.max(0, Math.floor((now - start) / 1000));
        const minutes = Math.floor(diff / 60);
        const seconds = diff % 60;

        // Format with leading zeros
        const formattedTime = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        console.log('Formatted time:', { timestamp, now, diff, formattedTime });
        return formattedTime;
    } catch (e) {
        console.warn('Time format error:', e);
        return "0:00";
    }
}

// Filter hookah items
function filterHookahItems(items) {
    if (!Array.isArray(items)) return [];

    console.log('Filtering hookah items from:', items);

    // Always include hookah items regardless of whether there's a hookah in the order
    // This ensures we catch standalone hookah flavors
    return items.filter(item => {
        if (!item || !item.name) return false;

        const name = item.name.toLowerCase();
        console.log('Checking item:', item.name);

        // First, check if it's a hookah
        if (name === 'hookah' || name.includes('shisha')) {
            console.log('Found hookah item:', item.name);
            return true;
        }

        // Check for specific flavors by name
        // Use exact flavor names to avoid false positives
        const hookahFlavors = [
            'mint', 'grape', 'watermelon', 'double apple', 'midnight',
            'blueberry', 'blueberry1', 'bluemist', 'blue mist', 'mango',
            'orange', 'lemon', 'lime', 'coconut', 'pineapple',
            'peach', 'cherry', 'berry', 'melon', 'strawberry',
            'vanilla', 'coffee', 'rose', 'guava', 'kiwi'
        ];

        // Check if the item name contains any of the flavor names
        for (const flavor of hookahFlavors) {
            if (name.includes(flavor)) {
                console.log('Found hookah flavor:', item.name, 'matched with', flavor);
                return true;
            }
        }

        // Check for flavor combinations (e.g., "Mint + Blueberry")
        if (name.includes('+') &&
            hookahFlavors.some(f => name.includes(f))) {
            console.log('Found hookah flavor combination:', item.name);
            return true;
        }

        // Check for other hookah-related terms
        const hookahTerms = ['tobacco', 'flavor', 'flavour', 'mix', 'blend'];
        if (hookahTerms.some(term => name.includes(term))) {
            console.log('Found hookah-related term in:', item.name);
            return true;
        }

        // Not a hookah item
        return false;
    });
}

// Create order card
function createOrderCard(order) {
    console.log('Creating hookah order card:', order.id);
    const hookahItems = filterHookahItems(order.items);
    if (hookahItems.length === 0) return '';

    return `
        <div class="order-card" data-order-id="${order.id}" data-timestamp="${order.timestamp}">
            <div class="order-info">
                <div class="table-number">${order.tableNumber || 'N/A'}</div>
                <div class="customer-name">${order.customerName || ''}</div>
            </div>
            <div class="order-items">
                ${hookahItems.map(item => `
                    <div class="order-item">
                        ${item.name} ${item.notes ? `(${item.notes})` : ''}
                    </div>
                `).join('')}
            </div>
            <div class="order-footer">
                <span class="order-timer">${formatTime(order.timestamp)}</span>
                <button class="complete-btn" type="button">Complete</button>
            </div>
        </div>
    `;
}

// Update orders
async function updateOrders() {
    try {
        const response = await fetch('http://localhost:5000/orders');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const orders = await response.json();
        console.log('All orders:', orders);
        const container = document.getElementById('orders-container');

        // Filter orders that have hookah items and are not completed
        const hookahOrders = orders.filter(order => {
            console.log('Checking order:', order.id, 'Table:', order.tableNumber, 'Customer:', order.customerName);
            console.log('Items:', order.items);

            const hookahItems = filterHookahItems(order.items);
            console.log('Hookah items found:', hookahItems);

            // Handle both string and object kitchen status
            let isCompleted = false;
            if (order.kitchenStatus) {
                console.log('Kitchen status:', order.kitchenStatus, 'Type:', typeof order.kitchenStatus);
                if (typeof order.kitchenStatus === 'string') {
                    // If it's a string, check if it's 'completed'
                    isCompleted = order.kitchenStatus === 'completed';
                } else if (typeof order.kitchenStatus === 'object') {
                    // If it's an object, check the hookah property
                    isCompleted = order.kitchenStatus.hookah === 'completed';
                }
                console.log('Is completed:', isCompleted);
            }

            const shouldInclude = hookahItems.length > 0 && !isCompleted;
            console.log('Should include in hookah display:', shouldInclude);
            return shouldInclude;
        });

        if (hookahOrders.length === 0) {
            container.innerHTML = '<div class="no-orders">No hookah orders at this time</div>';
            return;
        }

        // Render orders and add event listeners
        const orderElements = hookahOrders.map(createOrderCard).join('');
        container.innerHTML = orderElements;

        // Add event listeners after adding HTML
        hookahOrders.forEach(order => {
            const button = container.querySelector(`.order-card[data-order-id="${order.id}"] .complete-btn`);
            if (button) {
                button.addEventListener('click', () => completeOrder(order.id));
            }
        });
    } catch (error) {
        console.error('Failed to update orders:', error);
        document.getElementById('orders-container').innerHTML =
            `<div class="no-orders">Error: ${error.message}</div>`;
    }
}

// Complete order
async function completeOrder(orderId) {
    console.log('Completing hookah order:', orderId);

    // Show confirmation dialog
    const isConfirmed = confirm('Are you sure you want to mark this hookah order as complete?');
    if (!isConfirmed) return;

    // Find and disable the button immediately
    const button = document.querySelector(`.order-card[data-order-id="${orderId}"] .complete-btn`);
    if (button) {
        button.disabled = true;
        button.textContent = 'Completing...';
    }

    try {
        const response = await fetch(`http://localhost:5000/orders/${orderId}/kitchen`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                status: 'completed',
                type: 'hookah'
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Remove the completed order card
        const orderCard = document.querySelector(`.order-card[data-order-id="${orderId}"]`);
        if (orderCard) {
            orderCard.remove();
        }

        // Update the display
        await updateOrders();
    } catch (error) {
        console.error('Error completing order:', error);
        // Re-enable the button if there was an error
        if (button) {
            button.disabled = false;
            button.textContent = 'Complete';
        }
        alert('Failed to complete order. Please try again.');
    }
}

// Update timers
function updateTimers() {
    document.querySelectorAll('.order-timer').forEach(timer => {
        const card = timer.closest('.order-card');
        if (card) {
            const timestamp = card.getAttribute('data-timestamp');
            if (timestamp) {
                timer.textContent = formatTime(timestamp);
            }
        }
    });
}