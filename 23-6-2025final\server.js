const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const QRCode = require('qrcode');
const EnhancedBackupService = require('./enhanced-backup-service');
const PrinterService = require('./printer-service');
const BillArchiveService = require('./bill-archive-service');
const initializeMobileAppAPI = require('./mobile-app-api');

// Try to load the data export service
let DataExportService;
try {
    DataExportService = require('./data-export-service');
    console.log('Data Export Service module loaded successfully');
} catch (e) {
    console.error('Failed to load Data Export Service:', e.message);
    // Create a dummy service that will be used if the real one fails to load
    DataExportService = class DummyDataExportService {
        constructor() {
            console.log('Using dummy data export service');
        }

        async exportSalesReport() {
            return {
                success: false,
                error: 'Data Export Service is not available. Please install required dependencies.'
            };
        }
    };
}
const ExpenseService = require('./expense-service');
const crypto = require('crypto');
const https = require('https');
const semver = require('semver');

// Create or open the SQLite database
const db = new sqlite3.Database('./orders.db', (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
    } else {
        console.log('Connected to the SQLite database.');
        // Create the orders table if it doesn't exist
        db.run(`
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tableNumber TEXT NOT NULL,
                customerName TEXT NOT NULL,
                items TEXT NOT NULL,
                coverCharge TEXT,
                paid BOOLEAN DEFAULT 0,
                status TEXT DEFAULT 'pending',
                kitchenStatus TEXT,
                report TEXT,
                paymentMethod TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Create the pending_bills table if it doesn't exist
        db.run(`
            CREATE TABLE IF NOT EXISTS pending_bills (
                id TEXT PRIMARY KEY,
                tableNumber TEXT NOT NULL,
                customerName TEXT NOT NULL,
                items TEXT NOT NULL,
                notes TEXT,
                createdAt DATETIME NOT NULL,
                paidAt DATETIME,
                status TEXT DEFAULT 'pending',
                paymentMethod TEXT,
                transactionId TEXT
            )
        `, function(err) {
            if (err) {
                console.error('Error creating pending_bills table:', err);
            } else {
                console.log('Pending bills table created or already exists');
            }
        });

        // Check if columns exist
        db.all("PRAGMA table_info(orders)", (err, rows) => {
            if (err) {
                console.error('Error checking table structure:', err);
                return;
            }

            const columnsToAdd = [
                { name: 'coverCharge', type: 'TEXT' },
                { name: 'timestamp', type: 'DATETIME DEFAULT CURRENT_TIMESTAMP' },
                { name: 'kitchenStatus', type: 'TEXT' },
                { name: 'paymentMethod', type: 'TEXT' },
                { name: 'transactionId', type: 'TEXT' }
            ];

            columnsToAdd.forEach(column => {
                const hasColumn = rows.some(row => row.name === column.name);
                if (!hasColumn) {
                    console.log(`Adding ${column.name} column...`);
                    db.run(`ALTER TABLE orders ADD COLUMN ${column.name} ${column.type}`);
                }
            });
        });
    }
});

const app = express();

// Configure CORS
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type']
}));

app.use(bodyParser.json());

// Log requests
app.use((req, res, next) => {
    console.log(`${req.method} ${req.url}`);
    next();
});

// Serve static files
app.use(express.static(path.join(__dirname)));
app.use('/tablet', express.static(path.join(__dirname, 'tablet')));
app.use('/pos', express.static(path.join(__dirname, 'pos')));
app.use('/kitchen-displays', express.static(path.join(__dirname, 'kitchen-displays')));
app.use('/payment', express.static(path.join(__dirname, 'payment')));
app.use('/backup-viewer', express.static(path.join(__dirname, 'backup-viewer')));
app.use('/backup-manager', express.static(path.join(__dirname, 'backup-manager')));
app.use('/reports', express.static(path.join(__dirname, 'reports')));
app.use('/backup', express.static(path.join(__dirname, 'backup')));

// Redirect /payment to the new payment page
app.get('/payment', (req, res) => {
    res.redirect('/payment/payment-new.html' + (req.query.orderData ? '?orderData=' + req.query.orderData : ''));
});

// Initialize backup services
// Initialize enhanced backup service

// Initialize enhanced backup service with custom options
const enhancedBackupService = new EnhancedBackupService({
    retentionDays: 7,           // Keep 7 days of backups
    compressionEnabled: true,   // Enable compression to save space
    dailyBackupHour: 3,         // Run daily backup at 3 AM
    incrementalBackupInterval: 4 // Run incremental backup every 4 hours
});
console.log('Enhanced backup service initialized');

// Initialize printer service
let printerService = null;

// Load printer settings from file if exists
const printerSettingsPath = path.join(__dirname, 'printer-settings.json');
let printerConfig = {};

try {
    if (fs.existsSync(printerSettingsPath)) {
        const settingsData = fs.readFileSync(printerSettingsPath, 'utf8');
        printerConfig = JSON.parse(settingsData);
        console.log('Loaded printer settings from file');
    }
} catch (error) {
    console.error('Error loading printer settings:', error);
}

// Initialize printer service with config
printerService = new PrinterService(printerConfig);
console.log('Printer service initialized');

// Initialize bill archive service
const billArchiveService = new BillArchiveService({
    archiveDir: './bill-archive',
    createPdf: true
});
console.log('Bill Archive Service initialized');

// Initialize expense service
const expenseService = new ExpenseService(db);
console.log('Expense Service initialized');

// Initialize data export service
let dataExportService;
try {
    dataExportService = new DataExportService({
        localBackupDir: './data-exports',
        emailConfig: {
            host: 'smtp.gmail.com',
            port: 587,
            secure: false,
            auth: {
                user: process.env.EMAIL_USER || '',
                pass: process.env.EMAIL_PASS || ''
            }
        }
    });
    console.log('Data Export Service initialized');
} catch (e) {
    console.error('Failed to initialize Data Export Service:', e.message);
    dataExportService = {
        exportSalesReport: async () => ({
            success: false,
            error: 'Data Export Service is not available. Please install required dependencies.'
        })
    };
}

// Start scheduled backups
enhancedBackupService.scheduleBackups();
console.log('Enhanced scheduled backups started');

// API Endpoints

// Create a new order
app.post('/orders', (req, res) => {
    const { tableNumber, customerName, items, coverCharge } = req.body;
    const timestamp = new Date().toISOString();

    const sql = `
        INSERT INTO orders (tableNumber, customerName, items, coverCharge, timestamp)
        VALUES (?, ?, ?, ?, ?)
    `;

    db.run(sql, [tableNumber, customerName, JSON.stringify(items), coverCharge, timestamp], function(err) {
        if (err) {
            console.error('Error creating order:', err);
            return res.status(500).json({ error: err.message });
        }

        const order = {
            id: this.lastID,
            tableNumber,
            customerName,
            items,
            coverCharge,
            timestamp,
            paid: false
        };

        // Print tickets if printer service is available
        if (printerService) {
            try {
                // Check if order has hookah items
                const hookahItems = printerService.extractHookahItems(order);
                if (hookahItems.length > 0) {
                    console.log('Printing hookah ticket for order:', order.id);
                    printerService.printHookahTicket(order)
                        .then(success => {
                            if (success) {
                                console.log('Hookah ticket printed successfully');
                            } else {
                                console.warn('Failed to print hookah ticket');
                            }
                        })
                        .catch(error => {
                            console.error('Error printing hookah ticket:', error);
                        });
                }

                // Check if order has food/drink items
                const foodItems = printerService.extractFoodItems(order);
                if (foodItems.length > 0) {
                    console.log('Printing kitchen ticket for order:', order.id);
                    printerService.printKitchenTicket(order)
                        .then(success => {
                            if (success) {
                                console.log('Kitchen ticket printed successfully');
                            } else {
                                console.warn('Failed to print kitchen ticket');
                            }
                        })
                        .catch(error => {
                            console.error('Error printing kitchen ticket:', error);
                        });
                }
            } catch (error) {
                console.error('Error printing tickets:', error);
            }
        }

        res.status(201).json(order);
    });
});

// Get all unpaid orders
app.get('/orders', (req, res) => {
    // Check if we're filtering by table and customer
    const tableNumber = req.query.tableNumber;
    const customerName = req.query.customerName;

    if (tableNumber && customerName) {
        // Get orders for specific table and customer
        db.all(
            `SELECT * FROM orders WHERE tableNumber = ? AND customerName = ? ORDER BY timestamp DESC`,
            [tableNumber, customerName],
            (err, rows) => {
                if (err) {
                    console.error('Error fetching orders by table and customer:', err);
                    return res.status(500).json({ success: false, error: err.message });
                }

                // Parse items JSON for each order
                const orders = rows.map(order => {
                    try {
                        if (order.items && typeof order.items === 'string') {
                            order.items = JSON.parse(order.items);
                        }
                        return order;
                    } catch (e) {
                        console.error('Error parsing items for order:', order.id, e);
                        order.items = [];
                        return order;
                    }
                });

                res.json({ success: true, orders });
            }
        );
        return;
    }

    // Get the showPaid parameter from the query string (default to false)
    const showPaid = req.query.showPaid === 'true';

    // If showPaid is true, return all orders, otherwise only return unpaid orders
    const sql = showPaid
        ? `SELECT * FROM orders ORDER BY timestamp DESC`
        : `SELECT * FROM orders WHERE paid = 0 ORDER BY timestamp DESC`;
    db.all(sql, [], (err, rows) => {
        if (err) return res.status(500).json({ error: err.message });

        const orders = rows.map(order => {
            try {
                let timestamp;
                if (order.timestamp) {
                    const parsedDate = new Date(order.timestamp);
                    if (!isNaN(parsedDate.getTime())) {
                        timestamp = parsedDate.toISOString();
                    } else {
                        timestamp = new Date().toISOString();
                        db.run('UPDATE orders SET timestamp = ? WHERE id = ?', [timestamp, order.id]);
                    }
                } else {
                    timestamp = new Date().toISOString();
                    db.run('UPDATE orders SET timestamp = ? WHERE id = ?', [timestamp, order.id]);
                }

                let items = [];
                try {
                    items = JSON.parse(order.items);
                    items = items.map(item => ({
                        name: item.name || '',
                        price: parseFloat(item.price) || 0,
                        notes: item.notes || ''
                    }));
                } catch (e) {
                    console.error('Error parsing items:', e);
                }

                let kitchenStatus = null;
                try {
                    // Check if the kitchen status is a valid JSON string
                    if (order.kitchenStatus && order.kitchenStatus.startsWith('{')) {
                        kitchenStatus = JSON.parse(order.kitchenStatus);
                    } else {
                        // If it's not JSON, use it as is
                        kitchenStatus = order.kitchenStatus;
                    }
                } catch (e) {
                    console.error('Error parsing kitchen status:', e);
                    // If parsing fails, use the raw value
                    kitchenStatus = order.kitchenStatus;
                }

                return {
                    id: order.id,
                    tableNumber: order.tableNumber,
                    customerName: order.customerName,
                    items: items,
                    status: order.status,
                    kitchenStatus: kitchenStatus,
                    timestamp: timestamp,
                    paid: Boolean(order.paid)
                };
            } catch (e) {
                console.error('Error parsing order:', e);
                return null;
            }
        }).filter(order => order !== null);

        res.status(200).json(orders);
    });
});

// Get specific order details
app.get('/api/order/:tableNumber/:customerName', (req, res) => {
    const { tableNumber, customerName } = req.params;

    db.get(
        `SELECT * FROM orders WHERE tableNumber = ? AND customerName = ? AND paid = 0`,
        [tableNumber, customerName],
        (err, order) => {
            if (err) {
                return res.status(500).json({ error: err.message });
            }
            if (!order) {
                return res.status(404).json({ error: 'Order not found' });
            }

            try {
                order.items = JSON.parse(order.items);
                res.json(order);
            } catch (error) {
                res.status(500).json({ error: 'Error parsing order items' });
            }
        }
    );
});

// Update kitchen status
app.put('/orders/:id/kitchen', (req, res) => {
    const { id } = req.params;
    const { status, type } = req.body;

    if (!status || !type) {
        return res.status(400).json({ error: 'Missing status or type' });
    }

    db.get('SELECT kitchenStatus FROM orders WHERE id = ?', [id], (err, row) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }

        if (!row) {
            return res.status(404).json({ error: 'Order not found' });
        }

        let currentStatus = {};
        try {
            // Check if the kitchen status is a valid JSON string
            if (row.kitchenStatus && row.kitchenStatus.startsWith('{')) {
                currentStatus = JSON.parse(row.kitchenStatus);
            } else if (row.kitchenStatus) {
                // If it's not JSON but has a value, create an object with the existing type
                currentStatus = { [type]: row.kitchenStatus };
            }
        } catch (e) {
            console.error('Error parsing kitchen status:', e);
            // If parsing fails, create a new object with the existing type
            if (row.kitchenStatus) {
                currentStatus = { [type]: row.kitchenStatus };
            }
        }

        currentStatus[type] = status;
        const newStatus = JSON.stringify(currentStatus);

        db.run(
            `UPDATE orders SET kitchenStatus = ? WHERE id = ?`,
            [newStatus, id],
            function(err) {
                if (err) {
                    return res.status(500).json({ error: err.message });
                }
                res.status(200).json({
                    message: 'Kitchen status updated',
                    kitchenStatus: currentStatus
                });
            }
        );
    });
});

// Update order by ID
app.put('/orders/:id', (req, res) => {
    const { id } = req.params;
    const { items, tableNumber, customerName } = req.body;

    // Validate required fields
    if (!items) {
        return res.status(400).json({ error: 'Items are required' });
    }

    // Make sure items is a string (JSON)
    const itemsJson = typeof items === 'string' ? items : JSON.stringify(items);

    db.run(
        `UPDATE orders SET items = ? WHERE id = ?`,
        [itemsJson, id],
        function(err) {
            if (err) {
                console.error('Error updating order:', err);
                return res.status(500).json({ error: err.message });
            }

            if (this.changes === 0) {
                return res.status(404).json({ error: 'Order not found' });
            }

            res.status(200).json({
                message: 'Order updated successfully',
                id: id
            });
        }
    );
});

// Mark order as paid
app.put('/orders/:tableNumber/:customerName', (req, res) => {
    const { tableNumber, customerName } = req.params;
    db.run(
        `UPDATE orders SET paid = 1 WHERE tableNumber = ? AND customerName = ?`,
        [tableNumber, customerName],
        function(err) {
            if (err) return res.status(500).json({ error: err.message });
            res.status(200).json({ message: 'Order marked as paid' });
        }
    );
});

// Clear table orders
app.delete('/orders/table/:tableNumber', (req, res) => {
    const { tableNumber } = req.params;
    db.run(
        `DELETE FROM orders WHERE tableNumber = ?`,
        [tableNumber],
        function(err) {
            if (err) return res.status(500).json({ error: err.message });
            res.status(200).json({ message: 'Table orders cleared' });
        }
    );
});

// Process payment
app.post('/process-payment', (req, res) => {
    const { orderData, paymentMethod } = req.body;
    console.log('Processing payment:', orderData.tableNumber, orderData.customerName, paymentMethod);
    console.log('Payment data received:', orderData);

    // Update the order in the database
    db.run(
        `UPDATE orders SET paid = 1, paymentMethod = ? WHERE tableNumber = ? AND customerName = ?`,
        [paymentMethod, orderData.tableNumber, orderData.customerName],
        function(err) {
            if (err) {
                console.error('Error updating order:', err);
                return res.status(500).json({ error: err.message });
            }

            // Check if this is a table payment (combined bill) or individual bill payment
            const isTablePayment = orderData.customerName === `Table ${orderData.tableNumber}`;

            if (isTablePayment) {
                // For table payments, get all orders for the table and archive each one
                db.all(
                    `SELECT * FROM orders WHERE tableNumber = ?`,
                    [orderData.tableNumber],
                    async (err, orders) => {
                        if (err) {
                            console.error('Error retrieving orders for archiving:', err);
                            // Continue with the response even if archiving fails
                        } else if (orders && orders.length > 0) {
                            console.log(`Found ${orders.length} orders for table ${orderData.tableNumber}`);

                            // Create a combined order for the bill archive with all items from all orders
                            // First, collect all items from all orders at this table
                            const allItems = [];

                            // Process each order's items
                            orders.forEach(order => {
                                let orderItems = [];
                                try {
                                    // Parse items if they're stored as a string
                                    if (order.items && typeof order.items === 'string') {
                                        orderItems = JSON.parse(order.items);
                                    } else if (order.items && Array.isArray(order.items)) {
                                        orderItems = order.items;
                                    }

                                    // Add each item to the combined items list
                                    orderItems.forEach(item => {
                                        // Find if this item already exists in the combined list
                                        const existingItem = allItems.find(i =>
                                            i.name === item.name &&
                                            Math.abs(parseFloat(i.price) - parseFloat(item.price)) < 0.01
                                        );

                                        if (existingItem) {
                                            // If item exists, increase quantity
                                            existingItem.quantity = (existingItem.quantity || 1) + (item.quantity || 1);
                                        } else {
                                            // Otherwise add as new item
                                            allItems.push({
                                                name: item.name || '',
                                                price: parseFloat(item.price) || 0,
                                                quantity: parseInt(item.quantity) || 1,
                                                notes: item.notes || ''
                                            });
                                        }
                                    });
                                } catch (e) {
                                    console.error('Error processing items for order:', order.id, e);
                                }
                            });

                            console.log(`Combined ${allItems.length} items from ${orders.length} orders for table ${orderData.tableNumber}`);

                            // Create the combined order with all items
                            const combinedOrder = {
                                id: `table-${orderData.tableNumber}-${Date.now()}`,
                                tableNumber: orderData.tableNumber,
                                customerName: `Table ${orderData.tableNumber} (Combined)`,
                                timestamp: new Date().toISOString(),
                                items: allItems.length > 0 ? allItems : (orderData.items || [])
                            };

                            // Calculate subtotal from the combined items
                            const subtotal = combinedOrder.items.reduce((sum, item) => {
                                const price = parseFloat(item.price) || 0;
                                const quantity = parseInt(item.quantity) || 1;
                                return sum + (price * quantity);
                            }, 0);

                            console.log(`Calculated subtotal: $${subtotal.toFixed(2)} from ${combinedOrder.items.length} items`);

                            // For cash payments, no taxes are applied
                            let taxRate = 0;
                            let taxAmount = 0;
                            let gstRate = 0;
                            let gstAmount = 0;
                            let pstRate = 0;
                            let pstAmount = 0;

                            // For card payments, get tax information from the payment data or calculate it
                            if (paymentMethod !== 'cash') {
                                taxRate = orderData.taxRate || 0.07; // Default 7% if not provided
                                taxAmount = orderData.taxAmount || (subtotal * taxRate);
                                gstRate = orderData.gstRate || 0;
                                gstAmount = orderData.gstAmount || 0;
                                pstRate = orderData.pstRate || 0;
                                pstAmount = orderData.pstAmount || 0;
                            }

                            console.log('Tax calculation for combined order:', {
                                subtotal,
                                taxRate,
                                taxAmount,
                                gstRate,
                                gstAmount,
                                pstRate,
                                pstAmount
                            });

                            const tip = orderData.tip || 0;
                            // For cash payments, total is just subtotal + tip (no taxes)
                            // For card payments, total includes taxes
                            const total = paymentMethod === 'cash'
                                ? (orderData.total || (subtotal + tip))
                                : (orderData.total || (subtotal + taxAmount + tip));

                            // Archive the combined bill
                            const paymentDetails = {
                                paymentMethod: paymentMethod,
                                tip: tip,
                                subtotal: subtotal,
                                tax: taxAmount,
                                taxRate: taxRate,
                                gst: gstAmount,
                                gstRate: gstRate,
                                pst: pstAmount,
                                pstRate: pstRate,
                                total: total,
                                transactionId: orderData.transactionId || null
                            };

                            try {
                                const archiveResult = await billArchiveService.archiveBill(combinedOrder, paymentDetails);
                                console.log('Combined bill archived:', archiveResult);
                            } catch (archiveError) {
                                console.error('Error archiving combined bill:', archiveError);
                            }
                        }
                    }
                );
            } else {
                // For individual bill payments, get the specific order
                db.get(
                    `SELECT * FROM orders WHERE tableNumber = ? AND customerName = ?`,
                    [orderData.tableNumber, orderData.customerName],
                    async (err, order) => {
                        if (err) {
                            console.error('Error retrieving order for archiving:', err);
                            // Continue with the response even if archiving fails
                        } else if (order) {
                            try {
                                // Parse items if needed
                                if (order.items && typeof order.items === 'string') {
                                    order.items = JSON.parse(order.items);
                                }

                                // Ensure items have the correct format
                                if (order.items && Array.isArray(order.items)) {
                                    order.items = order.items.map(item => ({
                                        name: item.name || '',
                                        price: parseFloat(item.price) || 0,
                                        quantity: parseInt(item.quantity) || 1,
                                        notes: item.notes || ''
                                    }));
                                }

                                // Calculate total
                                const subtotal = order.items.reduce((sum, item) => {
                                    const price = parseFloat(item.price) || 0;
                                    const quantity = parseInt(item.quantity) || 1;
                                    return sum + (price * quantity);
                                }, 0);

                                // For cash payments, no taxes are applied
                                let taxRate = 0;
                                let taxAmount = 0;
                                let gstRate = 0;
                                let gstAmount = 0;
                                let pstRate = 0;
                                let pstAmount = 0;

                                // For card payments, get tax information from the payment data or calculate it
                                if (paymentMethod !== 'cash') {
                                    taxRate = orderData.taxRate || 0.07; // Default 7% if not provided
                                    taxAmount = orderData.taxAmount || (subtotal * taxRate);
                                    gstRate = orderData.gstRate || 0;
                                    gstAmount = orderData.gstAmount || 0;
                                    pstRate = orderData.pstRate || 0;
                                    pstAmount = orderData.pstAmount || 0;
                                }

                                console.log('Tax calculation for individual order:', {
                                    subtotal,
                                    taxRate,
                                    taxAmount,
                                    gstRate,
                                    gstAmount,
                                    pstRate,
                                    pstAmount
                                });

                                const tip = orderData.tip || 0;
                                // For cash payments, total is just subtotal + tip (no taxes)
                                // For card payments, total includes taxes
                                const total = paymentMethod === 'cash'
                                    ? (orderData.total || (subtotal + tip))
                                    : (orderData.total || (subtotal + taxAmount + tip));

                                // Archive the bill
                                const paymentDetails = {
                                    paymentMethod: paymentMethod,
                                    tip: tip,
                                    subtotal: subtotal,
                                    tax: taxAmount,
                                    taxRate: taxRate,
                                    gst: gstAmount,
                                    gstRate: gstRate,
                                    pst: pstAmount,
                                    pstRate: pstRate,
                                    total: total,
                                    transactionId: orderData.transactionId || null
                                };

                                const archiveResult = await billArchiveService.archiveBill(order, paymentDetails);
                                console.log('Individual bill archived:', archiveResult);
                            } catch (archiveError) {
                                console.error('Error archiving individual bill:', archiveError);
                            }
                        }
                    }
                );
            }

            // Delete orders after payment is processed
            // For table payments, delete all orders for the table
            // For individual bill payments, only delete the specific bill
            const deleteQuery = isTablePayment
                ? `DELETE FROM orders WHERE tableNumber = ?`
                : `DELETE FROM orders WHERE tableNumber = ? AND customerName = ?`;

            const deleteParams = isTablePayment
                ? [orderData.tableNumber]
                : [orderData.tableNumber, orderData.customerName];

            db.run(
                deleteQuery,
                deleteParams,
                function(deleteErr) {
                    if (deleteErr) {
                        console.error('Error deleting orders after payment:', deleteErr);
                        // Continue with the response even if deletion fails
                    } else {
                        if (isTablePayment) {
                            console.log(`Deleted all orders for table ${orderData.tableNumber} after payment`);
                        } else {
                            console.log(`Deleted bill for table ${orderData.tableNumber}, customer ${orderData.customerName} after payment`);
                        }
                    }

                    // Send response
                    console.log(`Payment processed for table ${orderData.tableNumber}, customer ${orderData.customerName}`);
                    res.status(200).json({
                        success: true,
                        message: 'Payment processed successfully',
                        tableNumber: orderData.tableNumber,
                        customerName: orderData.customerName,
                        paymentMethod: paymentMethod,
                        ordersDeleted: !deleteErr
                    });
                }
            );
        }
    );
});


// Backup endpoint
app.post('/api/backup', async (req, res) => {
    try {
        console.log('Creating backup...');
        const backupType = req.query.type || 'manual';

        // Use enhanced backup service
        const backupPath = await enhancedBackupService.createFullBackup(backupType);
        console.log('Backup created at:', backupPath);

        res.json({
            success: true,
            message: 'Backup created successfully',
            backupPath: backupPath,
            backupType: backupType
        });
    } catch (error) {
        console.error('Backup error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get backup information
app.get('/api/backups', (req, res) => {
    console.log('Fetching backup information...');
    try {
        // Use enhanced backup service to get backups
        const backups = enhancedBackupService.getBackups();
        console.log(`Found ${backups.length} backups`);
        res.json(backups);
    } catch (error) {
        console.error('Error fetching backups:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// API endpoint to get details of a specific backup
app.get('/api/backups/:name', (req, res) => {
    try {
        const backupName = req.params.name;
        const backupType = req.query.type || 'manual';

        console.log(`Getting details for backup: ${backupName}, type: ${backupType}`);

        const details = enhancedBackupService.getBackupDetails(backupName, backupType);

        if (details) {
            res.json(details);
        } else {
            res.status(404).json({
                success: false,
                error: `Backup ${backupName} not found`
            });
        }
    } catch (error) {
        console.error('Error getting backup details:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get backup details'
        });
    }
});

// API endpoint to get order data from a specific backup
app.get('/api/backups/:name/orders', async (req, res) => {
    try {
        const backupName = req.params.name;
        const backupType = req.query.type || 'manual';

        console.log(`Getting order data from backup: ${backupName}, type: ${backupType}`);

        const result = await enhancedBackupService.getBackupOrderData(backupName, backupType);

        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json({
                success: false,
                error: result.error || `Failed to get order data from backup ${backupName}`
            });
        }
    } catch (error) {
        console.error('Error getting order data from backup:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get order data from backup'
        });
    }
});

// Printer API Endpoints

// Get printer settings
app.get('/api/printer-settings', (req, res) => {
    try {
        if (!printerService) {
            return res.status(500).json({
                success: false,
                error: 'Printer service not initialized'
            });
        }

        res.json({
            success: true,
            config: printerService.config
        });
    } catch (error) {
        console.error('Error getting printer settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get printer settings'
        });
    }
});

// Update printer settings
app.post('/api/printer-settings', (req, res) => {
    try {
        if (!printerService) {
            return res.status(500).json({
                success: false,
                error: 'Printer service not initialized'
            });
        }

        const settings = req.body;
        printerService.updateConfig(settings);

        // Save settings to file
        fs.writeFileSync(printerSettingsPath, JSON.stringify(settings, null, 2));

        res.json({
            success: true,
            message: 'Printer settings updated successfully'
        });
    } catch (error) {
        console.error('Error updating printer settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update printer settings'
        });
    }
});

// List available USB printers
app.get('/api/printer-list', (req, res) => {
    try {
        if (!printerService) {
            return res.status(500).json({
                success: false,
                error: 'Printer service not initialized'
            });
        }

        // This is a mock implementation since we can't actually detect printers in this environment
        // In a real implementation, this would use the escpos.USB.findPrinter() method
        const mockPrinters = [
            { vendorId: '0x0416', productId: '0x5011', manufacturer: 'Thermal Printer 1' },
            { vendorId: '0x1504', productId: '0x0006', manufacturer: 'Thermal Printer 2' },
            { vendorId: '0x0483', productId: '0x5720', manufacturer: 'Receipt Printer' }
        ];

        res.json({
            success: true,
            printers: mockPrinters
        });
    } catch (error) {
        console.error('Error listing printers:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to list printers'
        });
    }
});

// Test printer
app.get('/api/printer-test', (req, res) => {
    try {
        if (!printerService) {
            return res.status(500).json({
                success: false,
                error: 'Printer service not initialized'
            });
        }

        const type = req.query.type || 'kitchen';

        // In a real implementation, this would call printerService.testPrinter(type)
        // Since we can't actually print in this environment, we'll simulate success

        res.json({
            success: true,
            message: `${type} printer test completed successfully`
        });
    } catch (error) {
        console.error('Error testing printer:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to test printer'
        });
    }
});

// Get backup storage information
app.get('/api/backup-storage', (req, res) => {
    console.log('Fetching backup storage information...');
    try {
        // Calculate next scheduled backup time
        let nextScheduledBackup = null;
        if (enhancedBackupService.options.scheduleDailyBackup) {
            const now = new Date();
            const nextBackup = new Date();
            nextBackup.setHours(enhancedBackupService.options.dailyBackupHour, 0, 0, 0);

            // If the scheduled time has already passed today, schedule for tomorrow
            if (now > nextBackup) {
                nextBackup.setDate(nextBackup.getDate() + 1);
            }

            nextScheduledBackup = nextBackup.toISOString();
        }

        // Get storage information from enhanced backup service
        const storageInfo = {
            primary: 'Local',
            external: enhancedBackupService.options.externalBackupDir ? 'External Drive' : null,
            cloud: null, // For future MyCloud integration
            nextScheduledBackup: nextScheduledBackup,
            backupSettings: {
                enabled: enhancedBackupService.options.scheduleDailyBackup,
                hour: enhancedBackupService.options.dailyBackupHour
            }
        };

        res.json({
            success: true,
            storage: storageInfo
        });
    } catch (error) {
        console.error('Error fetching backup storage info:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Update backup schedule
app.post('/api/backup-schedule', (req, res) => {
    try {
        const { enabled, hour, minute, ampm } = req.body;

        // Convert hour to 24-hour format if needed
        let hourValue = parseInt(hour);
        if (ampm === 'pm' && hourValue < 12) {
            hourValue += 12;
        } else if (ampm === 'am' && hourValue === 12) {
            hourValue = 0;
        }

        console.log(`Updating backup schedule: enabled=${enabled}, hour=${hourValue}`);

        // Update the backup schedule
        const nextBackup = enhancedBackupService.updateBackupSchedule({
            enabled: enabled,
            hour: hourValue
        });

        res.json({
            success: true,
            message: 'Backup schedule updated successfully',
            nextScheduledBackup: nextBackup ? nextBackup.toISOString() : null
        });
    } catch (error) {
        console.error('Error updating backup schedule:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get backup information (legacy endpoint)
app.get('/api/backups-legacy', (req, res) => {
    console.log('Fetching legacy backup information...');
    const backupDir = path.join(__dirname, 'backups');
    const backups = [];

    if (fs.existsSync(backupDir)) {
        console.log('Backups directory exists');
        const backupFolders = fs.readdirSync(backupDir)
            .filter(folder => folder.startsWith('backup-'))
            .sort((a, b) => b.localeCompare(a)); // Sort by date, newest first

        console.log('Found backup folders:', backupFolders);

        backupFolders.forEach(folder => {
            const manifestPath = path.join(backupDir, folder, 'manifest.json');
            if (fs.existsSync(manifestPath)) {
                try {
                    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
                    const backupPath = path.join(backupDir, folder);
                    let size = 0;

                    // Calculate total size of backup
                    const files = fs.readdirSync(backupPath, { withFileTypes: true });
                    for (const file of files) {
                        const filePath = path.join(backupPath, file.name);
                        if (file.isDirectory()) {
                            const subFiles = fs.readdirSync(filePath, { withFileTypes: true });
                            for (const subFile of subFiles) {
                                const subFilePath = path.join(filePath, subFile.name);
                                size += fs.statSync(subFilePath).size;
                            }
                        } else {
                            size += fs.statSync(filePath).size;
                        }
                    }

                    backups.push({
                        name: folder,
                        timestamp: manifest.timestamp,
                        fileCount: manifest.files.length,
                        hash: manifest.hash,
                        size: size
                    });
                } catch (error) {
                    console.error(`Error reading manifest for ${folder}:`, error);
                }
            }
        });
    } else {
        console.log('Backups directory does not exist');
    }

    console.log('Sending backup information:', backups);
    res.json(backups);
});

// Get detailed backup information
app.get('/api/backups/:backupName', (req, res) => {
    const { backupName } = req.params;
    const backupType = req.query.type || 'manual';

    console.log(`Fetching details for backup: ${backupName}, type: ${backupType}`);

    try {
        // Direct implementation without relying on the enhanced backup service
        const backupPath = path.join(__dirname, 'backups', backupType, backupName);
        console.log(`Looking for backup at: ${backupPath}`);

        if (!fs.existsSync(backupPath)) {
            console.log(`Backup directory not found: ${backupPath}`);
            return res.status(404).json({
                success: false,
                error: `Backup ${backupName} not found`
            });
        }

        // Get basic information about the backup
        const stats = fs.statSync(backupPath);
        const manifestPath = path.join(backupPath, 'manifest.json');

        let files = [];
        let timestamp = stats.mtime.toISOString();
        let hash = '';

        // Try to read the manifest if it exists
        if (fs.existsSync(manifestPath)) {
            try {
                const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
                files = manifest.files || [];
                timestamp = manifest.timestamp || timestamp;
                hash = manifest.hash || '';
            } catch (manifestError) {
                console.error(`Error reading manifest for ${backupName}:`, manifestError);
                // Continue with default values if manifest can't be read
            }
        } else {
            console.log(`No manifest found for ${backupName}, using directory listing`);
            // If no manifest, try to list files in the directory
            try {
                const getFilesRecursively = (dir) => {
                    let results = [];
                    const list = fs.readdirSync(dir);

                    list.forEach(file => {
                        const fullPath = path.join(dir, file);
                        const relativePath = path.relative(backupPath, fullPath);

                        if (fs.statSync(fullPath).isDirectory()) {
                            results = results.concat(getFilesRecursively(fullPath));
                        } else {
                            results.push(relativePath);
                        }
                    });

                    return results;
                };

                files = getFilesRecursively(backupPath);
            } catch (listError) {
                console.error(`Error listing files for ${backupName}:`, listError);
                // Continue with empty files array
            }
        }

        // Calculate size
        let size = 0;
        try {
            const calculateSize = (dir) => {
                let total = 0;
                const items = fs.readdirSync(dir);

                for (const item of items) {
                    const itemPath = path.join(dir, item);
                    const itemStats = fs.statSync(itemPath);

                    if (itemStats.isDirectory()) {
                        total += calculateSize(itemPath);
                    } else {
                        total += itemStats.size;
                    }
                }

                return total;
            };

            size = calculateSize(backupPath);
        } catch (sizeError) {
            console.error(`Error calculating size for ${backupName}:`, sizeError);
            // Continue with size = 0
        }

        // Return the backup details
        const backupDetails = {
            name: backupName,
            path: backupPath,
            timestamp: timestamp,
            type: backupType,
            files: files,
            size: size,
            hash: hash
        };

        console.log(`Successfully retrieved details for ${backupName}`);
        res.json(backupDetails);
    } catch (error) {
        console.error('Error fetching backup details:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Restore from backup
app.post('/api/restore/:backupName', async (req, res) => {
    const { backupName } = req.params;
    const backupType = req.query.type || 'manual';

    try {
        // Use enhanced backup service to restore
        const result = await enhancedBackupService.restoreFromBackup(backupName, backupType);
        res.json({
            success: true,
            message: `Restored from backup ${backupName}`,
            details: result
        });
    } catch (error) {
        console.error('Error restoring from backup:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Legacy backup detail endpoint
app.get('/api/backups-legacy/:backupName', (req, res) => {
    const { backupName } = req.params;
    const backupPath = path.join(__dirname, 'backups', backupName);
    const manifestPath = path.join(backupPath, 'manifest.json');

    if (!fs.existsSync(manifestPath)) {
        return res.status(404).json({ error: 'Backup not found' });
    }

    try {
        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        const size = calculateBackupSize(backupPath);

        res.json({
            name: backupName,
            timestamp: manifest.timestamp,
            fileCount: manifest.files.length,
            size: size,
            files: manifest.files,
            hash: manifest.hash
        });
    } catch (error) {
        console.error('Error reading backup details:', error);
        res.status(500).json({ error: 'Failed to read backup details' });
    }
});

// Helper function to calculate backup size
function calculateBackupSize(dir) {
    let size = 0;
    const files = fs.readdirSync(dir, { withFileTypes: true });

    for (const file of files) {
        const fullPath = path.join(dir, file.name);
        if (file.isDirectory()) {
            size += calculateBackupSize(fullPath);
        } else {
            size += fs.statSync(fullPath).size;
        }
    }

    return size;
}

// Add route for main page
app.get('/', (req, res) => {
    res.redirect('/pos');
});

// Add route for POS page with cache control
app.get('/pos', (req, res) => {
    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.sendFile(path.join(__dirname, 'pos', 'index.html'));
});

// Items API Endpoints

// Get all items
app.get('/api/items', (req, res) => {
    try {
        const searchTerm = req.query.search ? req.query.search.toLowerCase() : '';
        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Flatten the items from all categories
        const allItems = [];

        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            const categoryName = categoryKey;

            category.items.forEach(item => {
                // Add category information to each item
                const itemWithCategory = {
                    ...item,
                    category: categoryName,
                    categoryDisplayName: category.name
                };

                // If there's a search term, filter items
                if (!searchTerm ||
                    item.name.toLowerCase().includes(searchTerm) ||
                    category.name.toLowerCase().includes(searchTerm) ||
                    (item.description && item.description.toLowerCase().includes(searchTerm))) {
                    allItems.push(itemWithCategory);
                }
            });
        });

        res.json(allItems);
    } catch (error) {
        console.error('Error reading items:', error);
        res.status(500).json({ error: 'Failed to read items' });
    }
});

// Get a specific item
app.get('/api/items/:id', (req, res) => {
    try {
        const itemId = req.params.id;
        console.log('Server: Looking for item with ID:', itemId);

        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Search for the item in all categories
        let foundItem = null;
        let foundCategory = null;

        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            console.log(`Server: Searching in category ${categoryKey} with ${category.items.length} items`);

            // Log some sample IDs from this category
            if (category.items.length > 0) {
                console.log('Server: Sample item IDs in this category:',
                    category.items.slice(0, 3).map(item => item.id || 'no-id'));
            }

            const item = category.items.find(item => {
                const matches = (item.id === itemId) || (item.name === itemId);
                if (matches) {
                    console.log('Server: Found matching item:', item);
                }
                return matches;
            });

            if (item) {
                foundItem = { ...item, category: categoryKey, categoryDisplayName: category.name };
                foundCategory = categoryKey;
                console.log('Server: Item found in category:', categoryKey);
            }
        });

        if (foundItem) {
            console.log('Server: Returning found item:', foundItem);
            res.json(foundItem);
        } else {
            console.log('Server: Item not found with ID:', itemId);
            res.status(404).json({ error: 'Item not found' });
        }
    } catch (error) {
        console.error('Server: Error reading item:', error);
        res.status(500).json({ error: 'Failed to read item' });
    }
});

// Add a new item
app.post('/api/items', (req, res) => {
    try {
        const newItem = req.body;
        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Validate required fields
        if (!newItem.name || !newItem.category || newItem.price === undefined) {
            return res.status(400).json({ error: 'Name, category, and price are required' });
        }

        // Check if the category exists
        if (!itemsData.categories[newItem.category]) {
            return res.status(400).json({ error: 'Category does not exist' });
        }

        // Generate a unique ID
        const categoryPrefix = newItem.category.charAt(0);
        const existingIds = [];

        Object.keys(itemsData.categories).forEach(categoryKey => {
            itemsData.categories[categoryKey].items.forEach(item => {
                if (item.id) {
                    existingIds.push(item.id);
                }
            });
        });

        let newId;
        let counter = 1;
        do {
            newId = `${categoryPrefix}${counter}`;
            counter++;
        } while (existingIds.includes(newId));

        // Create the item object
        const itemToAdd = {
            id: newId,
            name: newItem.name,
            price: parseFloat(newItem.price),
            description: newItem.description || ''
        };

        // Add the item to the category
        itemsData.categories[newItem.category].items.push(itemToAdd);

        // Save the updated data
        fs.writeFileSync('items.json', JSON.stringify(itemsData, null, 2));

        res.status(201).json({
            ...itemToAdd,
            category: newItem.category,
            categoryDisplayName: itemsData.categories[newItem.category].name
        });
    } catch (error) {
        console.error('Error adding item:', error);
        res.status(500).json({ error: 'Failed to add item' });
    }
});

// Update an item
app.put('/api/items/:id', (req, res) => {
    try {
        const itemId = req.params.id;
        console.log('Server: Updating item with ID:', itemId);

        const updatedItem = req.body;
        console.log('Server: Update data received:', updatedItem);

        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Validate required fields
        if (!updatedItem.name || updatedItem.price === undefined) {
            console.log('Server: Validation failed - missing name or price');
            return res.status(400).json({ error: 'Name and price are required' });
        }

        // Find the item in all categories
        let found = false;
        let originalCategory = null;
        let originalItemIndex = -1;

        // First, find the item and its original category
        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            console.log(`Server: Searching for item to update in category ${categoryKey}`);

            // Try to find by ID first
            let itemIndex = category.items.findIndex(item => item.id === itemId);

            // If not found by ID, try by name
            if (itemIndex === -1) {
                itemIndex = category.items.findIndex(item => item.name === itemId);
                if (itemIndex !== -1) {
                    console.log(`Server: Found item by name in category ${categoryKey} at index ${itemIndex}`);
                }
            } else {
                console.log(`Server: Found item by ID in category ${categoryKey} at index ${itemIndex}`);
            }

            if (itemIndex !== -1) {
                originalCategory = categoryKey;
                originalItemIndex = itemIndex;
                found = true;
            }
        });

        if (!found) {
            console.log('Server: Item not found for update with ID:', itemId);
            return res.status(404).json({ error: 'Item not found' });
        }

        console.log(`Server: Item found in category ${originalCategory} at index ${originalItemIndex}`);

        // If category is changing, remove from old and add to new
        if (updatedItem.category && updatedItem.category !== originalCategory) {
            // Check if the new category exists
            if (!itemsData.categories[updatedItem.category]) {
                return res.status(400).json({ error: 'New category does not exist' });
            }

            // Find and remove the item from the original category
            const itemIndex = itemsData.categories[originalCategory].items.findIndex(
                item => item.id === itemId || (item.name === itemId)
            );

            if (itemIndex !== -1) {
                const removedItem = itemsData.categories[originalCategory].items.splice(itemIndex, 1)[0];

                // Update the item properties
                const updatedItemObj = {
                    id: removedItem.id,
                    name: updatedItem.name,
                    price: parseFloat(updatedItem.price),
                    description: updatedItem.description || removedItem.description || ''
                };

                // Add to the new category
                itemsData.categories[updatedItem.category].items.push(updatedItemObj);
            }
        } else {
            // Just update the item in its current category
            const itemIndex = itemsData.categories[originalCategory].items.findIndex(
                item => item.id === itemId || (item.name === itemId)
            );

            if (itemIndex !== -1) {
                itemsData.categories[originalCategory].items[itemIndex] = {
                    id: itemsData.categories[originalCategory].items[itemIndex].id,
                    name: updatedItem.name,
                    price: parseFloat(updatedItem.price),
                    description: updatedItem.description || itemsData.categories[originalCategory].items[itemIndex].description || ''
                };
            }
        }

        // Save the updated data
        fs.writeFileSync('items.json', JSON.stringify(itemsData, null, 2));

        // Determine the final category for the response
        const finalCategory = updatedItem.category || originalCategory;

        res.json({
            id: itemId,
            name: updatedItem.name,
            price: parseFloat(updatedItem.price),
            description: updatedItem.description || '',
            category: finalCategory,
            categoryDisplayName: itemsData.categories[finalCategory].name
        });
    } catch (error) {
        console.error('Error updating item:', error);
        res.status(500).json({ error: 'Failed to update item' });
    }
});

// Delete an item
app.delete('/api/items/:id', (req, res) => {
    try {
        const itemId = req.params.id;
        console.log('Server: Deleting item with ID:', itemId);

        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        // Find and remove the item from its category
        let found = false;
        let deletedFrom = null;

        Object.keys(itemsData.categories).forEach(categoryKey => {
            const category = itemsData.categories[categoryKey];
            console.log(`Server: Searching for item to delete in category ${categoryKey}`);

            // Try to find by ID first
            let itemIndex = category.items.findIndex(item => item.id === itemId);

            // If not found by ID, try by name
            if (itemIndex === -1) {
                itemIndex = category.items.findIndex(item => item.name === itemId);
                if (itemIndex !== -1) {
                    console.log(`Server: Found item by name in category ${categoryKey} at index ${itemIndex}`);
                }
            } else {
                console.log(`Server: Found item by ID in category ${categoryKey} at index ${itemIndex}`);
            }

            if (itemIndex !== -1) {
                const deletedItem = category.items.splice(itemIndex, 1)[0];
                console.log(`Server: Deleted item from category ${categoryKey}:`, deletedItem);
                found = true;
                deletedFrom = categoryKey;
            }
        });

        if (!found) {
            console.log('Server: Item not found for deletion with ID:', itemId);
            return res.status(404).json({ error: 'Item not found' });
        }

        // Save the updated data
        fs.writeFileSync('items.json', JSON.stringify(itemsData, null, 2));
        console.log(`Server: Item successfully deleted from category ${deletedFrom}`);

        res.json({ message: 'Item deleted successfully' });
    } catch (error) {
        console.error('Server: Error deleting item:', error);
        res.status(500).json({ error: 'Failed to delete item' });
    }
});

// Get all categories
app.get('/api/categories', (req, res) => {
    try {
        const itemsData = JSON.parse(fs.readFileSync('items.json', 'utf8'));

        const categories = Object.keys(itemsData.categories).map(key => ({
            id: key,
            name: itemsData.categories[key].name
        }));

        res.json(categories);
    } catch (error) {
        console.error('Error reading categories:', error);
        res.status(500).json({ error: 'Failed to read categories' });
    }
});

// Get orders for reporting
app.get('/api/orders/report', (req, res) => {
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    console.log(`Fetching orders for report from ${startDate} to ${endDate}`);

    // Validate dates
    if (!startDate || !endDate) {
        return res.status(400).json({
            success: false,
            error: 'Start date and end date are required'
        });
    }

    // Add one day to end date to include the entire end date
    const endDateObj = new Date(endDate);
    endDateObj.setDate(endDateObj.getDate() + 1);
    const adjustedEndDate = endDateObj.toISOString().split('T')[0];

    // Query orders within date range
    const sql = `
        SELECT * FROM orders
        WHERE timestamp >= ? AND timestamp < ?
        ORDER BY timestamp DESC
    `;

    db.all(sql, [startDate, adjustedEndDate], (err, rows) => {
        if (err) {
            console.error('Error fetching orders for report:', err);
            return res.status(500).json({
                success: false,
                error: err.message
            });
        }

        console.log(`Found ${rows.length} orders for the report`);

        res.json({
            success: true,
            orders: rows
        });
    });
});

// Process payment
app.post('/process-payment', (req, res) => {
    const { orderData, paymentMethod } = req.body;

    // Generate a unique transaction ID
    const transactionId = generateTransactionId();

    // For both cash and card payments, update the order status
    const sql = `
        UPDATE orders
        SET paid = 1,
            status = 'completed',
            paymentMethod = ?,
            transactionId = ?
        WHERE tableNumber = ? AND customerName = ? AND (paid = 0 OR paymentMethod IS NULL)
    `;

    db.run(sql, [paymentMethod, transactionId, orderData.tableNumber, orderData.customerName], function(err) {
        if (err) {
            console.error('Error processing payment:', err);
            return res.status(500).json({ error: err.message });
        }

        if (this.changes === 0) {
            // Check if the order is already paid
            db.get(
                'SELECT paid, paymentMethod FROM orders WHERE tableNumber = ? AND customerName = ?',
                [orderData.tableNumber, orderData.customerName],
                (err, row) => {
                    if (err) {
                        return res.status(500).json({ error: err.message });
                    }
                    if (row && row.paid) {
                        return res.status(200).json({
                            message: 'Order already paid',
                            paymentMethod: row.paymentMethod
                        });
                    }
                    return res.status(404).json({ error: 'No orders found for this table and customer' });
                }
            );
            return;
        }

        res.status(200).json({
            success: true,
            message: 'Payment processed successfully',
            tableNumber: orderData.tableNumber,
            customerName: orderData.customerName,
            paymentMethod: paymentMethod,
            transactionId: transactionId
        });
    });
});

// Get orders from a specific backup
app.get('/backup/:backupName/orders', (req, res) => {
    const backupName = req.params.backupName;
    const backupType = req.query.type || 'manual';
    const backupPath = path.join(__dirname, 'backups', backupType, backupName);
    const backupDbPath = path.join(backupPath, 'orders.db');

    console.log('Loading orders from backup:', backupName);
    console.log('Backup path:', backupPath);
    console.log('Database path:', backupDbPath);

    if (!fs.existsSync(backupDbPath)) {
        console.error('Backup database not found at:', backupDbPath);
        return res.status(404).json({ error: 'Backup database not found' });
    }

    // Create a temporary connection to the backup database
    const backupDb = new sqlite3.Database(backupDbPath);

    // First check if the orders table exists
    backupDb.get("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'", (err, row) => {
        if (err) {
            console.error('Error checking for orders table:', err);
            backupDb.close();
            return res.status(500).json({ error: err.message });
        }

        if (!row) {
            console.error('Orders table not found in backup database');
            backupDb.close();
            return res.status(404).json({ error: 'Orders table not found in backup' });
        }

        // Check the schema of the orders table
        backupDb.all("PRAGMA table_info(orders)", (err, columns) => {
            if (err) {
                console.error('Error checking table schema:', err);
                backupDb.close();
                return res.status(500).json({ error: err.message });
            }

            console.log('Table schema:', columns);

            // Determine which columns exist
            const hasTotalColumn = columns.some(col => col.name === 'total');
            const hasTipColumn = columns.some(col => col.name === 'tip');

            // Build the query based on available columns
            let query = `
                SELECT id, tableNumber, customerName, items, paymentMethod, status, timestamp
            `;

            if (hasTotalColumn) {
                query += `, total`;
            }

            if (hasTipColumn) {
                query += `, tip`;
            }

            query += ` FROM orders ORDER BY timestamp DESC`;

            // Get all orders
            backupDb.all(query, (err, rows) => {
                if (err) {
                    console.error('Error fetching orders:', err);
                    backupDb.close();
                    return res.status(500).json({ error: err.message });
                }

                console.log(`Found ${rows.length} orders in backup`);

                // Process the results
                const orders = rows.map(row => {
                    console.log('Processing order:', row.id);
                    let items = [];
                    try {
                        items = JSON.parse(row.items);
                        console.log('Successfully parsed items for order:', row.id);
                    } catch (e) {
                        console.error('Error parsing items for order:', row.id, e);
                    }

                    // Calculate total from items if total column doesn't exist
                    let total = row.total;
                    if (total === undefined) {
                        total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                    }

                    return {
                        id: row.id,
                        tableNumber: row.tableNumber,
                        customerName: row.customerName,
                        paymentMethod: row.paymentMethod,
                        status: row.status,
                        total: total,
                        tip: row.tip || 0,
                        items: items,
                        timestamp: row.timestamp
                    };
                });

                console.log('Returning orders:', orders);
                backupDb.close();
                res.json({ orders });
            });
        });
    });
});

// Generate QR code for transaction ID
app.get('/api/qrcode/:transactionId', async (req, res) => {
    try {
        const { transactionId } = req.params;

        if (!transactionId) {
            return res.status(400).json({
                success: false,
                error: 'Transaction ID is required'
            });
        }

        // Generate QR code as data URL
        const qrCodeDataUrl = await QRCode.toDataURL(transactionId, {
            width: 200,
            margin: 1,
            color: {
                dark: '#000000',
                light: '#ffffff'
            }
        });

        res.json({
            success: true,
            qrCode: qrCodeDataUrl
        });
    } catch (error) {
        console.error('Error generating QR code:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate QR code'
        });
    }
});

// Get transaction details by ID
app.get('/api/transaction/:transactionId', (req, res) => {
    const { transactionId } = req.params;

    if (!transactionId) {
        return res.status(400).json({
            success: false,
            error: 'Transaction ID is required'
        });
    }

    db.get(
        'SELECT * FROM orders WHERE transactionId = ?',
        [transactionId],
        (err, order) => {
            if (err) {
                return res.status(500).json({ error: err.message });
            }
            if (!order) {
                return res.status(404).json({ error: 'Transaction not found' });
            }

            try {
                order.items = JSON.parse(order.items);
                res.json({
                    success: true,
                    transaction: order
                });
            } catch (error) {
                res.status(500).json({ error: 'Error parsing order items' });
            }
        }
    );
});

// Expense API Endpoints

// Get expense categories
app.get('/api/expenses/categories', async (req, res) => {
    try {
        const categories = await expenseService.getExpenseCategories();
        res.json({
            success: true,
            categories: categories
        });
    } catch (error) {
        console.error('Error getting expense categories:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get expense categories'
        });
    }
});

// Get expenses
app.get('/api/expenses', async (req, res) => {
    try {
        const filters = {
            startDate: req.query.startDate,
            endDate: req.query.endDate,
            category: req.query.category,
            minAmount: req.query.minAmount,
            maxAmount: req.query.maxAmount
        };

        const expenses = await expenseService.getExpenses(filters);
        res.json({
            success: true,
            count: expenses.length,
            expenses: expenses
        });
    } catch (error) {
        console.error('Error getting expenses:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get expenses'
        });
    }
});

// Get expense by ID
app.get('/api/expenses/:id', async (req, res) => {
    try {
        const id = req.params.id;
        const expense = await expenseService.getExpenseById(id);

        if (!expense) {
            return res.status(404).json({
                success: false,
                error: 'Expense not found'
            });
        }

        res.json({
            success: true,
            expense: expense
        });
    } catch (error) {
        console.error('Error getting expense:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get expense'
        });
    }
});

// Create expense
app.post('/api/expenses', async (req, res) => {
    try {
        const expense = req.body;

        // Validate required fields
        if (!expense.description || !expense.amount || !expense.category) {
            return res.status(400).json({
                success: false,
                error: 'Description, amount, and category are required'
            });
        }

        const newExpense = await expenseService.recordExpense(expense);
        res.status(201).json({
            success: true,
            expense: newExpense
        });
    } catch (error) {
        console.error('Error creating expense:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create expense'
        });
    }
});

// Update expense
app.put('/api/expenses/:id', async (req, res) => {
    try {
        const id = req.params.id;
        const updates = req.body;

        const updatedExpense = await expenseService.updateExpense(id, updates);
        res.json({
            success: true,
            expense: updatedExpense
        });
    } catch (error) {
        console.error('Error updating expense:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to update expense'
        });
    }
});

// Delete expense
app.delete('/api/expenses/:id', async (req, res) => {
    try {
        const id = req.params.id;
        const result = await expenseService.deleteExpense(id);

        res.json({
            success: true,
            id: id
        });
    } catch (error) {
        console.error('Error deleting expense:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to delete expense'
        });
    }
});

// Get expense summary
app.get('/api/expenses/summary', async (req, res) => {
    try {
        const startDate = req.query.startDate || new Date().toISOString().split('T')[0];
        const endDate = req.query.endDate || new Date().toISOString().split('T')[0];

        const summary = await expenseService.getExpenseSummary(startDate, endDate);
        res.json({
            success: true,
            summary: summary
        });
    } catch (error) {
        console.error('Error getting expense summary:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get expense summary'
        });
    }
});

// Get financial summary
app.get('/api/financial/summary', async (req, res) => {
    try {
        // Get today's date
        const today = new Date().toISOString().split('T')[0];

        // Get current month start and end dates
        const currentDate = new Date();
        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

        const startDate = firstDayOfMonth.toISOString().split('T')[0];
        const endDate = lastDayOfMonth.toISOString().split('T')[0];

        // Get today's orders
        const todayOrdersQuery = `
            SELECT SUM(CASE
                WHEN items LIKE '%"price":%' THEN
                    (SELECT SUM(json_extract(value, '$.price') * json_extract(value, '$.quantity'))
                    FROM json_each(items))
                ELSE 0
            END) as total
            FROM orders
            WHERE date(timestamp) = ? AND paid = 1
        `;

        // Get month's orders
        const monthOrdersQuery = `
            SELECT SUM(CASE
                WHEN items LIKE '%"price":%' THEN
                    (SELECT SUM(json_extract(value, '$.price') * json_extract(value, '$.quantity'))
                    FROM json_each(items))
                ELSE 0
            END) as total
            FROM orders
            WHERE date(timestamp) >= ? AND date(timestamp) <= ? AND paid = 1
        `;

        // Get unpaid orders
        const unpaidOrdersQuery = `
            SELECT SUM(CASE
                WHEN items LIKE '%"price":%' THEN
                    (SELECT SUM(json_extract(value, '$.price') * json_extract(value, '$.quantity'))
                    FROM json_each(items))
                ELSE 0
            END) as total
            FROM orders
            WHERE paid = 0
        `;

        // Execute queries
        const todaySales = await new Promise((resolve, reject) => {
            db.get(todayOrdersQuery, [today], (err, row) => {
                if (err) reject(err);
                else resolve(row?.total || 0);
            });
        });

        const monthSales = await new Promise((resolve, reject) => {
            db.get(monthOrdersQuery, [startDate, endDate], (err, row) => {
                if (err) reject(err);
                else resolve(row?.total || 0);
            });
        });

        const outstandingAmount = await new Promise((resolve, reject) => {
            db.get(unpaidOrdersQuery, [], (err, row) => {
                if (err) reject(err);
                else resolve(row?.total || 0);
            });
        });

        // Get recent transactions
        const recentTransactionsQuery = `
            SELECT id, tableNumber, customerName, items, timestamp, paymentMethod, status
            FROM orders
            WHERE paid = 1 OR status = 'completed'
            ORDER BY timestamp DESC
            LIMIT 5
        `;

        const recentTransactions = await new Promise((resolve, reject) => {
            db.all(recentTransactionsQuery, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows || []);
            });
        });

        // Format transactions
        const formattedTransactions = recentTransactions.map(order => {
            let items;
            try {
                items = JSON.parse(order.items);
            } catch (e) {
                items = [];
            }

            const subtotal = items.reduce((sum, item) => {
                const price = parseFloat(item.price) || 0;
                const quantity = parseInt(item.quantity) || 1;
                return sum + (price * quantity);
            }, 0);

            return {
                date: order.timestamp.split('T')[0],
                description: `Table ${order.tableNumber} - ${order.customerName}`,
                amount: subtotal,
                type: 'Income'
            };
        });

        // Get recent expenses
        const recentExpenses = await expenseService.getExpenses({ limit: 5 });

        // Format expenses
        const formattedExpenses = recentExpenses.map(expense => ({
            date: expense.date,
            description: expense.description,
            amount: expense.amount,
            type: 'Expense'
        }));

        // Combine and sort transactions
        const allTransactions = [...formattedTransactions, ...formattedExpenses]
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 5);

        res.json({
            success: true,
            summary: {
                todaySales,
                monthSales,
                outstandingAmount
            },
            recentTransactions: allTransactions
        });
    } catch (error) {
        console.error('Error getting financial summary:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get financial summary'
        });
    }
});

// Data Export API Endpoints

// Export sales data
app.post('/api/export-sales', async (req, res) => {
    try {
        const {
            startDate,
            endDate,
            exportToLocal = true,
            exportToEmail = false,
            exportToGoogleSheets = false,
            printReport = false,
            emailRecipient = '',
            spreadsheetId = ''
        } = req.body;

        console.log(`Exporting sales data from ${startDate} to ${endDate}`);

        // Validate required fields
        if (!startDate || !endDate) {
            return res.status(400).json({
                success: false,
                error: 'Start date and end date are required'
            });
        }

        // Validate date format
        if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid date format. Use YYYY-MM-DD'
            });
        }

        // Validate email if email export is selected
        if (exportToEmail && !emailRecipient) {
            return res.status(400).json({
                success: false,
                error: 'Email recipient is required for email export'
            });
        }

        // Validate Google Spreadsheet ID if Google export is selected
        if (exportToGoogleSheets && !spreadsheetId) {
            return res.status(400).json({
                success: false,
                error: 'Google Spreadsheet ID is required for Google Sheets export'
            });
        }

        // Generate the export
        const exportOptions = {
            startDate,
            endDate,
            exportToLocal,
            exportToEmail,
            exportToGoogleSheets,
            printReport,
            emailRecipient,
            spreadsheetId
        };

        const result = await dataExportService.exportSalesReport(exportOptions);

        res.json({
            success: true,
            ...result
        });
    } catch (error) {
        console.error('Error exporting sales data:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to export sales data'
        });
    }
});

// Save auto-export settings
app.post('/api/settings/auto-export', (req, res) => {
    try {
        const { enabled, time, email } = req.body;

        // Validate required fields
        if (enabled === undefined) {
            return res.status(400).json({
                success: false,
                error: 'Enabled status is required'
            });
        }

        // Save settings to file
        const settingsPath = path.join(__dirname, 'auto-export-settings.json');
        fs.writeFileSync(settingsPath, JSON.stringify({
            enabled,
            time,
            email,
            lastUpdated: new Date().toISOString()
        }));

        // Schedule auto-export if enabled
        if (enabled && time) {
            // TODO: Implement scheduling logic
            console.log(`Auto-export scheduled for ${time} daily`);
        }

        res.json({
            success: true,
            message: 'Auto-export settings saved successfully'
        });
    } catch (error) {
        console.error('Error saving auto-export settings:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to save auto-export settings'
        });
    }
});

// Get auto-export settings
app.get('/api/settings/auto-export', (req, res) => {
    try {
        const settingsPath = path.join(__dirname, 'auto-export-settings.json');

        if (fs.existsSync(settingsPath)) {
            const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
            res.json({
                success: true,
                settings
            });
        } else {
            // Return default settings if file doesn't exist
            res.json({
                success: true,
                settings: {
                    enabled: false,
                    time: '23:59',
                    email: '',
                    lastUpdated: null
                }
            });
        }
    } catch (error) {
        console.error('Error getting auto-export settings:', error);
        res.status(500).json({
            success: false,
            error: error.message || 'Failed to get auto-export settings'
        });
    }
});

// Bill Archive API Endpoints

// Get bills by date
app.get('/api/bills/date/:date', (req, res) => {
    try {
        const date = req.params.date;
        console.log(`Getting bills for date: ${date}`);

        // Validate date format (YYYY-MM-DD)
        if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid date format. Use YYYY-MM-DD'
            });
        }

        const bills = billArchiveService.getBillsByDate(date);

        res.json({
            success: true,
            count: bills.length,
            bills: bills
        });
    } catch (error) {
        console.error('Error getting bills by date:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get bills'
        });
    }
});

// Get bill by ID
app.get('/api/bills/:id', (req, res) => {
    try {
        const billId = req.params.id;
        console.log(`Getting bill with ID: ${billId}`);

        const bill = billArchiveService.getBillById(billId);

        if (!bill) {
            return res.status(404).json({
                success: false,
                error: 'Bill not found'
            });
        }

        res.json({
            success: true,
            bill: bill
        });
    } catch (error) {
        console.error('Error getting bill by ID:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get bill'
        });
    }
});

// Get bill receipt HTML
app.get('/api/bills/:id/html', (req, res) => {
    try {
        const billId = req.params.id;
        console.log(`Getting HTML receipt for bill with ID: ${billId}`);

        const filePaths = billArchiveService.getBillFilePaths(billId);

        if (!filePaths.jsonPath) {
            return res.status(404).json({
                success: false,
                error: 'Bill not found'
            });
        }

        const htmlPath = filePaths.jsonPath.replace('.json', '.html');

        if (fs.existsSync(htmlPath)) {
            res.sendFile(htmlPath);
        } else {
            // If HTML doesn't exist, try to generate it on the fly
            const bill = billArchiveService.getBillById(billId);
            if (bill && filePaths.qrPath) {
                const htmlContent = billArchiveService.generateHtmlReceipt(bill, filePaths.qrPath);
                fs.writeFileSync(htmlPath, htmlContent);
                res.sendFile(htmlPath);
            } else {
                res.status(404).json({
                    success: false,
                    error: 'HTML receipt not found'
                });
            }
        }
    } catch (error) {
        console.error('Error getting bill HTML:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get bill HTML'
        });
    }
});

// Get bill receipt text (PDF)
app.get('/api/bills/:id/pdf', (req, res) => {
    try {
        const billId = req.params.id;
        console.log(`Getting PDF receipt for bill with ID: ${billId}`);

        const filePaths = billArchiveService.getBillFilePaths(billId);

        if (!filePaths.jsonPath) {
            return res.status(404).json({
                success: false,
                error: 'Bill not found'
            });
        }

        const pdfPath = filePaths.jsonPath.replace('.json', '.pdf');

        if (fs.existsSync(pdfPath)) {
            res.sendFile(pdfPath);
        } else {
            res.status(404).json({
                success: false,
                error: 'PDF receipt not found'
            });
        }
    } catch (error) {
        console.error('Error getting bill PDF:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get bill PDF'
        });
    }
});

// Search bills
app.get('/api/bills/search', (req, res) => {
    try {
        const criteria = req.query;
        console.log('Searching bills with criteria:', criteria);

        const bills = billArchiveService.searchBills(criteria);

        res.json({
            success: true,
            count: bills.length,
            bills: bills
        });
    } catch (error) {
        console.error('Error searching bills:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to search bills'
        });
    }
});

// Test endpoint to generate a bill with tax
app.get('/api/test/generate-bill', async (req, res) => {
    try {
        // Create a test order
        const testOrder = {
            id: Date.now(),
            tableNumber: 'Test-1',
            customerName: 'Test Customer',
            timestamp: new Date().toISOString(),
            items: [
                { name: 'Test Item 1', price: 10.00, quantity: 2 },
                { name: 'Test Item 2', price: 15.50, quantity: 1 }
            ],
            status: 'completed',
            paid: 1
        };

        // Calculate subtotal
        const subtotal = testOrder.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // For this test, let's create two bills - one with cash and one with card
        // First, create a cash payment (no tax)
        const tip = 5.00;

        // Create cash payment details (no tax)
        const cashPaymentDetails = {
            paymentMethod: 'cash',
            tip: tip,
            subtotal: subtotal,
            tax: 0,
            taxRate: 0,
            total: subtotal + tip, // No tax for cash payments
            transactionId: `test-cash-${Date.now()}`
        };

        // Create a copy of the order for the cash payment
        const cashOrder = {...testOrder, id: `cash-${testOrder.id}`};

        // Archive the cash bill
        await billArchiveService.archiveBill(cashOrder, cashPaymentDetails);

        // Now create a card payment (with tax)
        const taxRate = 0.07; // 7%
        const taxAmount = subtotal * taxRate;
        const total = subtotal + taxAmount + tip;

        // Create card payment details (with tax)
        const cardPaymentDetails = {
            paymentMethod: 'card',
            tip: tip,
            subtotal: subtotal,
            tax: taxAmount,
            taxRate: taxRate,
            total: total,
            transactionId: `test-card-${Date.now()}`
        };

        // Create a copy of the order for the card payment
        const cardOrder = {...testOrder, id: `card-${testOrder.id}`};

        // Archive the card bill
        const archiveResult = await billArchiveService.archiveBill(cardOrder, cardPaymentDetails);

        res.json({
            success: true,
            message: 'Test bills generated and archived',
            cashBillId: cashOrder.id,
            cardBillId: cardOrder.id,
            archiveResult: archiveResult
        });
    } catch (error) {
        console.error('Error generating test bill:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate test bill'
        });
    }
});

// Pending Bills API Endpoints

// Get all pending bills
app.get('/api/pending-bills', (req, res) => {
    // Check if we're filtering by table and customer
    const tableNumber = req.query.tableNumber;
    const customerName = req.query.customerName;

    let sql = `SELECT * FROM pending_bills`;
    let params = [];

    if (tableNumber && customerName) {
        sql += ` WHERE tableNumber = ? AND customerName = ?`;
        params = [tableNumber, customerName];
    }

    sql += ` ORDER BY createdAt DESC`;

    db.all(sql, params, (err, rows) => {
        if (err) {
            console.error('Error fetching pending bills:', err);
            return res.status(500).json({ success: false, error: err.message });
        }

        // Parse items JSON for each bill
        const bills = rows.map(bill => {
            try {
                if (bill.items && typeof bill.items === 'string') {
                    bill.items = JSON.parse(bill.items);
                }
                return bill;
            } catch (e) {
                console.error('Error parsing items for bill:', bill.id, e);
                bill.items = [];
                return bill;
            }
        });

        res.json({ success: true, bills });
    });
});

// Get a specific pending bill
app.get('/api/pending-bills/:id', (req, res) => {
    const billId = req.params.id;

    db.get(`SELECT * FROM pending_bills WHERE id = ?`, [billId], (err, bill) => {
        if (err) {
            console.error('Error fetching pending bill:', err);
            return res.status(500).json({ success: false, error: err.message });
        }

        if (!bill) {
            return res.status(404).json({ success: false, error: 'Bill not found' });
        }

        // Parse items JSON
        try {
            if (bill.items && typeof bill.items === 'string') {
                bill.items = JSON.parse(bill.items);
            }
        } catch (e) {
            console.error('Error parsing items for bill:', bill.id, e);
            bill.items = [];
        }

        res.json({ success: true, bill });
    });
});

// Mark a pending bill as paid
app.post('/api/pending-bills/:id/pay', (req, res) => {
    const billId = req.params.id;
    const { paymentMethod, transactionId } = req.body;

    if (!paymentMethod) {
        return res.status(400).json({ success: false, error: 'Payment method is required' });
    }

    const paidAt = new Date().toISOString();

    // Update the pending bill status
    db.run(
        `UPDATE pending_bills SET status = ?, paidAt = ?, paymentMethod = ?, transactionId = ? WHERE id = ?`,
        ['paid', paidAt, paymentMethod, transactionId || null, billId],
        function(err) {
            if (err) {
                console.error('Error updating pending bill:', err);
                return res.status(500).json({ success: false, error: err.message });
            }

            if (this.changes === 0) {
                return res.status(404).json({ success: false, error: 'Bill not found' });
            }

            // Get the updated bill for archiving
            db.get(`SELECT * FROM pending_bills WHERE id = ?`, [billId], async (err, bill) => {
                if (err || !bill) {
                    console.error('Error fetching updated bill:', err);
                    // Still return success since the bill was marked as paid
                    return res.json({ success: true, message: 'Bill marked as paid' });
                }

                try {
                    // Parse items
                    let items = [];
                    if (bill.items && typeof bill.items === 'string') {
                        items = JSON.parse(bill.items);
                    }

                    // Create order object for archiving
                    const order = {
                        id: bill.id,
                        tableNumber: bill.tableNumber,
                        customerName: bill.customerName,
                        items: items,
                        timestamp: bill.createdAt
                    };

                    // Calculate subtotal
                    const subtotal = items.reduce((sum, item) => {
                        const price = parseFloat(item.price) || 0;
                        const quantity = parseInt(item.quantity) || 1;
                        return sum + (price * quantity);
                    }, 0);

                    // Create payment details
                    // For cash payments, no taxes are applied
                    const paymentDetails = {
                        paymentMethod: paymentMethod,
                        transactionId: transactionId || null,
                        subtotal: subtotal
                    };

                    // Add tax information only for non-cash payments
                    if (paymentMethod !== 'cash') {
                        paymentDetails.tax = subtotal * 0.07; // Default 7% tax
                        paymentDetails.taxRate = 0.07;
                        paymentDetails.total = subtotal * 1.07; // Subtotal + tax
                    } else {
                        paymentDetails.tax = 0;
                        paymentDetails.taxRate = 0;
                        paymentDetails.total = subtotal; // For cash, total is just the subtotal
                    }

                    // Archive the bill
                    await billArchiveService.archiveBill(order, paymentDetails);

                    res.json({ success: true, message: 'Bill marked as paid and archived' });
                } catch (archiveError) {
                    console.error('Error archiving paid bill:', archiveError);
                    // Still return success since the bill was marked as paid
                    res.json({ success: true, message: 'Bill marked as paid' });
                }
            });
        }
    );
});

// Delete a pending bill
app.delete('/api/pending-bills/:id', (req, res) => {
    const billId = req.params.id;

    db.run(`DELETE FROM pending_bills WHERE id = ?`, [billId], function(err) {
        if (err) {
            console.error('Error deleting pending bill:', err);
            return res.status(500).json({ success: false, error: err.message });
        }

        if (this.changes === 0) {
            return res.status(404).json({ success: false, error: 'Bill not found' });
        }

        res.json({ success: true, message: 'Bill deleted successfully' });
    });
});

// Mark bill as pending
app.post('/mark-as-pending', (req, res) => {
    const { orderData, notes } = req.body;
    console.log('Marking bill as pending:', orderData.tableNumber, orderData.customerName);

    if (!orderData) {
        return res.status(400).json({ success: false, error: 'No order data provided' });
    }

    try {
        console.log('Order data received:', JSON.stringify(orderData));

        // Create a pending bill record
        const pendingBill = {
            id: Date.now().toString(),
            orderData: orderData,
            notes: notes || '',
            createdAt: new Date().toISOString(),
            status: 'pending'
        };

        console.log('Created pending bill:', JSON.stringify(pendingBill));

        // Save to pending bills database
        db.run(
            `INSERT INTO pending_bills (id, tableNumber, customerName, items, notes, createdAt, status)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
                pendingBill.id,
                orderData.tableNumber || '',
                orderData.customerName || '',
                (() => {
                    try {
                        return JSON.stringify(orderData.items || []);
                    } catch (e) {
                        console.error('Error stringifying items:', e);
                        return '[]';
                    }
                })(),
                pendingBill.notes || '',
                pendingBill.createdAt,
                pendingBill.status || 'pending'
            ],
            function(err) {
                if (err) {
                    console.error('Error saving pending bill:', err);
                    return res.status(500).json({ success: false, error: err.message });
                }

                // Remove the order from active orders
                db.run(
                    `DELETE FROM orders WHERE tableNumber = ? AND customerName = ?`,
                    [orderData.tableNumber, orderData.customerName],
                    function(deleteErr) {
                        if (deleteErr) {
                            console.error('Error removing order:', deleteErr);
                            // Still return success since the pending bill was created
                        }

                        console.log(`Bill marked as pending for table ${orderData.tableNumber}, customer ${orderData.customerName}`);
                        res.status(200).json({
                            success: true,
                            message: 'Bill marked as pending',
                            pendingBillId: pendingBill.id
                        });
                    }
                );
            }
        );
    } catch (error) {
        console.error('Error marking bill as pending:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({ success: false, error: error.message });
    }
});

// WhatsApp API endpoint
app.post('/send-whatsapp', async (req, res) => {
    const { phoneNumber, message, senderNumber } = req.body;

    if (!phoneNumber || !message) {
        return res.status(400).json({
            success: false,
            error: 'Phone number and message are required'
        });
    }

    try {
        // Get WhatsApp configuration
        const whatsappConfig = {
            accountSid: process.env.WHATSAPP_ACCOUNT_SID,
            authToken: process.env.WHATSAPP_AUTH_TOKEN,
            fromNumber: senderNumber || process.env.WHATSAPP_FROM_NUMBER
        };

        // Initialize WhatsApp client (using Twilio as an example)
        const client = require('twilio')(whatsappConfig.accountSid, whatsappConfig.authToken);

        // Send the message
        const result = await client.messages.create({
            body: message,
            from: `whatsapp:${whatsappConfig.fromNumber}`,
            to: `whatsapp:${phoneNumber}`
        });

        console.log(`WhatsApp message sent successfully. SID: ${result.sid}`);

        res.json({
            success: true,
            message: 'WhatsApp message sent successfully',
            messageId: result.sid
        });
    } catch (error) {
        console.error('Error sending WhatsApp message:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

async function checkForUpdates() {
    const currentVersion = require('./package.json').version;

    try {
        // Replace with your update server URL
        const response = await fetch('https://your-update-server.com/version');
        const { latestVersion, downloadUrl } = await response.json();

        if (semver.gt(latestVersion, currentVersion)) {
            console.log(`Update available: ${latestVersion}`);
            // Notify users through the web interface
            // You can implement the update process here
        }
    } catch (error) {
        console.error('Update check failed:', error);
    }
}

// Check for updates periodically
setInterval(checkForUpdates, 24 * 60 * 60 * 1000); // Check daily

// WhatsApp template API endpoint
app.post('/api/whatsapp-template', (req, res) => {
    const { template, businessInfo } = req.body;

    if (!template) {
        return res.status(400).json({
            success: false,
            error: 'Template is required'
        });
    }

    // In a real implementation, this would save to a database
    // For now, we'll just save to files
    try {
        // Save template
        fs.writeFileSync('whatsapp-template.txt', template, 'utf8');

        // Save business info if provided
        if (businessInfo) {
            fs.writeFileSync('whatsapp-business-info.json', JSON.stringify(businessInfo, null, 2), 'utf8');
        }

        res.json({
            success: true,
            message: 'WhatsApp settings saved successfully'
        });
    } catch (error) {
        console.error('Error saving WhatsApp settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to save settings'
        });
    }
});

// Get WhatsApp settings
app.get('/api/whatsapp-settings', (req, res) => {
    try {
        const settingsPath = path.join(__dirname, 'whatsapp-settings.json');

        if (fs.existsSync(settingsPath)) {
            const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
            res.json({
                success: true,
                settings: settings
            });
        } else {
            // Return default settings if file doesn't exist
            res.json({
                success: true,
                settings: {
                    businessName: 'Paradiz Hookah Lounge',
                    businessPhone: '',
                    businessAddress: '',
                    businessTagline: 'The Best Hookah Experience',
                    includeLogo: true,
                    includeQR: true,
                    includeItems: true,
                    footerText: 'Thank you for your business!'
                }
            });
        }
    } catch (error) {
        console.error('Error getting WhatsApp settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get WhatsApp settings'
        });
    }
});

// Save WhatsApp settings
app.post('/api/whatsapp-settings', (req, res) => {
    try {
        const settings = req.body;
        const settingsPath = path.join(__dirname, 'whatsapp-settings.json');

        // Save settings to file
        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));

        res.json({
            success: true,
            message: 'WhatsApp settings saved successfully'
        });
    } catch (error) {
        console.error('Error saving WhatsApp settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to save WhatsApp settings'
        });
    }
});

// Send test WhatsApp receipt
app.post('/api/whatsapp-test', (req, res) => {
    try {
        const { phoneNumber, settings, order } = req.body;

        if (!phoneNumber) {
            return res.status(400).json({
                success: false,
                error: 'Phone number is required'
            });
        }

        // Generate receipt message
        let message = '';

        // Business name and tagline
        message += `*${settings.businessName}*\n`;
        if (settings.businessTagline) {
            message += `${settings.businessTagline}\n`;
        }
        message += '\n';

        // Order details
        const date = new Date(order.timestamp);
        message += `Date: ${date.toLocaleDateString()} | Time: ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n`;
        message += `Table: ${order.tableNumber} | Customer: ${order.customerName}\n\n`;

        // Items
        if (settings.includeItems && order.items && order.items.length > 0) {
            message += '*Items:*\n';
            order.items.forEach(item => {
                const itemTotal = item.price * item.quantity;
                message += `${item.name} x${item.quantity}: $${itemTotal.toFixed(2)}\n`;
            });
            message += '\n';
        }

        // Total
        const total = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        message += `*Total: $${total.toFixed(2)}*\n\n`;

        // Payment method
        message += `Payment Method: ${order.paymentMethod}\n\n`;

        // Footer
        if (settings.footerText) {
            message += `${settings.footerText}\n`;
        }

        // Business contact
        if (settings.businessPhone) {
            message += `\nContact: ${settings.businessPhone}`;
        }

        // Send the message
        console.log(`Sending test receipt to ${phoneNumber}`);
        console.log('Message content:', message);

        // Simulate API delay
        setTimeout(() => {
            res.json({
                success: true,
                message: 'Test receipt sent successfully'
            });
        }, 1000);
    } catch (error) {
        console.error('Error sending test receipt:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to send test receipt'
        });
    }
});

// Get WhatsApp template and business info (legacy)
app.get('/api/whatsapp-template', (req, res) => {
    try {
        // Default template
        const defaultTemplate = `{{#logoTop}}
{{logoUrl}}
{{/logoTop}}

*{{businessName}}*
{{#businessTagline}}{{businessTagline}}{{/businessTagline}}

Date: {{date}} | Time: {{time}}
Table: {{tableNumber}} | Customer: {{customerName}}

*Items:*
{{items}}

*Summary:*
Subtotal: ${{subtotal}}
GST (5%): ${{gst}}
PST (7%): ${{pst}}
*Total: ${{total}}*

Payment Method: {{paymentMethod}}
{{#transactionId}}Transaction ID: {{transactionId}}{{/transactionId}}

Thank you for your business!
{{#contactInfo}}Contact us: {{contactInfo}}{{/contactInfo}}

{{#qrCode}}
Scan to verify receipt:
{{qrCode}}
{{/qrCode}}

{{#logoBottom}}
{{logoUrl}}
{{/logoBottom}}`;

        // Default business info
        const defaultBusinessInfo = {
            name: 'Paradiz Hookah Lounge',
            tagline: 'The Best Hookah Experience',
            logoUrl: '',
            logoPosition: 'top',
            contactInfo: '',
            senderWhatsapp: ''
        };

        // Response object
        const response = {
            success: true,
            template: defaultTemplate,
            businessInfo: defaultBusinessInfo
        };

        // Check if template file exists
        if (fs.existsSync('whatsapp-template.txt')) {
            response.template = fs.readFileSync('whatsapp-template.txt', 'utf8');
        }

        // Check if business info file exists
        if (fs.existsSync('whatsapp-business-info.json')) {
            try {
                response.businessInfo = JSON.parse(fs.readFileSync('whatsapp-business-info.json', 'utf8'));
            } catch (parseError) {
                console.error('Error parsing business info:', parseError);
                // Keep default business info if parsing fails
            }
        }

        res.json(response);
    } catch (error) {
        console.error('Error reading WhatsApp settings:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to read WhatsApp settings'
        });
    }
});

// Function to generate a unique transaction ID
function generateTransactionId() {
    const timestamp = new Date().getTime();
    const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `TRX-${timestamp}-${randomPart}`;
}

// Initialize Mobile App API
initializeMobileAppAPI(app, db);
console.log('Mobile App API initialized');

// Create database tables for mobile app if they don't exist
fs.readFile('mobile-app-schema.sql', 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading mobile app schema file:', err);
        return;
    }

    // Split the SQL file into individual statements
    const statements = data.split(';').filter(stmt => stmt.trim());

    // Execute each statement
    statements.forEach(statement => {
        db.run(statement, err => {
            if (err) {
                console.error('Error executing schema statement:', err);
                console.error('Statement:', statement);
            }
        });
    });

    console.log('Mobile app database schema initialized');
});

// Generate QR codes for tables
function generateTableQRCodes() {
    const tablesDir = path.join(__dirname, 'public', 'table-qr-codes');

    // Create directory if it doesn't exist
    if (!fs.existsSync(tablesDir)) {
        fs.mkdirSync(tablesDir, { recursive: true });
    }

    // Generate QR codes for tables 1-20
    for (let tableNumber = 1; tableNumber <= 20; tableNumber++) {
        const sessionId = crypto.randomBytes(16).toString('hex');
        const tableData = JSON.stringify({
            table: tableNumber,
            session: sessionId,
            timestamp: new Date().toISOString()
        });

        const qrPath = path.join(tablesDir, `table-${tableNumber}.png`);

        QRCode.toFile(qrPath, tableData, {
            color: {
                dark: '#6B46C1',  // Dark lavender
                light: '#FFFFFF'
            },
            width: 300,
            margin: 1
        }, err => {
            if (err) {
                console.error(`Error generating QR code for table ${tableNumber}:`, err);
            } else {
                console.log(`QR code generated for table ${tableNumber}`);
            }
        });
    }
}

// Generate QR codes on startup
generateTableQRCodes();

const PORT = 5000;
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});


