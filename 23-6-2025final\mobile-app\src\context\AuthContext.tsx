import React, { createContext, useState, useEffect } from 'react';
import * as SecureStore from 'expo-secure-store';
import { api } from '../api/api';

interface AuthContextData {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<boolean>;
  signOut: () => Promise<void>;
  signUp: (name: string, email: string, password: string) => Promise<boolean>;
}

interface User {
  id: string;
  name: string;
  email: string;
}

export const AuthContext = createContext<AuthContextData>({} as AuthContextData);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load user from storage
    loadStoredUser();
  }, []);

  async function loadStoredUser() {
    try {
      const userString = await SecureStore.getItemAsync('user');
      const token = await SecureStore.getItemAsync('userToken');
      
      if (userString && token) {
        setUser(JSON.parse(userString));
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      }
    } catch (error) {
      console.log('Error loading user from storage:', error);
    } finally {
      setIsLoading(false);
    }
  }

  async function signIn(email: string, password: string): Promise<boolean> {
    try {
      setIsLoading(true);
      
      // Call your API to authenticate
      const response = await api.post('/api/auth/login', { email, password });
      
      if (response.data.success) {
        const { user, token } = response.data;
        
        // Store user and token
        await SecureStore.setItemAsync('user', JSON.stringify(user));
        await SecureStore.setItemAsync('userToken', token);
        
        // Set user in context
        setUser(user);
        
        // Set token in API headers
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.log('Error signing in:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }

  async function signOut(): Promise<void> {
    try {
      setIsLoading(true);
      
      // Remove user and token from storage
      await SecureStore.deleteItemAsync('user');
      await SecureStore.deleteItemAsync('userToken');
      
      // Remove token from API headers
      delete api.defaults.headers.common['Authorization'];
      
      // Clear user from context
      setUser(null);
    } catch (error) {
      console.log('Error signing out:', error);
    } finally {
      setIsLoading(false);
    }
  }

  async function signUp(name: string, email: string, password: string): Promise<boolean> {
    try {
      setIsLoading(true);
      
      // Call your API to register
      const response = await api.post('/api/auth/register', { name, email, password });
      
      if (response.data.success) {
        // Automatically sign in after registration
        return await signIn(email, password);
      }
      
      return false;
    } catch (error) {
      console.log('Error signing up:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <AuthContext.Provider value={{ user, isLoading, signIn, signOut, signUp }}>
      {children}
    </AuthContext.Provider>
  );
};
