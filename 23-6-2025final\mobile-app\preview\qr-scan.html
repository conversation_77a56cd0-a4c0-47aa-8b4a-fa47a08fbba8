<!DOCTYPE html>
<html>
<head>
    <title>Paradiz App Preview - QR Scan</title>
    <style>
        body { font-family: Arial, sans-serif; display: flex; justify-content: center; padding: 20px; background-color: #f5f5f5; }
        .phone { width: 375px; height: 667px; background: black; border-radius: 30px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.15); position: relative; }
        .scan-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; }
        .scan-frame { width: 250px; height: 250px; border: 2px solid white; border-radius: 20px; position: relative; }
        .scan-corner { position: absolute; width: 20px; height: 20px; border-color: #6B46C1; }
        .top-left { top: -2px; left: -2px; border-top: 4px solid; border-left: 4px solid; }
        .top-right { top: -2px; right: -2px; border-top: 4px solid; border-right: 4px solid; }
        .bottom-left { bottom: -2px; left: -2px; border-bottom: 4px solid; border-left: 4px solid; }
        .bottom-right { bottom: -2px; right: -2px; border-bottom: 4px solid; border-right: 4px solid; }
        .scan-text { color: white; margin-top: 20px; font-size: 18px; text-align: center; max-width: 80%; }
        .scan-line { position: absolute; height: 2px; width: 100%; background-color: #6B46C1; top: 50%; animation: scan 2s linear infinite; }
        .cancel-button { position: absolute; bottom: 40px; background-color: rgba(255,255,255,0.2); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: bold; cursor: pointer; backdrop-filter: blur(5px); }
        .demo-controls { margin-top: 20px; text-align: center; }
        .button { background: #6B46C1; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-weight: bold; cursor: pointer; }
        .scan-button { background: #38A169; color: white; border: none; padding: 10px 20px; border-radius: 8px; font-weight: bold; cursor: pointer; position: absolute; bottom: 100px; }
        .header-bar { position: absolute; top: 0; left: 0; width: 100%; padding: 15px 0; background-color: rgba(0,0,0,0.5); display: flex; justify-content: center; }
        .header-text { color: white; font-size: 16px; font-weight: bold; }
        .permission-note { position: absolute; top: 60px; width: 100%; text-align: center; color: rgba(255,255,255,0.7); font-size: 12px; }

        @keyframes scan {
            0% { top: 20%; opacity: 0.1; }
            50% { top: 80%; opacity: 0.8; }
            100% { top: 20%; opacity: 0.1; }
        }
    </style>
</head>
<body>
    <div>
        <div class="phone">
            <div class="header-bar">
                <div class="header-text">Scan QR Code</div>
            </div>
            <div class="permission-note">Camera permission granted</div>
            <div class="scan-overlay">
                <div class="scan-frame">
                    <div class="scan-corner top-left"></div>
                    <div class="scan-corner top-right"></div>
                    <div class="scan-corner bottom-left"></div>
                    <div class="scan-corner bottom-right"></div>
                    <div class="scan-line"></div>
                </div>
                <div class="scan-text">Position the QR code within the frame to scan</div>
                <button class="cancel-button" onclick="window.location.href='index.html'">Cancel</button>
                <button class="scan-button" onclick="window.location.href='table-order.html'">Simulate Successful Scan</button>
            </div>
        </div>

        <div class="demo-controls">
            <p>This screen simulates scanning a QR code at the table</p>
            <button class="button" onclick="window.location.href='index.html'">Back to Out-of-Store View</button>
            <button class="button" style="background-color: #38A169;" onclick="window.location.href='table-order.html'">Skip to Table Order View</button>
        </div>
    </div>
</body>
</html>
