import React, { useState, useEffect, useContext } from 'react';
import { View, StyleSheet, ScrollView, Image, TouchableOpacity, Alert } from 'react-native';
import { Text, Card, Button, Title, Paragraph, ActivityIndicator } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { Ionicons } from '@expo/vector-icons';
import { AuthContext } from '../context/AuthContext';
import { NotificationContext } from '../context/NotificationContext';
import { menuApi } from '../api/api';
import NotificationBell from '../components/NotificationBell';

const HomeScreen = () => {
  const navigation = useNavigation();
  const { user } = useContext(AuthContext);
  const { unreadCount } = useContext(NotificationContext);
  const [featuredItems, setFeaturedItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasPermission, setHasPermission] = useState(null);
  const [scanning, setScanning] = useState(false);
  const [showEventBanner, setShowEventBanner] = useState(true);

  useEffect(() => {
    loadFeaturedItems();
    requestCameraPermission();
  }, []);

  const requestCameraPermission = async () => {
    const { status } = await BarCodeScanner.requestPermissionsAsync();
    setHasPermission(status === 'granted');
  };

  const loadFeaturedItems = async () => {
    try {
      setLoading(true);
      const response = await menuApi.getItems();
      // Get featured items or just the first few items
      setFeaturedItems(response.data.items.slice(0, 4));
    } catch (error) {
      console.error('Error loading featured items:', error);
      Alert.alert('Error', 'Failed to load featured items');
    } finally {
      setLoading(false);
    }
  };

  const handleBarCodeScanned = ({ type, data }) => {
    setScanning(false);

    try {
      // Parse the QR code data
      const qrData = JSON.parse(data);

      if (qrData.table && qrData.session) {
        // Navigate to table order screen
        navigation.navigate('TableOrder', {
          tableId: qrData.table,
          sessionId: qrData.session
        });
      } else {
        Alert.alert('Invalid QR Code', 'This QR code is not valid for table ordering.');
      }
    } catch (error) {
      Alert.alert('Invalid QR Code', 'Could not process this QR code.');
    }
  };

  const startScanning = () => {
    if (hasPermission === null) {
      Alert.alert('Requesting Permission', 'We need camera permission to scan QR codes.');
      requestCameraPermission();
    } else if (hasPermission === false) {
      Alert.alert('No Camera Permission', 'Please enable camera permission in your settings to use this feature.');
    } else {
      setScanning(true);
    }
  };

  return (
    <View style={styles.container}>
      {scanning ? (
        <View style={styles.scannerContainer}>
          <BarCodeScanner
            onBarCodeScanned={handleBarCodeScanned}
            style={StyleSheet.absoluteFillObject}
          />
          <Button
            mode="contained"
            onPress={() => setScanning(false)}
            style={styles.cancelButton}
          >
            Cancel
          </Button>
        </View>
      ) : (
        <ScrollView>
          <View style={styles.header}>
            <View style={styles.headerTop}>
              <Image
                source={require('../../assets/images/logo.png')}
                style={styles.logo}
                resizeMode="contain"
              />
              <TouchableOpacity
                style={styles.notificationContainer}
                onPress={() => navigation.navigate('Notifications')}
              >
                <NotificationBell onPress={() => navigation.navigate('Notifications')} />
              </TouchableOpacity>
            </View>
            <Text style={styles.welcomeText}>
              Welcome{user ? `, ${user.name}` : ''}!
            </Text>
          </View>

          {showEventBanner && (
            <TouchableOpacity
              style={styles.eventBanner}
              onPress={() => navigation.navigate('EventDetails', { eventId: '1' })}
            >
              <View style={styles.eventBannerContent}>
                <View style={styles.eventIconContainer}>
                  <Ionicons name="musical-notes" size={24} color="white" />
                </View>
                <View style={styles.eventTextContainer}>
                  <Text style={styles.eventTitle}>Live Music Night - This Friday!</Text>
                  <Text style={styles.eventDesc}>Join us for live music and special hookah flavors</Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={(e) => {
                  e.stopPropagation();
                  setShowEventBanner(false);
                }}
              >
                <Ionicons name="close" size={20} color="white" />
              </TouchableOpacity>
            </TouchableOpacity>
          )}

          <Card style={styles.scanCard}>
            <Card.Content>
              <Title style={styles.cardTitle}>Order from your table</Title>
              <Paragraph>Scan the QR code at your table to place an order directly from your phone.</Paragraph>
            </Card.Content>
            <Card.Actions>
              <Button
                mode="contained"
                onPress={startScanning}
                icon="qrcode-scan"
              >
                Scan QR Code
              </Button>
            </Card.Actions>
          </Card>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Featured Items</Text>
            {loading ? (
              <ActivityIndicator size="large" color="#6B46C1" />
            ) : (
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {featuredItems.map((item) => (
                  <TouchableOpacity
                    key={item.id}
                    onPress={() => navigation.navigate('MenuDetail', { itemId: item.id })}
                  >
                    <Card style={styles.featuredCard}>
                      <Card.Cover source={{ uri: item.image }} style={styles.featuredImage} />
                      <Card.Content>
                        <Title style={styles.itemTitle}>{item.name}</Title>
                        <Paragraph style={styles.itemPrice}>${item.price.toFixed(2)}</Paragraph>
                      </Card.Content>
                    </Card>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            )}
          </View>

          <View style={styles.quickLinks}>
            <TouchableOpacity
              style={styles.quickLink}
              onPress={() => navigation.navigate('Menu')}
            >
              <View style={[styles.iconContainer, { backgroundColor: '#6B46C1' }]}>
                <Ionicons name="restaurant-outline" size={24} color="white" />
              </View>
              <Text style={styles.quickLinkText}>Menu</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickLink}
              onPress={() => navigation.navigate('Reservation')}
            >
              <View style={[styles.iconContainer, { backgroundColor: '#38B2AC' }]}>
                <Ionicons name="calendar-outline" size={24} color="white" />
              </View>
              <Text style={styles.quickLinkText}>Reservations</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickLink}
              onPress={() => navigation.navigate('Profile')}
            >
              <View style={[styles.iconContainer, { backgroundColor: '#ED8936' }]}>
                <Ionicons name="person-outline" size={24} color="white" />
              </View>
              <Text style={styles.quickLinkText}>Profile</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  header: {
    padding: 20,
    marginTop: 20,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  logo: {
    width: 150,
    height: 80,
  },
  notificationContainer: {
    padding: 8,
  },
  welcomeText: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 10,
    color: '#2D3748',
    textAlign: 'center',
  },
  eventBanner: {
    backgroundColor: '#6B46C1',
    margin: 16,
    marginTop: 0,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  eventBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  eventIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  eventTextContainer: {
    flex: 1,
  },
  eventTitle: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  eventDesc: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  closeButton: {
    padding: 4,
  },
  scanCard: {
    margin: 16,
    elevation: 4,
  },
  cardTitle: {
    color: '#6B46C1',
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#2D3748',
  },
  featuredCard: {
    width: 200,
    marginRight: 16,
    elevation: 3,
  },
  featuredImage: {
    height: 120,
  },
  itemTitle: {
    fontSize: 16,
  },
  itemPrice: {
    color: '#6B46C1',
    fontWeight: 'bold',
  },
  quickLinks: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    marginBottom: 20,
  },
  quickLink: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickLinkText: {
    fontSize: 14,
    color: '#2D3748',
  },
  scannerContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  cancelButton: {
    margin: 20,
    backgroundColor: '#E53E3E',
  },
});

export default HomeScreen;
