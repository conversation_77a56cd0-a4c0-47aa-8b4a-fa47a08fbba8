<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Ordering System</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- POS Button -->
        <button id="pos-button" class="pos-button"><i class="fas fa-cash-register"></i> POS</button>

        <!-- Left Panel: Table & Customer Info -->
        <div class="left-panel">
            <h2>Table: <span id="table-number">--</span></h2>

            <!-- Customer Name Input -->
            <div class="customer-name-input">
                <label for="customer-name">Customer Name:</label>
                <input type="text" id="customer-name" placeholder="Enter customer name" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false">
            </div>

            <!-- Cover Charge Section -->
            <div class="cover-charge-section">
                <div class="cover-charge-toggle">
                    <label for="cover-charge-toggle">Event Cover Charge:</label>
                    <select id="cover-charge-toggle">
                        <option value="0">No Cover Charge</option>
                        <option value="10">$10 per person</option>
                        <option value="15">$15 per person</option>
                        <option value="20">$20 per person</option>
                    </select>
                </div>
                <div class="person-count" id="person-count-container" style="display: none;">
                    <label for="person-count">Number of People:</label>
                    <div class="person-count-controls">
                        <button class="count-btn" id="decrease-count"><i class="fas fa-minus"></i></button>
                        <input type="number" id="person-count" value="1" min="1" max="20">
                        <button class="count-btn" id="increase-count"><i class="fas fa-plus"></i></button>
                    </div>
                </div>
            </div>

            <!-- Table Selection -->
            <div class="selection-grid">
                <div class="tables-column">
                    <h3>Tables</h3>
                    <div class="table-buttons" id="table-buttons">
                        <!-- Buttons for tables 1 to 15 will be generated dynamically -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Middle Panel: Categories & Order Items -->
        <div class="middle-panel">
            <div class="table-customer-info">
                <span>Table: <span id="middle-table-number">--</span></span>
                <span> | Customer: <span id="middle-customer-name">--</span></span>
            </div>

            <!-- Categories -->
            <div class="categories" id="categories">
                <!-- Category buttons will be generated dynamically -->
            </div>

            <!-- Order Items Grid -->
            <div class="items-grid" id="items-grid">
                <p>Select a category to see items</p>
            </div>
        </div>

        <!-- Right Panel: Order Summary -->
        <div class="right-panel">
            <div class="order-summary">
                <h3>Order Summary</h3>
                <p>Table: <span id="summary-table">--</span></p>
                <p>Customer: <span id="summary-customer">--</span></p>
                <ul id="order-list"></ul>
                <div class="summary-buttons">
                    <button class="send-btn" id="send-btn"><i class="fas fa-paper-plane"></i> Send Order</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Hookah Flavors -->
    <div id="hookah-modal" class="modal">
        <div class="modal-content">
            <span class="close" id="close-modal">&times;</span>
            <h3>Select Hookah Flavors</h3>
            <p class="flavor-hint">(Select up to 3 flavors)</p>
            <div class="flavor-grid" id="flavor-grid">
                <!-- Hookah flavor checkboxes will be generated dynamically -->
            </div>
            <div class="modal-footer">
                <div class="selected-flavors">Selected: <span id="selected-count">0</span>/3</div>
                <button class="add-hookah-btn" id="add-hookah-btn"><i class="fas fa-plus-circle"></i> Add to Order</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="virtual-keyboard.js"></script>
</body>
</html>