const express = require('express');
const path = require('path');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// API endpoint to get menu items
app.get('/api/menu', (req, res) => {
  const menuItems = [
    {
      id: 1,
      name: 'Double Apple Hookah',
      description: 'Classic double apple flavor',
      price: 25.99,
      category: 'Hookahs',
      image: '/images/double-apple.jpg'
    },
    {
      id: 2,
      name: 'Mint Hookah',
      description: 'Refreshing mint flavor',
      price: 25.99,
      category: 'Hookahs',
      image: '/images/mint.jpg'
    },
    {
      id: 3,
      name: '<PERSON><PERSON><PERSON>',
      description: 'Served with warm pita bread',
      price: 8.99,
      category: 'Food',
      image: '/images/hummus.jpg'
    },
    {
      id: 4,
      name: 'Turkish Coffee',
      description: 'Traditional Turkish coffee',
      price: 4.99,
      category: 'Drinks',
      image: '/images/turkish-coffee.jpg'
    }
  ];
  
  res.json(menuItems);
});

// API endpoint to get available reservation times
app.get('/api/reservations/availability', (req, res) => {
  const { date } = req.query;
  
  // In a real app, this would check a database
  const availableTimes = [
    '17:00', '17:30', '18:00', '18:30', '19:00', '19:30',
    '20:00', '20:30', '21:00', '21:30', '22:00', '22:30'
  ];
  
  res.json({ date, availableTimes });
});

// API endpoint to create a reservation
app.post('/api/reservations', (req, res) => {
  const { date, time, partySize, specialRequests } = req.body;
  
  // In a real app, this would save to a database
  res.json({
    success: true,
    reservation: {
      id: Math.floor(Math.random() * 1000),
      date,
      time,
      partySize,
      specialRequests,
      status: 'confirmed'
    }
  });
});

// Catch-all route to serve the main HTML file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log(`Open your browser to see the mobile app preview`);
});
