<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Backup Manager</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Theme system -->
    <script src="../theme.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-database"></i> POS Backup & Data Export</h1>
            <div class="actions">
                <button id="create-backup-btn" class="primary-btn">
                    <i class="fas fa-save"></i> Create Backup
                </button>
                <button id="export-data-btn" class="export-btn">
                    <i class="fas fa-file-export"></i> Export Data
                </button>
                <button id="refresh-btn">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button id="back-btn">
                    <i class="fas fa-arrow-left"></i> Back to POS
                </button>
            </div>
        </header>

        <div class="backup-types">
            <button class="type-btn active" data-type="all">All Backups</button>
            <button class="type-btn" data-type="manual">Manual</button>
            <button class="type-btn" data-type="daily">Daily</button>
            <button class="type-btn" data-type="incremental">Incremental</button>
        </div>

        <div class="main-content">
            <div class="backup-list-container">
                <h2>Available Backups</h2>
                <div id="backup-list" class="backup-list">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading backups...
                    </div>
                </div>
            </div>

            <!-- Data Export Section -->
            <div id="data-export-section" class="data-export-section" style="display: none;">
                <h2><i class="fas fa-file-export"></i> Data Export</h2>
                <p class="section-description">Export your sales data, items, cover charges, and taxes in various formats.</p>

                <div class="export-options-container">
                    <div class="export-form">
                        <div class="form-group">
                            <label for="export-start-date">Start Date</label>
                            <input type="date" id="export-start-date" class="date-input">
                        </div>
                        <div class="form-group">
                            <label for="export-end-date">End Date</label>
                            <input type="date" id="export-end-date" class="date-input">
                        </div>

                        <div class="export-destinations">
                            <h3>Export Destinations</h3>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="export-to-excel" checked>
                                    <label for="export-to-excel">Excel Spreadsheet</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="export-to-email">
                                    <label for="export-to-email">Email</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="export-to-google">
                                    <label for="export-to-google">Google Sheets</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="export-to-print">
                                    <label for="export-to-print">Print</label>
                                </div>
                            </div>
                        </div>

                        <div id="email-export-options" class="conditional-options" style="display: none;">
                            <div class="form-group">
                                <label for="email-recipient">Email Recipient</label>
                                <input type="email" id="email-recipient" placeholder="Enter email address">
                            </div>
                        </div>

                        <div id="google-export-options" class="conditional-options" style="display: none;">
                            <div class="form-group">
                                <label for="google-spreadsheet-id">Google Spreadsheet ID</label>
                                <input type="text" id="google-spreadsheet-id" placeholder="Enter spreadsheet ID">
                            </div>
                            <div class="help-text">
                                <p><i class="fas fa-info-circle"></i> The spreadsheet ID is the long string in the URL of your Google Sheet.</p>
                            </div>
                        </div>

                        <div class="export-actions">
                            <button id="generate-export-btn" class="primary-btn export-btn">
                                <i class="fas fa-file-export"></i> Generate Export
                            </button>
                        </div>
                    </div>

                    <div class="export-info">
                        <h3>What's Included in the Export</h3>
                        <ul class="export-contents">
                            <li><i class="fas fa-check"></i> <strong>Sales Summary:</strong> Total sales, tax, tips, and cover charges</li>
                            <li><i class="fas fa-check"></i> <strong>Daily Sales:</strong> Breakdown of sales by day</li>
                            <li><i class="fas fa-check"></i> <strong>Item Sales:</strong> Detailed list of items sold with quantities and revenue</li>
                            <li><i class="fas fa-check"></i> <strong>Payment Methods:</strong> Sales by payment method (cash, card, etc.)</li>
                            <li><i class="fas fa-check"></i> <strong>Bill Details:</strong> Complete list of all bills in the selected period</li>
                        </ul>

                        <div class="auto-export-settings">
                            <h3>Automatic Exports</h3>
                            <div class="form-group">
                                <label for="auto-export-enabled">Enable Daily Exports</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="auto-export-enabled">
                                    <span class="slider"></span>
                                </div>
                            </div>
                            <div id="auto-export-options" style="display: none;">
                                <div class="form-group">
                                    <label for="auto-export-time">Export Time</label>
                                    <input type="time" id="auto-export-time" value="23:59">
                                </div>
                                <div class="form-group">
                                    <label for="auto-export-email">Send to Email</label>
                                    <input type="email" id="auto-export-email" placeholder="Enter email address">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="backup-details" class="backup-details">
            <h2>Backup Details</h2>

            <div class="details-tabs">
                <button id="tab-files" class="tab-btn active">Files</button>
                <button id="tab-orders" class="tab-btn">Orders</button>
            </div>

            <div id="tab-content-files" class="tab-content details-content active">
                <p>Select a backup to view details</p>
            </div>

            <div id="tab-content-orders" class="tab-content">
                <div class="orders-container">
                    <p>Select a backup to view order data</p>
                </div>
            </div>
        </div>

        <!-- Modal for backup creation -->
        <div id="backup-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Create New Backup</h2>
                <form id="backup-form">
                    <div class="form-group">
                        <label for="backup-type">Backup Type:</label>
                        <select id="backup-type">
                            <option value="manual">Manual Backup</option>
                            <option value="daily">Daily Backup</option>
                            <option value="incremental">Incremental Backup</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="primary-btn">
                            <i class="fas fa-save"></i> Create Backup
                        </button>
                        <button type="button" class="cancel-btn">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modal for restore confirmation -->
        <div id="restore-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Restore from Backup</h2>
                <div class="warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Warning: Restoring from a backup will replace your current data. This action cannot be undone.</p>
                </div>
                <div class="restore-details">
                    <p>You are about to restore from: <span id="restore-backup-name"></span></p>
                    <p>Created on: <span id="restore-backup-date"></span></p>
                </div>
                <div class="form-actions">
                    <button id="confirm-restore-btn" class="danger-btn">
                        <i class="fas fa-check"></i> Yes, Restore
                    </button>
                    <button id="cancel-restore-btn" class="cancel-btn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <div class="back-button-container">
        <button id="back-btn" class="back-btn"><i class="fas fa-arrow-left"></i> Back to Settings</button>
    </div>

    <!-- Order Details Modal -->
    <div id="order-details-modal" class="order-details-modal">
        <div class="order-details-content">
            <span class="close">&times;</span>
            <h2>Order Details</h2>
            <div id="order-details-container">
                <!-- Order details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="success-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <h2>Success!</h2>
                <p id="success-message-text">Your export has been generated successfully.</p>
                <div id="success-actions">
                    <!-- Actions will be added dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div id="error-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <h2>Error</h2>
                <p id="error-message-text">An error occurred while generating your export.</p>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
