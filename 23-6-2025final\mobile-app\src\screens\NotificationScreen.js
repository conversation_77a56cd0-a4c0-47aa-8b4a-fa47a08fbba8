import React, { useContext, useEffect } from 'react';
import { StyleSheet, View, Text, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { NotificationContext } from '../context/NotificationContext';
import { Ionicons } from '@expo/vector-icons';

// Helper function to format time
const formatTimeAgo = (timestamp) => {
  const now = new Date();
  const notificationTime = new Date(timestamp);
  const diffInSeconds = Math.floor((now - notificationTime) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }
  
  // Format as date for older notifications
  return notificationTime.toLocaleDateString();
};

const NotificationItem = ({ item, onPress }) => {
  const { markAsRead } = useContext(NotificationContext);
  
  const handlePress = () => {
    if (!item.read) {
      markAsRead(item.id);
    }
    onPress(item);
  };
  
  // Determine icon based on notification type
  let iconName = 'notifications-outline';
  if (item.data && item.data.type) {
    switch (item.data.type) {
      case 'event':
        iconName = 'calendar-outline';
        break;
      case 'promotion':
        iconName = 'pricetag-outline';
        break;
      case 'reservation':
        iconName = 'time-outline';
        break;
      case 'order':
        iconName = 'restaurant-outline';
        break;
    }
  }
  
  return (
    <TouchableOpacity 
      style={[styles.notificationItem, !item.read && styles.unread]} 
      onPress={handlePress}
    >
      <View style={styles.iconContainer}>
        <Ionicons name={iconName} size={24} color="#6B46C1" />
      </View>
      <View style={styles.contentContainer}>
        <Text style={styles.notificationTitle}>{item.title}</Text>
        <Text style={styles.notificationBody}>{item.body}</Text>
        <Text style={styles.notificationTime}>
          {formatTimeAgo(item.timestamp)}
        </Text>
      </View>
      {!item.read && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );
};

const NotificationScreen = ({ navigation }) => {
  const { notifications, unreadCount, loading, markAllAsRead, refreshNotifications } = useContext(NotificationContext);
  
  useEffect(() => {
    // Refresh notifications when screen is focused
    const unsubscribe = navigation.addListener('focus', () => {
      refreshNotifications();
    });
    
    return unsubscribe;
  }, [navigation, refreshNotifications]);
  
  const handleNotificationPress = (notification) => {
    // Handle different notification types
    if (notification.data && notification.data.type) {
      switch (notification.data.type) {
        case 'event':
          navigation.navigate('EventDetails', { eventId: notification.data.eventId });
          break;
        case 'reservation':
          navigation.navigate('ReservationScreen', { reservationId: notification.data.reservationId });
          break;
        case 'promotion':
          navigation.navigate('HomeScreen', { promotionId: notification.data.promotionId });
          break;
        case 'order':
          navigation.navigate('OrderScreen', { orderId: notification.data.orderId });
          break;
        default:
          // Just stay on the notifications screen
          break;
      }
    }
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6B46C1" />
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      {unreadCount > 0 && (
        <TouchableOpacity style={styles.markAllContainer} onPress={markAllAsRead}>
          <Text style={styles.markAllText}>Mark all as read</Text>
        </TouchableOpacity>
      )}
      
      {notifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="notifications-off-outline" size={64} color="#A0AEC0" />
          <Text style={styles.emptyText}>No notifications yet</Text>
          <Text style={styles.emptySubtext}>We'll notify you when there's something new</Text>
        </View>
      ) : (
        <FlatList
          data={notifications}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <NotificationItem item={item} onPress={handleNotificationPress} />
          )}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F7FAFC',
  },
  markAllContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    alignItems: 'flex-end',
    backgroundColor: 'white',
  },
  markAllText: {
    color: '#6B46C1',
    fontWeight: '500',
  },
  listContainer: {
    flexGrow: 1,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: 'white',
    position: 'relative',
  },
  unread: {
    backgroundColor: '#F0F5FF',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EBF4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D3748',
    marginBottom: 4,
  },
  notificationBody: {
    fontSize: 14,
    color: '#4A5568',
    marginBottom: 8,
  },
  notificationTime: {
    fontSize: 12,
    color: '#718096',
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#6B46C1',
    position: 'absolute',
    top: 16,
    right: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    color: '#2D3748',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptySubtext: {
    color: '#718096',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default NotificationScreen;
