<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Theme Settings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin-top: 50px;
        }
        .message {
            padding: 20px;
            background-color: #d4edda;
            color: #155724;
            border-radius: 5px;
            margin: 20px auto;
            max-width: 500px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Reset Theme Settings</h1>
    <button id="reset-btn">Reset to Light Mode</button>
    <div id="message" class="message" style="display: none;"></div>

    <script>
        document.getElementById('reset-btn').addEventListener('click', function() {
            // Reset theme settings
            localStorage.setItem('theme', 'light');
            
            // Remove any page-specific theme settings
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('dark-mode-') || key.startsWith('theme-')) {
                    localStorage.removeItem(key);
                }
            });
            
            // Show success message
            const messageEl = document.getElementById('message');
            messageEl.textContent = 'Theme settings reset to light mode. Please refresh your application pages.';
            messageEl.style.display = 'block';
        });
    </script>
</body>
</html>
