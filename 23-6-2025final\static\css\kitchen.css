body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

header {
    background-color: #333;
    color: white;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
    border-radius: 5px;
}

.hookah-display header {
    background-color: #8B4513; /* Brown for hookah */
}

.food-display header {
    background-color: #4CAF50; /* Green for food */
}

.orders-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.order-card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 15px;
    border-left: 5px solid #4CAF50;
}

.hookah-display .order-card {
    border-left-color: #8B4513;
}

.order-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-weight: bold;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.table-number {
    color: #333;
    font-size: 1.2em;
}

.order-time {
    color: #666;
    font-size: 0.9em;
}

.order-type {
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 0.8em;
    color: white;
}

.order-type.food {
    background-color: #4CAF50;
}

.order-type.drink {
    background-color: #2196F3;
}

.order-type.dessert {
    background-color: #9C27B0;
}

.order-items {
    margin: 10px 0;
}

.item {
    margin: 5px 0;
    padding: 5px;
    border-radius: 3px;
}

.modifiers {
    margin: 3px 0 3px 20px;
    color: #666;
    font-size: 0.9em;
    list-style-type: circle;
}

.special-notes {
    font-style: italic;
    color: #E91E63;
    margin-top: 10px;
    padding-top: 5px;
    border-top: 1px dashed #ddd;
}

.complete-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    float: right;
}

.complete-btn:hover {
    background-color: #45a049;
}

.station-toggles {
    margin: 10px 0;
}

.station-toggles button {
    padding: 5px 10px;
    margin: 0 5px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.station-toggles button.active {
    background-color: #333;
    color: white;
}

#current-time {
    text-align: right;
    font-size: 0.9em;
    color: #ddd;
}