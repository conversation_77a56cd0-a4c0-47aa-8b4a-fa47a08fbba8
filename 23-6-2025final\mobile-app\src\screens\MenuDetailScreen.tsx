import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Image, Alert } from 'react-native';
import { Text, Button, ActivityIndicator, Chip } from 'react-native-paper';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { menuApi } from '../api/api';

const MenuDetailScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { itemId } = route.params;
  
  const [item, setItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    loadItemDetails();
  }, []);

  const loadItemDetails = async () => {
    try {
      setLoading(true);
      const response = await menuApi.getItemDetails(itemId);
      setItem(response.data.item);
    } catch (error) {
      console.error('Error loading item details:', error);
      Alert.alert('Error', 'Failed to load item details');
    } finally {
      setLoading(false);
    }
  };

  const increaseQuantity = () => {
    setQuantity(quantity + 1);
  };

  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const addToOrder = () => {
    // This would typically add the item to a cart context or navigate to an order screen
    Alert.alert(
      'Added to Order',
      `${quantity} x ${item.name} added to your order.`,
      [
        {
          text: 'Continue Shopping',
          style: 'cancel',
        },
        {
          text: 'View Order',
          onPress: () => navigation.navigate('Order'),
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6B46C1" />
      </View>
    );
  }

  if (!item) {
    return (
      <View style={styles.errorContainer}>
        <Text>Item not found</Text>
        <Button mode="contained" onPress={() => navigation.goBack()} style={styles.backButton}>
          Go Back
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {item.image ? (
        <Image source={{ uri: item.image }} style={styles.image} resizeMode="cover" />
      ) : (
        <View style={styles.placeholderImage}>
          <Ionicons name="restaurant-outline" size={80} color="#A0AEC0" />
        </View>
      )}

      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.price}>${item.price.toFixed(2)}</Text>
        </View>

        <Text style={styles.description}>{item.description}</Text>

        {item.category && (
          <Chip style={styles.categoryChip} textStyle={styles.categoryText}>
            {item.category}
          </Chip>
        )}

        {item.options && item.options.length > 0 && (
          <View style={styles.optionsContainer}>
            <Text style={styles.sectionTitle}>Options</Text>
            {item.options.map((option, index) => (
              <View key={index} style={styles.optionItem}>
                <Text style={styles.optionName}>{option.name}</Text>
                <Text style={styles.optionPrice}>+${option.price.toFixed(2)}</Text>
              </View>
            ))}
          </View>
        )}

        <View style={styles.quantityContainer}>
          <Text style={styles.sectionTitle}>Quantity</Text>
          <View style={styles.quantityControls}>
            <Button
              mode="outlined"
              onPress={decreaseQuantity}
              disabled={quantity <= 1}
              style={styles.quantityButton}
            >
              <Ionicons name="remove" size={16} color={quantity <= 1 ? '#A0AEC0' : '#6B46C1'} />
            </Button>
            <Text style={styles.quantityText}>{quantity}</Text>
            <Button
              mode="outlined"
              onPress={increaseQuantity}
              style={styles.quantityButton}
            >
              <Ionicons name="add" size={16} color="#6B46C1" />
            </Button>
          </View>
        </View>

        <Button
          mode="contained"
          onPress={addToOrder}
          style={styles.addButton}
          labelStyle={styles.addButtonLabel}
        >
          Add to Order - ${(item.price * quantity).toFixed(2)}
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  image: {
    width: '100%',
    height: 250,
  },
  placeholderImage: {
    width: '100%',
    height: 250,
    backgroundColor: '#EDF2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2D3748',
    flex: 1,
  },
  price: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#6B46C1',
  },
  description: {
    fontSize: 16,
    color: '#4A5568',
    lineHeight: 24,
    marginBottom: 20,
  },
  categoryChip: {
    alignSelf: 'flex-start',
    marginBottom: 20,
    backgroundColor: '#EDF2F7',
  },
  categoryText: {
    color: '#4A5568',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D3748',
    marginBottom: 12,
  },
  optionsContainer: {
    marginBottom: 20,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#EDF2F7',
  },
  optionName: {
    fontSize: 16,
    color: '#4A5568',
  },
  optionPrice: {
    fontSize: 16,
    color: '#6B46C1',
    fontWeight: '500',
  },
  quantityContainer: {
    marginBottom: 24,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    borderColor: '#E2E8F0',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 16,
    minWidth: 30,
    textAlign: 'center',
  },
  addButton: {
    backgroundColor: '#6B46C1',
    paddingVertical: 8,
  },
  addButtonLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    marginTop: 16,
    backgroundColor: '#6B46C1',
  },
});

export default MenuDetailScreen;
