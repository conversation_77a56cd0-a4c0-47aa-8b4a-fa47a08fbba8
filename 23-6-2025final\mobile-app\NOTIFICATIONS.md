# Notification System for Paradiz Hookah Lounge Mobile App

This document provides an overview of the notification system implemented in the Paradiz Hookah Lounge mobile app.

## Features

The notification system includes the following features:

1. **Push Notifications**: Receive notifications even when the app is not active
2. **In-App Notifications**: View and manage notifications within the app
3. **Event Reminders**: Set reminders for upcoming events
4. **Notification Badge**: See the number of unread notifications
5. **Event Banners**: Promotional banners for upcoming events

## Components

The notification system consists of the following components:

### 1. NotificationService

Located at `src/utils/NotificationService.js`, this service handles:
- Requesting notification permissions
- Scheduling local notifications
- Managing notification badges
- Setting up notification channels (Android)

### 2. NotificationContext

Located at `src/context/NotificationContext.js`, this context provider:
- Manages notification state across the app
- Handles fetching notifications from the server
- Provides methods for marking notifications as read
- Manages push token registration

### 3. NotificationBell

Located at `src/components/NotificationBell.js`, this component:
- Displays a bell icon with a badge showing unread count
- Navigates to the notifications screen when pressed

### 4. NotificationScreen

Located at `src/screens/NotificationScreen.js`, this screen:
- Displays a list of all notifications
- Allows marking individual or all notifications as read
- Handles navigation based on notification type

### 5. EventDetailScreen

Located at `src/screens/EventDetailScreen.js`, this screen:
- Displays event details
- Allows setting reminders for events
- Provides RSVP functionality

## API Integration

The notification system integrates with the following API endpoints:

- `GET /api/notifications` - Fetch all notifications
- `PUT /api/notifications/:id/read` - Mark a notification as read
- `PUT /api/notifications/read-all` - Mark all notifications as read
- `POST /api/users/push-token` - Register a device for push notifications
- `GET /api/events` - Fetch all events
- `GET /api/events/:id` - Fetch event details
- `POST /api/events/:id/reminder` - Set or remove an event reminder

## Usage

### Displaying the Notification Bell

Add the NotificationBell component to your screen's header:

```javascript
import NotificationBell from '../components/NotificationBell';

// In your component
useEffect(() => {
  navigation.setOptions({
    headerRight: () => (
      <NotificationBell onPress={() => navigation.navigate('Notifications')} />
    ),
  });
}, [navigation]);
```

### Scheduling a Notification

Use the NotificationContext to schedule event reminders:

```javascript
import { useContext } from 'react';
import { NotificationContext } from '../context/NotificationContext';

// In your component
const { scheduleEventReminder } = useContext(NotificationContext);

// Schedule a reminder for an event
const handleSetReminder = async (event) => {
  const notificationId = await scheduleEventReminder(event);
  // Save the notificationId if you need to cancel it later
};
```

### Handling Notification Taps

Notification taps are handled automatically by the NotificationContext. When a user taps on a notification, they will be navigated to the appropriate screen based on the notification type.

## Testing

For testing purposes, mock data is provided in `src/api/mockData.js`. This data is used when the app is running in development mode.

To test the notification system:

1. Run the app in development mode
2. Navigate to the home screen to see the event banner
3. Tap the notification bell to see the notifications screen
4. Tap on a notification to navigate to the relevant screen
5. Set a reminder for an event to test the scheduling functionality

## Customization

To customize the notification appearance:

1. Update the notification icons in `assets/images/notification-icon.png`
2. Modify the notification sounds in `assets/sounds/notification.wav`
3. Update the notification colors in `app.config.js`

## Production Deployment

Before deploying to production:

1. Set up a Firebase Cloud Messaging (FCM) project for Android
2. Set up Apple Push Notification Service (APNS) for iOS
3. Configure your server to send push notifications
4. Update the `projectId` in `app.config.js` with your Expo project ID
