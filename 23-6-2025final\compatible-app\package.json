{"name": "compatible-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~48.0.21", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.14", "react-native-web": "~0.18.10", "react-dom": "18.2.0", "@expo/webpack-config": "^18.0.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}