<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Items Management - Paradiz <PERSON></title>
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles.css">
    <script src="../theme.js"></script>
</head>
<body>
    <div class="container">
        <h1>Items Management</h1>

        <div class="items-container">
            <div class="instructions">
                <p>Use this page to manage your menu items. Click <strong>Edit</strong> to modify an item's name, price, category, or description. Click <strong>Delete</strong> to remove an item.</p>
            </div>
            <div class="items-controls">
                <button id="add-item-btn" class="primary-btn">Add New Item</button>
                <div class="search-container">
                    <input type="text" id="search-input" placeholder="Search items...">
                    <button id="search-btn">Search</button>
                </div>
            </div>

            <div class="category-filter">
                <label for="category-select">Filter by Category:</label>
                <select id="category-select">
                    <option value="">All Categories</option>
                    <!-- Categories will be loaded here dynamically -->
                </select>
            </div>

            <div class="items-list-container">
                <table id="items-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price ($)</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="items-list">
                        <!-- Items will be loaded here dynamically -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Add Item Modal -->
        <div id="add-item-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2 id="modal-title">Add New Item</h2>
                <form id="item-form">
                    <input type="hidden" id="item-id">
                    <div class="form-group">
                        <label for="item-name">Name:</label>
                        <input type="text" id="item-name" required>
                    </div>
                    <div class="form-group">
                        <label for="item-category">Category:</label>
                        <select id="item-category" required>
                            <option value="">Select Category</option>
                            <!-- Categories will be loaded here dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="item-price">Price ($):</label>
                        <input type="number" id="item-price" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="item-description">Description:</label>
                        <textarea id="item-description" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="primary-btn">💾 Save Changes</button>
                        <button type="button" id="cancel-item" class="secondary-btn">❌ Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Confirm Delete Modal -->
        <div id="confirm-delete-modal" class="modal">
            <div class="modal-content delete-modal">
                <h2><span class="warning-icon">⚠️</span> Confirm Delete</h2>
                <p class="delete-warning">Are you sure you want to delete this item? This action cannot be undone.</p>
                <div class="form-actions">
                    <button id="confirm-delete-btn" class="danger-btn">🗑️ Delete</button>
                    <button id="cancel-delete-btn" class="secondary-btn">✖ Cancel</button>
                </div>
            </div>
        </div>

        <div class="back-button-container">
            <button id="back-btn" class="back-btn">Back to Settings</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
