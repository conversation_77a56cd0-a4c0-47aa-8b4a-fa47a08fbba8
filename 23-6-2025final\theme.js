/**
 * Global Theme Management System
 * This script handles theme switching between light and dark modes
 * across the entire application.
 */

// Execute when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Apply the saved theme or default to light
    applyTheme();
    
    // Check if we're on the display settings page
    const themeRadios = document.querySelectorAll('input[name="theme"]');
    if (themeRadios.length > 0) {
        // We're on the display settings page, set up the radio buttons
        const savedTheme = localStorage.getItem('theme') || 'light';
        
        // Set the correct radio button as checked
        document.getElementById(`theme-${savedTheme}`).checked = true;
        
        // Add event listeners to the radio buttons
        themeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    setTheme(this.value);
                }
            });
        });
    }
});

/**
 * Sets the theme and saves the preference to localStorage
 * @param {string} theme - The theme to set ('light' or 'dark')
 */
function setTheme(theme) {
    localStorage.setItem('theme', theme);
    applyTheme();
}

/**
 * Applies the current theme from localStorage
 */
function applyTheme() {
    const theme = localStorage.getItem('theme') || 'light';
    
    if (theme === 'dark') {
        document.body.classList.add('dark-theme');
    } else {
        document.body.classList.remove('dark-theme');
    }
    
    // Set the theme color meta tag for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
        metaThemeColor.setAttribute('content', theme === 'dark' ? '#1a1a1a' : '#ffffff');
    }
    
    // Dispatch a custom event that other scripts can listen for
    document.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
}

/**
 * Toggles between light and dark themes
 * This can be called from any page to switch themes
 */
function toggleTheme() {
    const currentTheme = localStorage.getItem('theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    return newTheme; // Return the new theme for any callbacks
}

// Make functions available globally
window.setTheme = setTheme;
window.applyTheme = applyTheme;
window.toggleTheme = toggleTheme;
