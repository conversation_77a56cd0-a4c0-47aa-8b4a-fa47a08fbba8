import React, { useState, useContext, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, TouchableOpacity } from 'react-native';
import { Text, Button, Card, Avatar, TextInput, Divider, ActivityIndicator, List } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { AuthContext } from '../context/AuthContext';
import { userApi, orderApi } from '../api/api';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const { user, signOut } = useContext(AuthContext);
  
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [pastOrders, setPastOrders] = useState([]);
  const [loadingOrders, setLoadingOrders] = useState(false);

  useEffect(() => {
    if (user) {
      setName(user.name);
      setEmail(user.email);
      loadPastOrders();
    }
  }, [user]);

  const loadPastOrders = async () => {
    if (!user) return;

    setLoadingOrders(true);
    try {
      // In a real app, this would call the API
      // const response = await orderApi.getUserOrders();
      // setPastOrders(response.data.orders);

      // Simulate API call
      setTimeout(() => {
        setPastOrders([
          { id: 123, date: '2023-04-10', total: 65.97, status: 'completed' },
          { id: 122, date: '2023-04-03', total: 42.98, status: 'completed' },
          { id: 121, date: '2023-03-27', total: 89.95, status: 'completed' },
        ]);
        setLoadingOrders(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading past orders:', error);
      Alert.alert('Error', 'Failed to load your order history');
      setLoadingOrders(false);
    }
  };

  const handleUpdateProfile = async () => {
    if (!name || !email) {
      Alert.alert('Error', 'Name and email are required');
      return;
    }

    setLoading(true);
    try {
      // In a real app, this would call the API
      // const response = await userApi.updateProfile({ name, email });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update local user state
      // This would typically be handled by the AuthContext
      
      setEditMode(false);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          onPress: signOut,
          style: 'destructive',
        },
      ]
    );
  };

  const getInitials = (name) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  if (!user) {
    return (
      <View style={styles.container}>
        <View style={styles.notLoggedInContainer}>
          <Text style={styles.notLoggedInText}>Please log in to view your profile</Text>
          <Button
            mode="contained"
            onPress={() => navigation.navigate('Login')}
            style={styles.loginButton}
          >
            Log In
          </Button>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.profileCard}>
        <Card.Content>
          <View style={styles.profileHeader}>
            <Avatar.Text
              size={80}
              label={getInitials(user.name)}
              style={styles.avatar}
            />
            {!editMode ? (
              <View style={styles.profileInfo}>
                <Text style={styles.nameText}>{user.name}</Text>
                <Text style={styles.emailText}>{user.email}</Text>
                <Button
                  mode="outlined"
                  onPress={() => setEditMode(true)}
                  style={styles.editButton}
                >
                  Edit Profile
                </Button>
              </View>
            ) : (
              <View style={styles.editForm}>
                <TextInput
                  label="Name"
                  value={name}
                  onChangeText={setName}
                  mode="outlined"
                  style={styles.input}
                />
                <TextInput
                  label="Email"
                  value={email}
                  onChangeText={setEmail}
                  mode="outlined"
                  style={styles.input}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
                <View style={styles.editActions}>
                  <Button
                    mode="outlined"
                    onPress={() => {
                      setName(user.name);
                      setEmail(user.email);
                      setEditMode(false);
                    }}
                    style={styles.cancelButton}
                  >
                    Cancel
                  </Button>
                  <Button
                    mode="contained"
                    onPress={handleUpdateProfile}
                    style={styles.saveButton}
                    loading={loading}
                    disabled={loading}
                  >
                    Save
                  </Button>
                </View>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.sectionCard}>
        <Card.Title title="Order History" />
        <Card.Content>
          {loadingOrders ? (
            <ActivityIndicator size="small" color="#6B46C1" style={styles.loadingIndicator} />
          ) : pastOrders.length > 0 ? (
            pastOrders.map((order) => (
              <TouchableOpacity
                key={order.id}
                onPress={() => navigation.navigate('OrderDetails', { orderId: order.id })}
              >
                <View style={styles.orderItem}>
                  <View>
                    <Text style={styles.orderDate}>{order.date}</Text>
                    <Text style={styles.orderNumber}>Order #{order.id}</Text>
                  </View>
                  <View style={styles.orderRight}>
                    <Text style={styles.orderTotal}>${order.total.toFixed(2)}</Text>
                    <Ionicons name="chevron-forward" size={20} color="#A0AEC0" />
                  </View>
                </View>
                <Divider style={styles.divider} />
              </TouchableOpacity>
            ))
          ) : (
            <Text style={styles.noOrdersText}>No order history found</Text>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.sectionCard}>
        <Card.Title title="Settings" />
        <Card.Content>
          <List.Item
            title="Notifications"
            left={props => <List.Icon {...props} icon="bell-outline" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Notifications', 'Notification settings would go here')}
          />
          <Divider />
          <List.Item
            title="Payment Methods"
            left={props => <List.Icon {...props} icon="credit-card-outline" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Payment Methods', 'Payment method settings would go here')}
          />
          <Divider />
          <List.Item
            title="Privacy Policy"
            left={props => <List.Icon {...props} icon="shield-outline" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Privacy Policy', 'Privacy policy would go here')}
          />
          <Divider />
          <List.Item
            title="Terms of Service"
            left={props => <List.Icon {...props} icon="file-document-outline" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Terms of Service', 'Terms of service would go here')}
          />
        </Card.Content>
      </Card>

      <Button
        mode="outlined"
        onPress={handleSignOut}
        style={styles.signOutButton}
        color="#E53E3E"
      >
        Sign Out
      </Button>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  notLoggedInContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  notLoggedInText: {
    fontSize: 18,
    color: '#4A5568',
    marginBottom: 20,
  },
  loginButton: {
    backgroundColor: '#6B46C1',
  },
  profileCard: {
    margin: 16,
    elevation: 4,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: '#6B46C1',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  nameText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  emailText: {
    fontSize: 16,
    color: '#4A5568',
    marginBottom: 8,
  },
  editButton: {
    alignSelf: 'flex-start',
    borderColor: '#6B46C1',
  },
  editForm: {
    flex: 1,
  },
  input: {
    marginBottom: 12,
    backgroundColor: 'white',
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: '#6B46C1',
  },
  sectionCard: {
    margin: 16,
    marginTop: 0,
    elevation: 4,
  },
  loadingIndicator: {
    margin: 16,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  orderDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  orderNumber: {
    fontSize: 14,
    color: '#4A5568',
  },
  orderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B46C1',
    marginRight: 8,
  },
  divider: {
    backgroundColor: '#E2E8F0',
  },
  noOrdersText: {
    textAlign: 'center',
    color: '#4A5568',
    padding: 16,
  },
  signOutButton: {
    margin: 16,
    marginBottom: 32,
    borderColor: '#E53E3E',
  },
});

export default ProfileScreen;
