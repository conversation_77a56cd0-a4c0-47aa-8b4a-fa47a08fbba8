.header-with-back {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.back-button {
    background-color: transparent;
    border: none;
    font-size: 24px;
    cursor: pointer;
    margin-right: 15px;
    color: var(--text-color);
}

.settings-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: var(--card-shadow);
}

.settings-section h2 {
    margin-top: 0;
    color: var(--heading-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.settings-description {
    margin-bottom: 20px;
    color: var(--text-muted);
}

.settings-section {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: var(--card-shadow);
}

.settings-section h2 {
    margin-top: 0;
    color: var(--heading-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-bg);
    color: var(--text-color);
}

.form-hint {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 5px;
}

.template-preview {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
}

.preview-container {
    background-color: #ECE5DD; /* WhatsApp chat background color */
    border-radius: 8px;
    padding: 15px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #000;
    max-width: 400px;
    margin: 0 auto;
}

.preview-container .business-logo {
    text-align: center;
    margin-bottom: 10px;
}

.preview-container .business-name {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 5px;
}

.preview-container .business-tagline {
    font-size: 14px;
    text-align: center;
    margin-bottom: 10px;
    font-style: italic;
}

.preview-container .receipt-header {
    border-top: 1px dashed #999;
    border-bottom: 1px dashed #999;
    padding: 10px 0;
    margin-bottom: 10px;
}

.preview-container .receipt-items {
    margin-bottom: 10px;
}

.preview-container .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.preview-container .receipt-total {
    font-weight: bold;
    text-align: right;
    margin-top: 10px;
    margin-bottom: 10px;
}

.preview-container .receipt-footer {
    text-align: center;
    font-size: 12px;
    margin-top: 15px;
    color: #555;
}

.preview-container .qr-code {
    text-align: center;
    margin-top: 10px;
}

.settings-actions {
    margin-top: 30px;
    text-align: center;
}

.save-btn {
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.save-btn:hover {
    background-color: var(--button-primary-hover);
}

.primary-btn {
    background-color: #25D366; /* WhatsApp green */
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.primary-btn:hover {
    background-color: #128C7E; /* Darker WhatsApp green */
}

.status-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
    display: none;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}
