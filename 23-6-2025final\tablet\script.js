// Load menu items from JSON file
let menuItems = {};

// Fetch and load menu items
async function loadMenuItems() {
    try {
        console.log('Loading menu items from items.json...');
        // Add cache-busting parameter to force fresh data
        const timestamp = new Date().getTime();
        const response = await fetch(`http://localhost:5000/items.json?t=${timestamp}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log('Received data:', data);

        // Convert the JSON structure to match our existing menuItems format
        menuItems = {
            'hookahs': data.categories.hookahs.items.map(item => {
                console.log('Processing hookah item:', item);
                return {
                    id: item.id,
                    name: item.name,
                    price: parseFloat(item.price)
                };
            }),
            'hot-drinks': data.categories['hot-drinks'].items.map(item => ({
                name: item.name,
                price: parseFloat(item.price)
            })),
            'cold-drinks': data.categories['cold-drinks'].items.map(item => ({
                name: item.name,
                price: parseFloat(item.price)
            })),
            'food': data.categories.food.items.map(item => ({
                name: item.name,
                price: parseFloat(item.price)
            })),
            'desserts': data.categories.desserts.items.map(item => ({
                name: item.name,
                price: parseFloat(item.price)
            }))
        };

        console.log('Processed menu items:', menuItems);
        console.log('Hookah items specifically:', menuItems.hookahs);

        // Debug: Show hookah count for troubleshooting
        const hookahCount = menuItems.hookahs ? menuItems.hookahs.length : 0;
        const flavorCount = menuItems.hookahs ? menuItems.hookahs.filter(item => item.id && item.id.startsWith('f')).length : 0;

        // Temporary debug alert - remove this after testing
        alert(`Debug: Loaded ${hookahCount} hookah items, ${flavorCount} flavors`);

        // Initialize the app after loading items
        setupTableButtons();
        setupCustomerNameInput();
        setupCategories();
        setupHookahModal();
        setupSendOrderButton();
        setupCoverCharge();
    } catch (error) {
        console.error('Error loading menu items:', error);
        alert('Error loading menu items: ' + error.message + '. Please refresh the page.');
    }
}

// Wait for DOM to be fully loaded before starting
document.addEventListener('DOMContentLoaded', loadMenuItems);

// Setup table buttons
function setupTableButtons() {
    const tableButtonsContainer = document.getElementById('table-buttons');
    for (let i = 1; i <= 15; i++) {
        const button = document.createElement('button');
        button.className = 'table-btn';
        button.innerHTML = `<i class="fas fa-table"></i> ${i}`;
        button.addEventListener('click', () => updateTableNumber(i));
        tableButtonsContainer.appendChild(button);
    }
}

// Update table number in the UI
function updateTableNumber(tableNumber) {
    document.getElementById('table-number').textContent = tableNumber;
    document.getElementById('summary-table').textContent = tableNumber;
    document.getElementById('middle-table-number').textContent = tableNumber;
}

// Setup customer name input
function setupCustomerNameInput() {
    const customerNameInput = document.getElementById('customer-name');

    // Initialize virtual keyboard
    const virtualKeyboard = new VirtualKeyboard(customerNameInput);

    // Clear the input when focused to prevent showing previous names
    customerNameInput.addEventListener('focus', () => {
        // Clear the input field when focused
        customerNameInput.value = '';
        // Also update the display areas
        document.getElementById('summary-customer').textContent = '';
        document.getElementById('middle-customer-name').textContent = '';
    });

    customerNameInput.addEventListener('input', () => {
        const customerName = customerNameInput.value;
        document.getElementById('summary-customer').textContent = customerName;
        document.getElementById('middle-customer-name').textContent = customerName;
    });
}

// Setup category buttons
function setupCategories() {
    const categoriesContainer = document.getElementById('categories');
    const categories = ['hookah', 'hot-drinks', 'cold-drinks', 'food', 'desserts'];
    categories.forEach(category => {
        const button = document.createElement('button');
        button.className = 'category-btn';

        // Add appropriate icon based on category
        let icon = '';
        switch(category) {
            case 'hookah':
                icon = '<i class="fas fa-smoking"></i> ';
                break;
            case 'hot-drinks':
                icon = '<i class="fas fa-mug-hot"></i> ';
                break;
            case 'cold-drinks':
                icon = '<i class="fas fa-glass-martini-alt"></i> ';
                break;
            case 'food':
                icon = '<i class="fas fa-utensils"></i> ';
                break;
            case 'desserts':
                icon = '<i class="fas fa-ice-cream"></i> ';
                break;
            default:
                icon = '<i class="fas fa-list"></i> ';
        }

        button.innerHTML = icon + category.replace('-', ' ');
        button.addEventListener('click', () => showCategory(category));
        categoriesContainer.appendChild(button);
    });
}

// Show items for the selected category
function showCategory(category) {
    const itemsGrid = document.getElementById('items-grid');
    itemsGrid.innerHTML = '';

    if (category === 'hookah') {
        showHookahModal();
        return;
    }

    const items = menuItems[category];
    if (items) {
        items.forEach(item => {
            const button = document.createElement('button');
            button.className = 'item-btn';
            button.textContent = `${item.name} - $${item.price}`;
            button.addEventListener('click', () => addToOrder(item.name, item.price));
            itemsGrid.appendChild(button);
        });
    }
}

// Add item to the order list
function addToOrder(itemName, price) {
    const orderList = document.getElementById('order-list');
    const listItem = document.createElement('li');
    listItem.innerHTML = `
        <span>${itemName} - $${price}</span>
        <button class="remove-btn" onclick="this.parentElement.remove()"><i class="fas fa-trash-alt"></i></button>
    `;
    orderList.appendChild(listItem);
}

// Setup send order button
function setupSendOrderButton() {
    const sendButton = document.getElementById('send-btn');
    if (sendButton) {
        sendButton.addEventListener('click', sendOrder);
    }
}

// Setup cover charge functionality
function setupCoverCharge() {
    const coverChargeToggle = document.getElementById('cover-charge-toggle');
    const personCountContainer = document.getElementById('person-count-container');
    const personCount = document.getElementById('person-count');
    const decreaseBtn = document.getElementById('decrease-count');
    const increaseBtn = document.getElementById('increase-count');

    // Show/hide person count based on cover charge selection
    coverChargeToggle.addEventListener('change', () => {
        const selectedValue = parseInt(coverChargeToggle.value);
        personCountContainer.style.display = selectedValue > 0 ? 'block' : 'none';
        updateCoverCharge();
    });

    // Handle decrease button
    decreaseBtn.addEventListener('click', () => {
        const currentValue = parseInt(personCount.value);
        if (currentValue > 1) {
            personCount.value = currentValue - 1;
            updateCoverCharge();
        }
    });

    // Handle increase button
    increaseBtn.addEventListener('click', () => {
        const currentValue = parseInt(personCount.value);
        if (currentValue < 20) {
            personCount.value = currentValue + 1;
            updateCoverCharge();
        }
    });

    // Handle manual input
    personCount.addEventListener('change', () => {
        let value = parseInt(personCount.value);
        if (value < 1) value = 1;
        if (value > 20) value = 20;
        personCount.value = value;
        updateCoverCharge();
    });
}

// Update cover charge in order list
function updateCoverCharge() {
    const coverChargeToggle = document.getElementById('cover-charge-toggle');
    const personCount = document.getElementById('person-count');
    const orderList = document.getElementById('order-list');

    // Remove existing cover charge
    const existingCoverCharge = orderList.querySelector('.cover-charge-item');
    if (existingCoverCharge) {
        existingCoverCharge.remove();
    }

    // Add new cover charge if applicable
    const chargePerPerson = parseInt(coverChargeToggle.value);
    if (chargePerPerson > 0) {
        const numberOfPeople = parseInt(personCount.value);
        const totalCharge = chargePerPerson * numberOfPeople;

        const listItem = document.createElement('li');
        listItem.className = 'cover-charge-item';
        listItem.innerHTML = `
            <span>Cover Charge (${numberOfPeople} × $${chargePerPerson}) - $${totalCharge}</span>
            <button class="remove-btn" onclick="this.parentElement.remove()"><i class="fas fa-trash-alt"></i></button>
        `;
        orderList.insertBefore(listItem, orderList.firstChild);
    }
}

// Send order to the server
async function sendOrder() {
    const orderList = document.getElementById('order-list');
    const tableNumber = document.getElementById('table-number').textContent;
    const customerName = document.getElementById('customer-name').value;
    const coverChargeToggle = document.getElementById('cover-charge-toggle');
    const personCount = document.getElementById('person-count');

    // Check if table number and customer name are valid
    if (tableNumber === '--' || !customerName) {
        alert('Please select a table and enter a customer name before sending the order.');
        return;
    }

    // Validate cover charge if selected
    if (parseInt(coverChargeToggle.value) > 0 && parseInt(personCount.value) < 1) {
        alert('Please enter a valid number of people for the cover charge.');
        return;
    }

    // Get the list of items in the order
    const items = Array.from(orderList.children).map(li => {
        const itemText = li.querySelector('span').textContent;
        const [name, price] = itemText.split(' - $');
        return { name, price: parseFloat(price) };
    });

    try {
        // Prepare cover charge data
        const coverChargeData = {
            pricePerPerson: parseInt(coverChargeToggle.value),
            numberOfPeople: parseInt(personCount.value),
            total: parseInt(coverChargeToggle.value) * parseInt(personCount.value)
        };

        // Send the order to the server
        const response = await fetch('http://localhost:5000/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                tableNumber,
                customerName,
                items,
                coverCharge: coverChargeData
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to send order');
        }

        // Reset the tablet for the next order
        orderList.innerHTML = '';
        document.getElementById('customer-name').value = '';
        document.getElementById('cover-charge-toggle').value = '0';
        document.getElementById('person-count').value = '1';
        document.getElementById('person-count-container').style.display = 'none';

        // Update table number displays
        const tableNumberElements = [
            document.getElementById('table-number'),
            document.getElementById('middle-table-number'),
            document.getElementById('summary-table')
        ];
        tableNumberElements.forEach(el => {
            if (el) el.textContent = '--';
        });

        // Update customer name displays
        const customerNameElements = [
            document.getElementById('middle-customer-name'),
            document.getElementById('summary-customer')
        ];
        customerNameElements.forEach(el => {
            if (el) el.textContent = '--';
        });

        // Reset the category display
        const itemsGrid = document.getElementById('items-grid');
        if (itemsGrid) {
            itemsGrid.innerHTML = '<p>Select a category to see items</p>';
        }

        alert('Order sent successfully!');
    } catch (error) {
        console.error('Error sending order:', error);
        alert('Failed to send order: ' + error.message);
    }
}

// Setup hookah modal
function setupHookahModal() {
    const modal = document.getElementById('hookah-modal');
    const closeBtn = document.getElementById('close-modal');
    const addHookahBtn = document.getElementById('add-hookah-btn');
    const flavorGrid = document.getElementById('flavor-grid');

    // Close modal when clicking outside
    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeModal();
        }
    });

    // Close modal when clicking the close button
    closeBtn.addEventListener('click', closeModal);

    // Add hookah to order
    addHookahBtn.addEventListener('click', addCustomHookah);

    // Generate hookah flavor checkboxes from items.json
    const flavors = menuItems.hookahs
        .filter(item => item.id && item.id.startsWith('f')) // Only get flavor items
        .map(item => item.name);

    // Debug: Show flavor information
    alert(`Debug: Found ${flavors.length} flavors: ${flavors.join(', ')}`);

    flavors.forEach(flavor => {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = flavor.toLowerCase().replace(' ', '-');
        checkbox.value = flavor;

        const label = document.createElement('label');
        label.htmlFor = checkbox.id;
        label.textContent = flavor;

        flavorGrid.appendChild(checkbox);
        flavorGrid.appendChild(label);
    });

    // Update selected count when checkboxes change
    flavorGrid.addEventListener('change', updateSelectedCount);
}

// Show the hookah modal
function showHookahModal() {
    const hookahModal = document.getElementById('hookah-modal');
    hookahModal.style.display = 'block';
    updateSelectedCount();
}

// Close the hookah modal
function closeModal() {
    const hookahModal = document.getElementById('hookah-modal');
    hookahModal.style.display = 'none';
    document.querySelectorAll('.flavor-grid input[type="checkbox"]').forEach(cb => {
        cb.checked = false;
    });
    updateSelectedCount();
}

// Update selected flavor count
function updateSelectedCount() {
    const selectedCount = document.querySelectorAll('.flavor-grid input[type="checkbox"]:checked').length;
    document.getElementById('selected-count').textContent = selectedCount;
    const checkboxes = document.querySelectorAll('.flavor-grid input[type="checkbox"]:not(:checked)');
    checkboxes.forEach(cb => {
        cb.disabled = selectedCount >= 3;
    });
    document.getElementById('add-hookah-btn').disabled = selectedCount === 0;
}

// Add custom hookah to order
function addCustomHookah() {
    const selectedFlavors = Array.from(document.querySelectorAll('.flavor-grid input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    if (selectedFlavors.length === 0) {
        alert('Please select at least one flavor');
        return;
    }

    if (selectedFlavors.length > 3) {
        alert('Maximum 3 flavors allowed');
        return;
    }

    // Check if we've reached the hookah limit
    const currentHookahCount = countHookahItemsInOrder();
    if (currentHookahCount >= 50) {
        alert('Maximum 50 hookah items allowed per order');
        return;
    }

    // Get the base hookah price
    const baseHookah = menuItems.hookahs.find(h => h.name === 'Hookah');
    if (!baseHookah) {
        console.error('Could not find hookah price');
        return;
    }

    const hookahItem = {
        name: selectedFlavors.join(' + '),
        price: baseHookah.price
    };

    const orderList = document.getElementById('order-list');
    const li = document.createElement('li');
    li.innerHTML = `
        <span>${hookahItem.name} - $${hookahItem.price}</span>
        <button class="remove-btn" onclick="this.parentElement.remove()"><i class="fas fa-trash-alt"></i></button>
    `;
    orderList.appendChild(li);
    closeModal();
}

// Initialize the app
document.addEventListener('DOMContentLoaded', function () {
    // Add POS button event listener
    const posButton = document.getElementById('pos-button');
    if (posButton) {
        posButton.addEventListener('click', () => {
            window.location.href = '../pos/index.html';
        });
    }

    // ... existing initialization code ...
});