<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS System</title>
    <link rel="stylesheet" href="styles.css">
    <script src="../theme.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Table Buttons Section -->
        <section class="table-buttons">
            <h3>Select Table</h3>
            <div class="button-row" id="table-buttons-row1">
                <!-- First Row: Tables 1 to 8 -->
            </div>
            <div class="button-row" id="table-buttons-row2">
                <!-- Second Row: Tables 9 to 15 -->
            </div>
        </section>

        <!-- Table Summary -->
        <section class="table-summary">
            <h3>Table Summary</h3>
            <div id="customer-bills" class="customer-bills-container"></div>

            <!-- Bottom Section: Total Bill, Pay Table, Backup, Clear Table, Back to Ordering -->
            <div class="bottom-section">
                <!-- Refresh Button -->
                <button id="refresh-orders-btn" class="refresh-btn"><i class="fas fa-sync-alt"></i> All Tables</button>

                <!-- Total Bill -->
                <button class="total-bill-btn"><i class="fas fa-calculator"></i> Total Bill: <span id="table-total-amount">$0</span></button>

                <!-- Hookah -->
                <button class="pay-table-btn" id="pay-table-btn"><i class="fas fa-smoking"></i> Hookah</button>

                <!-- Food-Drink -->
                <button class="mis-btn" id="backup-btn"><i class="fas fa-utensils"></i> Food-Drink</button>

                <!-- Main -->
                <button class="mis-btn" id="backup-viewer-btn"><i class="fas fa-home"></i> Main</button>

                <!-- Clear Table -->
                <button class="mis-btn" id="clear-table-btn"><i class="fas fa-trash-alt"></i> Clear Table</button>

                <!-- Order -->
                <button class="mis-btn" id="back-to-ordering-btn"><i class="fas fa-shopping-cart"></i> Order</button>
            </div>
        </section>
    </div>

    <!-- Modal for Splitting -->
    <div id="splitModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-left">
                <h2>Split Item</h2>
                <p>Select the bills to split <strong id="selectedItemName"></strong> with:</p>
                <div id="billSelection"></div>
                <button id="confirmSplit"><i class="fas fa-check"></i> Confirm Split</button>
                <button id="undoSplit" style="display: none;"><i class="fas fa-undo"></i> Undo Last Split</button>
            </div>
            <div class="modal-right">
                <h3>Split Type</h3>
                <div id="splitOptions">
                    <label for="equal">
                        <input type="radio" name="splitType" value="equal" id="equal" checked> Equal Split
                    </label>
                    <label for="percentage">
                        <input type="radio" name="splitType" value="percentage" id="percentage"> Percentage Split
                    </label>
                    <label for="custom">
                        <input type="radio" name="splitType" value="custom" id="custom"> Custom Split
                    </label>
                </div>
                <div id="percentageInputs" style="display: none;">
                    <h3>Enter Percentages</h3>
                    <div id="percentageFields"></div>
                </div>
                <div id="customInputs" style="display: none;">
                    <h3>Enter Custom Amounts</h3>
                    <div id="customFields"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Bill Splitting -->
    <div id="billSplitModal" class="modal">
        <div class="modal-content bill-split-modal">
            <span class="close">&times;</span>
            <h2>Split Bill Equally</h2>
            <p style="font-size: 14px; margin: 6px 0;">Split <strong id="billToSplit"></strong> into equal parts:</p>

            <div class="parts-selection">
                <label for="numberOfParts">Number of parts:</label>
                <div class="parts-controls">
                    <button class="count-btn" id="decreaseParts"><i class="fas fa-minus"></i></button>
                    <input type="number" id="numberOfParts" value="2" min="2" max="10">
                    <button class="count-btn" id="increaseParts"><i class="fas fa-plus"></i></button>
                </div>
            </div>

            <div class="bill-split-preview">
                <h3>Preview</h3>
                <p style="font-size: 13px; margin: 4px 0;">Each new bill will contain:</p>
                <ul id="splitPreview"></ul>
                <p style="font-size: 13px; margin: 4px 0;">Amount per bill: <span id="amountPerBill">$0.00</span></p>
            </div>

            <div class="bill-split-buttons">
                <button id="confirmBillSplit"><i class="fas fa-check"></i> Split</button>
                <button id="cancelBillSplit"><i class="fas fa-times"></i> Cancel</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>