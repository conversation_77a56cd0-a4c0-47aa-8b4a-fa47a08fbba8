document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme selection
    initThemeSettings();

    // Back button navigation
    const backBtn = document.getElementById('back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            window.location.href = '../index.html';
        });
    }
});

/**
 * Initialize theme settings and event listeners
 */
function initThemeSettings() {
    const themeRadios = document.querySelectorAll('input[name="theme"]');
    const currentTheme = localStorage.getItem('theme') || 'light';

    // Set the correct radio button based on current theme
    document.getElementById(`theme-${currentTheme}`).checked = true;

    // Add event listeners to theme radio buttons
    themeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                // Set the theme using the global function from theme.js
                setTheme(this.value);

                // Show a success message
                showThemeChangeMessage(this.value);
            }
        });
    });
}

/**
 * Show a temporary message when theme is changed
 * @param {string} theme - The selected theme ('light' or 'dark')
 */
function showThemeChangeMessage(theme) {
    // Create message element if it doesn't exist
    let messageEl = document.getElementById('theme-message');

    if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.id = 'theme-message';
        messageEl.style.position = 'fixed';
        messageEl.style.bottom = '20px';
        messageEl.style.left = '50%';
        messageEl.style.transform = 'translateX(-50%)';
        messageEl.style.padding = '10px 20px';
        messageEl.style.borderRadius = '4px';
        messageEl.style.backgroundColor = theme === 'dark' ? '#343a40' : '#d4edda';
        messageEl.style.color = theme === 'dark' ? '#fff' : '#155724';
        messageEl.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
        messageEl.style.zIndex = '1000';
        messageEl.style.transition = 'opacity 0.3s ease';
        document.body.appendChild(messageEl);
    } else {
        // Update existing message style based on theme
        messageEl.style.backgroundColor = theme === 'dark' ? '#343a40' : '#d4edda';
        messageEl.style.color = theme === 'dark' ? '#fff' : '#155724';
    }

    // Set message text
    messageEl.textContent = `${theme.charAt(0).toUpperCase() + theme.slice(1)} theme applied successfully!`;

    // Show the message
    messageEl.style.opacity = '1';

    // Hide after 3 seconds
    setTimeout(() => {
        messageEl.style.opacity = '0';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, 3000);
}