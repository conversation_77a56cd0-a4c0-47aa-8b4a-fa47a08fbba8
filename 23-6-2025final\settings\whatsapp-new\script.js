// WhatsApp Settings JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const backBtn = document.getElementById('back-btn');
    const businessName = document.getElementById('business-name');
    const businessPhone = document.getElementById('business-phone');
    const businessTagline = document.getElementById('business-tagline');
    const businessAddress = document.getElementById('business-address');
    const includeLogo = document.getElementById('include-logo');
    const includeQR = document.getElementById('include-qr');
    const includeItems = document.getElementById('include-items');
    const footerText = document.getElementById('footer-text');
    const receiptPreview = document.getElementById('receipt-preview');
    const testPhone = document.getElementById('test-phone');
    const testBtn = document.getElementById('test-btn');
    const saveBtn = document.getElementById('save-btn');
    const statusMessage = document.getElementById('status-message');
    
    // Default settings
    const defaultSettings = {
        businessName: 'Paradiz Hookah Lounge',
        businessPhone: '',
        businessTagline: 'The Best Hookah Experience',
        businessAddress: '',
        includeLogo: true,
        includeQR: true,
        includeItems: true,
        footerText: 'Thank you for your business!'
    };
    
    // Sample order data for preview
    const sampleOrder = {
        id: 12345,
        tableNumber: '7',
        customerName: 'John Doe',
        timestamp: new Date().toISOString(),
        items: [
            { name: 'Hookah - Mint', price: 25.00, quantity: 1 },
            { name: 'Soft Drink', price: 3.50, quantity: 2 },
            { name: 'Fries', price: 5.00, quantity: 1 }
        ],
        totalAmount: 37.00,
        paymentMethod: 'Cash'
    };
    
    // Initialize
    loadSettings();
    
    // Event Listeners
    backBtn.addEventListener('click', function() {
        window.location.href = '../index.html';
    });
    
    // Update preview when settings change
    [businessName, businessPhone, businessTagline, businessAddress, footerText].forEach(input => {
        input.addEventListener('input', updatePreview);
    });
    
    [includeLogo, includeQR, includeItems].forEach(checkbox => {
        checkbox.addEventListener('change', updatePreview);
    });
    
    // Save settings
    saveBtn.addEventListener('click', saveSettings);
    
    // Test receipt
    testBtn.addEventListener('click', sendTestReceipt);
    
    /**
     * Load saved settings from server
     */
    function loadSettings() {
        showLoading();
        
        fetch('http://localhost:5000/api/whatsapp-settings')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.settings) {
                    const settings = data.settings;
                    
                    // Set form values
                    businessName.value = settings.businessName || defaultSettings.businessName;
                    businessPhone.value = settings.businessPhone || defaultSettings.businessPhone;
                    businessTagline.value = settings.businessTagline || defaultSettings.businessTagline;
                    businessAddress.value = settings.businessAddress || defaultSettings.businessAddress;
                    includeLogo.checked = settings.includeLogo !== undefined ? settings.includeLogo : defaultSettings.includeLogo;
                    includeQR.checked = settings.includeQR !== undefined ? settings.includeQR : defaultSettings.includeQR;
                    includeItems.checked = settings.includeItems !== undefined ? settings.includeItems : defaultSettings.includeItems;
                    footerText.value = settings.footerText || defaultSettings.footerText;
                    
                    // Update preview
                    updatePreview();
                    showStatus('Settings loaded successfully', 'success');
                } else {
                    // Use default settings
                    resetToDefaults();
                    updatePreview();
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
                resetToDefaults();
                updatePreview();
                showStatus('Error loading settings. Using defaults.', 'error');
            });
    }
    
    /**
     * Reset form to default values
     */
    function resetToDefaults() {
        businessName.value = defaultSettings.businessName;
        businessPhone.value = defaultSettings.businessPhone;
        businessTagline.value = defaultSettings.businessTagline;
        businessAddress.value = defaultSettings.businessAddress;
        includeLogo.checked = defaultSettings.includeLogo;
        includeQR.checked = defaultSettings.includeQR;
        includeItems.checked = defaultSettings.includeItems;
        footerText.value = defaultSettings.footerText;
    }
    
    /**
     * Save settings to server
     */
    function saveSettings() {
        const settings = {
            businessName: businessName.value,
            businessPhone: businessPhone.value,
            businessTagline: businessTagline.value,
            businessAddress: businessAddress.value,
            includeLogo: includeLogo.checked,
            includeQR: includeQR.checked,
            includeItems: includeItems.checked,
            footerText: footerText.value
        };
        
        // Disable save button
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        
        fetch('http://localhost:5000/api/whatsapp-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            // Re-enable save button
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Settings';
            
            if (data.success) {
                showStatus('Settings saved successfully', 'success');
            } else {
                showStatus(`Error saving settings: ${data.error || 'Unknown error'}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            
            // Re-enable save button
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Settings';
            
            showStatus('Error saving settings. Please try again.', 'error');
        });
    }
    
    /**
     * Send test receipt
     */
    function sendTestReceipt() {
        const phone = testPhone.value.trim();
        
        if (!phone) {
            showStatus('Please enter a phone number', 'error');
            return;
        }
        
        // Disable test button
        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        
        // Get current settings
        const settings = {
            businessName: businessName.value,
            businessPhone: businessPhone.value,
            businessTagline: businessTagline.value,
            businessAddress: businessAddress.value,
            includeLogo: includeLogo.checked,
            includeQR: includeQR.checked,
            includeItems: includeItems.checked,
            footerText: footerText.value
        };
        
        fetch('http://localhost:5000/api/whatsapp-test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phoneNumber: phone,
                settings: settings,
                order: sampleOrder
            })
        })
        .then(response => response.json())
        .then(data => {
            // Re-enable test button
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fab fa-whatsapp"></i> Send Test Receipt';
            
            if (data.success) {
                showStatus('Test receipt sent successfully', 'success');
            } else {
                showStatus(`Error sending test receipt: ${data.error || 'Unknown error'}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error sending test receipt:', error);
            
            // Re-enable test button
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fab fa-whatsapp"></i> Send Test Receipt';
            
            showStatus('Error sending test receipt. Please try again.', 'error');
        });
    }
    
    /**
     * Update receipt preview
     */
    function updatePreview() {
        const name = businessName.value || defaultSettings.businessName;
        const phone = businessPhone.value || '';
        const tagline = businessTagline.value || defaultSettings.businessTagline;
        const address = businessAddress.value || '';
        const showLogo = includeLogo.checked;
        const showQR = includeQR.checked;
        const showItems = includeItems.checked;
        const footer = footerText.value || defaultSettings.footerText;
        
        // Calculate total
        const total = sampleOrder.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        // Format date
        const date = new Date(sampleOrder.timestamp);
        const formattedDate = date.toLocaleDateString();
        const formattedTime = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        // Build preview HTML
        let html = '<div class="whatsapp-message">';
        
        // Logo
        if (showLogo) {
            html += `
                <div class="business-logo">
                    <i class="fas fa-store"></i>
                </div>
            `;
        }
        
        // Business name and tagline
        html += `<div class="business-name">${name}</div>`;
        
        if (tagline) {
            html += `<div class="business-tagline">${tagline}</div>`;
        }
        
        // Receipt header
        html += `
            <div class="receipt-header">
                <div>Date: ${formattedDate} | Time: ${formattedTime}</div>
                <div>Table: ${sampleOrder.tableNumber} | Customer: ${sampleOrder.customerName}</div>
            </div>
        `;
        
        // Items
        if (showItems) {
            html += '<div class="receipt-items">';
            html += '<div><strong>Items:</strong></div>';
            
            sampleOrder.items.forEach(item => {
                const itemTotal = item.price * item.quantity;
                html += `
                    <div class="receipt-item">
                        <span>${item.name} x${item.quantity}</span>
                        <span>$${itemTotal.toFixed(2)}</span>
                    </div>
                `;
            });
            
            html += '</div>';
        }
        
        // Total
        html += `
            <div class="receipt-total">
                Total: $${total.toFixed(2)}
            </div>
        `;
        
        // Payment method
        html += `<div>Payment Method: ${sampleOrder.paymentMethod}</div>`;
        
        // Business address
        if (address) {
            html += `<div style="margin-top: 10px;">Address: ${address}</div>`;
        }
        
        // Business phone
        if (phone) {
            html += `<div>Contact: ${phone}</div>`;
        }
        
        // Footer
        if (footer) {
            html += `<div class="receipt-footer">${footer}</div>`;
        }
        
        // QR Code
        if (showQR) {
            html += `
                <div class="qr-code">
                    <div>Scan to verify receipt:</div>
                    <i class="fas fa-qrcode"></i>
                </div>
            `;
        }
        
        html += '</div>'; // Close whatsapp-message
        
        // Update preview
        receiptPreview.innerHTML = html;
    }
    
    /**
     * Show loading state in preview
     */
    function showLoading() {
        receiptPreview.innerHTML = '<div class="loading">Loading preview...</div>';
    }
    
    /**
     * Show status message
     * @param {string} message - Status message
     * @param {string} type - 'success' or 'error'
     */
    function showStatus(message, type = 'success') {
        statusMessage.textContent = message;
        statusMessage.className = 'status-message';
        
        if (type === 'success') {
            statusMessage.classList.add('status-success');
        } else {
            statusMessage.classList.add('status-error');
        }
        
        // Hide after 5 seconds
        setTimeout(() => {
            statusMessage.style.display = 'none';
        }, 5000);
    }
});
