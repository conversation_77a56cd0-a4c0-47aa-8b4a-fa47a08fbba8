// DOM Elements
const backupTableBody = document.getElementById('backupTableBody');
const backupModal = document.getElementById('backupModal');
const backupInfo = document.getElementById('backupInfo');
const backupFiles = document.getElementById('backupFiles');
const ordersTableBody = document.getElementById('ordersTableBody');
const refreshBtn = document.getElementById('refreshBtn');
const createBackupBtn = document.getElementById('createBackupBtn');
const closeModal = document.getElementById('closeModal');
const backBtn = document.getElementById('backBtn');

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Format time
function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Calculate total from items
function calculateTotal(items) {
    if (!items || !Array.isArray(items)) return 0;
    return items.reduce((total, item) => {
        const price = parseFloat(item.price) || 0;
        const quantity = parseInt(item.quantity) || 1;
        return total + (price * quantity);
    }, 0);
}

// Load backups
async function loadBackups() {
    try {
        // Use the enhanced backup service API
        const response = await fetch('http://localhost:5000/api/backups');
        const backups = await response.json();

        if (backups.length === 0) {
            backupTableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="no-backups">No backups found</td>
                </tr>
            `;
            return;
        }

        backupTableBody.innerHTML = backups.map(backup => {
            // Calculate file count from files array if available
            const fileCount = backup.files ? backup.files.length : (backup.fileCount || 0);

            return `
                <tr>
                    <td>${formatDate(backup.timestamp)}</td>
                    <td>${formatTime(backup.timestamp)}</td>
                    <td>${fileCount}</td>
                    <td>${formatFileSize(backup.size)}</td>
                    <td>
                        <button class="btn btn-primary" onclick="showBackupDetails('${backup.name}', '${backup.type || 'manual'}')">
                            View Details
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    } catch (error) {
        console.error('Error loading backups:', error);
        backupTableBody.innerHTML = `
            <tr>
                <td colspan="5" class="error-message">Failed to load backups: ${error.message}</td>
            </tr>
        `;
    }
}

// Show backup details
async function showBackupDetails(backupName, backupType = 'manual') {
    console.log(`Showing details for backup: ${backupName}, type: ${backupType}`);
    try {
        // Show loading state
        backupInfo.innerHTML = '<div class="loading">Loading backup details...</div>';
        backupFiles.innerHTML = '';
        ordersTableBody.innerHTML = '<tr><td colspan="7" class="loading">Loading orders...</td></tr>';

        // Fetch backup metadata from enhanced backup service
        console.log(`Fetching from: http://localhost:5000/api/backups/${backupName}?type=${backupType}`);
        const metadataResponse = await fetch(`http://localhost:5000/api/backups/${backupName}?type=${backupType}`);

        if (!metadataResponse.ok) {
            throw new Error(`Server returned ${metadataResponse.status}: ${metadataResponse.statusText}`);
        }

        const metadata = await metadataResponse.json();
        console.log('Received metadata:', metadata);

        // Build backup info HTML
        backupInfo.innerHTML = `
            <div class="info-grid">
                <div class="info-item">
                    <strong>Name:</strong> ${metadata.name}
                </div>
                <div class="info-item">
                    <strong>Type:</strong> ${metadata.type || 'manual'}
                </div>
                <div class="info-item">
                    <strong>Date:</strong> ${formatDate(metadata.timestamp)}
                </div>
                <div class="info-item">
                    <strong>Time:</strong> ${formatTime(metadata.timestamp)}
                </div>
                <div class="info-item">
                    <strong>Size:</strong> ${formatFileSize(metadata.size)}
                </div>
                <div class="info-item">
                    <strong>Files:</strong> ${metadata.files ? metadata.files.length : (metadata.fileCount || 0)}
                </div>
            </div>
        `;

        // Build files list HTML
        if (metadata.files && metadata.files.length > 0) {
            backupFiles.innerHTML = `
                <ul class="files-list">
                    ${metadata.files.map(file => `
                        <li>${file}</li>
                    `).join('')}
                </ul>
            `;
        } else {
            backupFiles.innerHTML = '<div class="no-files">No files information available</div>';
        }

        // Fetch and display orders
        try {
            const ordersResponse = await fetch(`http://localhost:5000/backup/${backupName}/orders?type=${backupType}`);
            const ordersData = await ordersResponse.json();

            if (ordersData.orders && ordersData.orders.length > 0) {
                ordersTableBody.innerHTML = ordersData.orders.map(order => {
                    const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
                    const total = calculateTotal(items);

                    return `
                        <tr>
                            <td>${order.tableNumber || 'N/A'}</td>
                            <td>${order.customerName || 'N/A'}</td>
                            <td>
                                <ul class="items-list">
                                    ${items.map(item => `
                                        <li>
                                            ${item.quantity || 1}x ${item.name}
                                            ${item.notes ? ` (${item.notes})` : ''}
                                        </li>
                                    `).join('')}
                                </ul>
                            </td>
                            <td class="total-amount">${formatCurrency(total)}</td>
                            <td class="status-${order.status?.toLowerCase() || 'pending'}">
                                ${order.status || 'Pending'}
                            </td>
                            <td class="payment-method">
                                ${order.paymentMethod || 'Not Paid'}
                            </td>
                            <td>${formatTime(order.timestamp)}</td>
                        </tr>
                    `;
                }).join('');
            } else {
                ordersTableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="no-orders">No orders found in this backup</td>
                    </tr>
                `;
            }
        } catch (error) {
            console.error('Error loading orders:', error);
            ordersTableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="error-message">
                        Failed to load orders. The backup might be corrupted or in an incompatible format.
                    </td>
                </tr>
            `;
        }

        // Show modal
        backupModal.style.display = 'block';
    } catch (error) {
        console.error('Error loading backup details:', error);
        backupInfo.innerHTML = `
            <div class="error-message">
                <h3>Error Loading Backup Details</h3>
                <p>${error.message}</p>
                <p>Please try again or create a new backup.</p>
            </div>
        `;
        backupFiles.innerHTML = '';
        ordersTableBody.innerHTML = `
            <tr>
                <td colspan="7" class="error-message">
                    Could not load orders due to an error.
                </td>
            </tr>
        `;

        // Still show the modal with the error
        backupModal.style.display = 'block';
    }
}

// Create new backup
async function createBackup() {
    try {
        // Show loading state
        createBackupBtn.textContent = 'Creating Backup...';
        createBackupBtn.disabled = true;

        // Use enhanced backup service
        const response = await fetch('http://localhost:5000/api/backup?type=manual', {
            method: 'POST'
        });
        const result = await response.json();

        // Restore button state
        createBackupBtn.textContent = 'Backup';
        createBackupBtn.disabled = false;

        if (result.success) {
            alert('Backup created successfully!');
            loadBackups();
        } else {
            throw new Error(result.error || 'Failed to create backup');
        }
    } catch (error) {
        // Restore button state
        createBackupBtn.textContent = 'Backup';
        createBackupBtn.disabled = false;

        console.error('Error creating backup:', error);
        alert('Failed to create backup: ' + error.message);
    }
}

// Event Listeners
refreshBtn.addEventListener('click', loadBackups);
createBackupBtn.addEventListener('click', createBackup);
closeModal.addEventListener('click', () => {
    backupModal.style.display = 'none';
});

backBtn.addEventListener('click', () => {
    window.location.href = '../settings/index.html';
});

// Close modal when clicking outside
window.addEventListener('click', (event) => {
    if (event.target === backupModal) {
        backupModal.style.display = 'none';
    }
});

// Initial load
loadBackups();