:root {
    /* Light theme variables */
    --background-color: #f4f4f4;
    --container-bg: #ffffff;
    --text-color: #333333;
    --heading-color: #2c3e50;
    --border-color: #e0e0e0;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    --button-primary-bg: #3498db;
    --button-primary-color: #ffffff;
    --button-secondary-bg: #e0e0e0;
    --button-secondary-color: #333333;
    --input-bg: #ffffff;
    --input-border: #cccccc;
    --input-color: #333333;
}

/* Disable transitions during initial theme load to prevent flash */
.theme-transition-disabled * {
    transition: none !important;
}

/* Dark theme variables */
body.dark-theme {
    --background-color: #1a1a1a;
    --container-bg: #2c2c2c;
    --text-color: #f0f0f0;
    --heading-color: #f8f9fa;
    --border-color: #444444;
    --card-bg: #343a40;
    --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --button-primary-bg: #2980b9;
    --button-primary-color: #ffffff;
    --button-secondary-bg: #495057;
    --button-secondary-color: #f0f0f0;
    --input-bg: #3a3a3a;
    --input-border: #555555;
    --input-color: #f0f0f0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Icon Styles */
.fas, .far, .fab, .fa {
    margin-right: 10px;
    font-size: 1.2em;
}

/* Common button styles */
button {
    background: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

button:active {
    transform: translateY(0);
}

/* Common container styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: var(--container-bg);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Common heading styles */
h1, h2, h3, h4, h5, h6 {
    color: var(--heading-color);
    margin-top: 0;
    transition: color 0.3s ease;
}

/* Common card styles */
.card {
    background: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--card-shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.container {
    width: 80%;
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: var(--container-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

h1 {
    text-align: center;
    color: var(--heading-color);
    margin-bottom: 30px;
    transition: color 0.3s ease;
}

nav ul {
    list-style-type: none;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

nav ul li {
    margin: 0;
}

nav ul li a {
    text-decoration: none;
    color: white;
    display: block;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-align: center;
    font-size: 1.1em;
    font-weight: bold;
}

/* Button styles for different sections */
.button {
    background-color: #3498db;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.button:active {
    transform: translateY(0);
}

/* Specific button colors for different sections */
nav ul li:nth-child(1) a {
    background-color: #3498db; /* Blue for tablet */
}

nav ul li:nth-child(2) a {
    background-color: #2ecc71; /* Green for POS */
}

nav ul li:nth-child(3) a {
    background-color: #9b59b6; /* Purple for hookah */
}

nav ul li:nth-child(4) a {
    background-color: #e67e22; /* Orange for hookah kitchen */
}

nav ul li:nth-child(5) a {
    background-color: #8A2BE2; /* Dark lavender (blueviolet) for food/drinks */
}

/* Hover effects for each section */
nav ul li:nth-child(1) a:hover {
    background-color: #2980b9;
}

nav ul li:nth-child(2) a:hover {
    background-color: #27ae60;
}

nav ul li:nth-child(3) a:hover {
    background-color: #8e44ad;
}

nav ul li:nth-child(4) a:hover {
    background-color: #d35400;
}

nav ul li:nth-child(5) a:hover {
    background-color: #6A1B9A; /* Darker lavender on hover */
}

/* Additional dark theme styles for navigation */
body.dark-theme nav ul li a {
    opacity: 0.9;
}

body.dark-theme nav ul li a:hover {
    opacity: 1;
}

/* Theme toggle button styles */
.theme-toggle-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--button-primary-bg);
    color: var(--button-primary-color);
    border: none;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.theme-toggle-btn:hover {
    transform: scale(1.1);
}

.theme-toggle-btn:active {
    transform: scale(0.95);
}

/* For mobile devices */
@media (max-width: 768px) {
    .theme-toggle-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
        bottom: 15px;
        right: 15px;
    }
}