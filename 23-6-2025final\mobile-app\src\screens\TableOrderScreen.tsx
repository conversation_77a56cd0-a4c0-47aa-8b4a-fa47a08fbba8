import React, { useState, useEffect, useContext } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { Text, Card, Title, Paragraph, Button, Chip, Divider, ActivityIndicator, Badge } from 'react-native-paper';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { AuthContext } from '../context/AuthContext';
import { menuApi, orderApi } from '../api/api';

const TableOrderScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useContext(AuthContext);
  const { tableId, sessionId } = route.params;

  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [menuItems, setMenuItems] = useState([]);
  const [cartItems, setCartItems] = useState([]);
  const [activeOrder, setActiveOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    loadMenuItems(selectedCategory);
  }, [selectedCategory]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load categories
      const categoriesResponse = await menuApi.getCategories();
      setCategories(categoriesResponse.data.categories);
      
      // Check if there's an active order for this table
      const orderResponse = await orderApi.getTableOrder(tableId, sessionId);
      
      if (orderResponse.data.order) {
        setActiveOrder(orderResponse.data.order);
      }
      
      // Load initial menu items
      await loadMenuItems();
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load menu data');
    } finally {
      setLoading(false);
    }
  };

  const loadMenuItems = async (categoryId = null) => {
    try {
      const response = await menuApi.getItems(categoryId);
      setMenuItems(response.data.items);
    } catch (error) {
      console.error('Error loading menu items:', error);
      Alert.alert('Error', 'Failed to load menu items');
    }
  };

  const addToCart = (item) => {
    const existingItem = cartItems.find(cartItem => cartItem.id === item.id);
    
    if (existingItem) {
      setCartItems(cartItems.map(cartItem => 
        cartItem.id === item.id 
          ? { ...cartItem, quantity: cartItem.quantity + 1 } 
          : cartItem
      ));
    } else {
      setCartItems([...cartItems, { ...item, quantity: 1 }]);
    }
  };

  const removeFromCart = (itemId) => {
    const existingItem = cartItems.find(item => item.id === itemId);
    
    if (existingItem.quantity > 1) {
      setCartItems(cartItems.map(item => 
        item.id === itemId 
          ? { ...item, quantity: item.quantity - 1 } 
          : item
      ));
    } else {
      setCartItems(cartItems.filter(item => item.id !== itemId));
    }
  };

  const submitOrder = async () => {
    if (cartItems.length === 0) {
      Alert.alert('Empty Cart', 'Please add items to your order');
      return;
    }

    try {
      setSubmitting(true);
      
      const orderItems = cartItems.map(item => ({
        id: item.id,
        quantity: item.quantity,
        price: item.price,
        customizations: item.customizations || []
      }));
      
      let response;
      
      if (activeOrder) {
        // Add to existing order
        response = await orderApi.addToOrder(activeOrder.id, orderItems);
      } else {
        // Create new order
        response = await orderApi.createOrder({
          tableId,
          sessionId,
          items: orderItems
        });
      }
      
      if (response.data.success) {
        setActiveOrder(response.data.order);
        setCartItems([]);
        Alert.alert('Success', 'Your order has been submitted!');
      } else {
        Alert.alert('Error', response.data.message || 'Failed to submit order');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      Alert.alert('Error', 'Failed to submit your order');
    } finally {
      setSubmitting(false);
    }
  };

  const renderCategoryChip = ({ item }) => (
    <Chip
      selected={selectedCategory === item.id}
      onPress={() => setSelectedCategory(selectedCategory === item.id ? null : item.id)}
      style={styles.categoryChip}
      textStyle={{ color: selectedCategory === item.id ? 'white' : '#6B46C1' }}
      selectedColor="#6B46C1"
    >
      {item.name}
    </Chip>
  );

  const renderMenuItem = ({ item }) => (
    <Card style={styles.menuItemCard}>
      <Card.Content>
        <View style={styles.menuItemHeader}>
          <Title style={styles.menuItemTitle}>{item.name}</Title>
          <Text style={styles.menuItemPrice}>${item.price.toFixed(2)}</Text>
        </View>
        <Paragraph style={styles.menuItemDescription} numberOfLines={2}>
          {item.description}
        </Paragraph>
        <View style={styles.menuItemActions}>
          <Button
            mode="contained"
            onPress={() => addToCart(item)}
            style={styles.addButton}
            labelStyle={styles.buttonLabel}
          >
            Add to Order
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const renderCartItem = ({ item }) => (
    <View style={styles.cartItem}>
      <View style={styles.cartItemInfo}>
        <Text style={styles.cartItemName}>{item.name}</Text>
        <Text style={styles.cartItemPrice}>${(item.price * item.quantity).toFixed(2)}</Text>
      </View>
      <View style={styles.cartItemQuantity}>
        <TouchableOpacity onPress={() => removeFromCart(item.id)} style={styles.quantityButton}>
          <Ionicons name="remove" size={16} color="#6B46C1" />
        </TouchableOpacity>
        <Text style={styles.quantityText}>{item.quantity}</Text>
        <TouchableOpacity onPress={() => addToCart(item)} style={styles.quantityButton}>
          <Ionicons name="add" size={16} color="#6B46C1" />
        </TouchableOpacity>
      </View>
    </View>
  );

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6B46C1" />
        <Text style={styles.loadingText}>Loading menu...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.tableInfo}>Table {tableId}</Text>
        {activeOrder && (
          <Text style={styles.orderInfo}>Order #{activeOrder.id}</Text>
        )}
      </View>

      <View style={styles.content}>
        <View style={styles.menuContainer}>
          <FlatList
            horizontal
            data={categories}
            renderItem={renderCategoryChip}
            keyExtractor={(item) => item.id.toString()}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          />

          <FlatList
            data={menuItems}
            renderItem={renderMenuItem}
            keyExtractor={(item) => item.id.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.menuItemsContainer}
          />
        </View>

        <View style={styles.cartContainer}>
          <View style={styles.cartHeader}>
            <Text style={styles.cartTitle}>Your Order</Text>
            {cartItems.length > 0 && (
              <TouchableOpacity onPress={() => setCartItems([])}>
                <Text style={styles.clearCart}>Clear</Text>
              </TouchableOpacity>
            )}
          </View>

          {cartItems.length > 0 ? (
            <>
              <FlatList
                data={cartItems}
                renderItem={renderCartItem}
                keyExtractor={(item) => item.id.toString()}
                contentContainerStyle={styles.cartItemsContainer}
              />

              <Divider style={styles.divider} />

              <View style={styles.totalContainer}>
                <Text style={styles.totalLabel}>Total:</Text>
                <Text style={styles.totalAmount}>${calculateTotal().toFixed(2)}</Text>
              </View>

              <Button
                mode="contained"
                onPress={submitOrder}
                style={styles.submitButton}
                loading={submitting}
                disabled={submitting}
              >
                {activeOrder ? 'Add to Order' : 'Place Order'}
              </Button>
            </>
          ) : (
            <View style={styles.emptyCartContainer}>
              <Ionicons name="cart-outline" size={48} color="#A0AEC0" />
              <Text style={styles.emptyCartText}>Your cart is empty</Text>
              <Text style={styles.emptyCartSubtext}>Add items from the menu</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#4A5568',
  },
  header: {
    backgroundColor: '#6B46C1',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tableInfo: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  orderInfo: {
    color: 'white',
    fontSize: 16,
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  menuContainer: {
    flex: 2,
    borderRightWidth: 1,
    borderRightColor: '#E2E8F0',
  },
  categoriesContainer: {
    padding: 16,
  },
  categoryChip: {
    marginRight: 8,
    backgroundColor: 'white',
    borderColor: '#6B46C1',
    borderWidth: 1,
  },
  menuItemsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  menuItemCard: {
    marginBottom: 16,
    elevation: 2,
  },
  menuItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuItemTitle: {
    fontSize: 16,
    flex: 1,
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B46C1',
  },
  menuItemDescription: {
    fontSize: 14,
    color: '#4A5568',
    marginVertical: 8,
  },
  menuItemActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  addButton: {
    backgroundColor: '#6B46C1',
  },
  buttonLabel: {
    fontSize: 12,
  },
  cartContainer: {
    flex: 1,
    padding: 16,
    backgroundColor: 'white',
  },
  cartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  clearCart: {
    color: '#E53E3E',
    fontSize: 14,
  },
  cartItemsContainer: {
    flexGrow: 1,
  },
  cartItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  cartItemInfo: {
    flex: 1,
  },
  cartItemName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2D3748',
  },
  cartItemPrice: {
    fontSize: 14,
    color: '#6B46C1',
    fontWeight: 'bold',
    marginTop: 4,
  },
  cartItemQuantity: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#EDF2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    marginHorizontal: 8,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  divider: {
    marginVertical: 16,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B46C1',
  },
  submitButton: {
    backgroundColor: '#6B46C1',
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyCartText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4A5568',
    marginTop: 16,
  },
  emptyCartSubtext: {
    fontSize: 14,
    color: '#A0AEC0',
    marginTop: 8,
  },
});

export default TableOrderScreen;
