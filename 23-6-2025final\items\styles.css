/* Items Management Styles */

:root {
    --danger-color: #dc3545;
    --danger-color-dark: #bd2130;
    --success-color: #28a745;
    --success-color-dark: #218838;
}

.items-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.instructions {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 4px solid var(--primary-color);
    padding: 10px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.instructions p {
    margin: 0;
    line-height: 1.5;
}

.items-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    align-items: center;
}

.search-container {
    display: flex;
    gap: 10px;
}

#search-input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    width: 250px;
}

#search-btn {
    padding: 8px 16px;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

#search-btn:hover {
    background-color: var(--secondary-color-dark);
}

.category-filter {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

#category-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    background-color: var(--input-bg);
    color: var(--text-color);
    min-width: 200px;
}

.items-list-container {
    overflow-x: auto;
}

#items-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

#items-table th,
#items-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

#items-table th {
    background-color: var(--table-header-bg);
    color: var(--text-color);
    font-weight: bold;
}

#items-table tr:hover {
    background-color: var(--hover-color);
}

.action-btn {
    padding: 6px 12px;
    margin-right: 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
}

.edit-btn {
    background-color: var(--secondary-color);
    color: white;
}

.delete-btn {
    background-color: var(--danger-color);
    color: white;
}

.edit-btn:hover {
    background-color: var(--secondary-color-dark);
}

.delete-btn:hover {
    background-color: var(--danger-color-dark);
}

/* Modal Styles */
.modal {
    display: none; /* Initially hidden */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85); /* Very dark background for maximum contrast */
    backdrop-filter: blur(5px); /* Stronger blur effect */
}

/* When modal is shown, it will use these flex properties */
.modal[style*="display: block"] {
    display: flex !important;
    justify-content: center;
    align-items: flex-start; /* Align to top instead of center */
    padding-top: 5vh; /* Add some space from the top */
}

.modal-content {
    background-color: #ffffff; /* Force white background for maximum contrast */
    padding: 20px; /* Reduced padding */
    border-radius: 8px;
    width: 90%;
    max-width: 550px;
    max-height: 85vh; /* Limit height to 85% of viewport height */
    overflow-y: auto; /* Add scrolling if content is too tall */
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.5); /* Much stronger shadow */
    border: 3px solid #000000; /* Solid black border */
    animation: modalFadeIn 0.3s ease-out;
    color: #000000; /* Force black text */
    margin: 0; /* Remove margin since we're using flex centering */
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    color: var(--text-color-light);
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: var(--danger-color);
}

.modal-content h2 {
    margin-top: 0;
    padding-bottom: 10px; /* Reduced padding */
    border-bottom: 2px solid #000000; /* Thinner border */
    margin-bottom: 15px; /* Reduced margin */
    color: #0066cc; /* Bright blue */
    font-size: 20px; /* Smaller font size */
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-group {
    margin-bottom: 10px; /* Reduced margin */
}

.form-group label {
    display: block;
    margin-bottom: 5px; /* Reduced margin */
    font-weight: bold;
    font-size: 16px; /* Smaller font size */
    color: #000000; /* Force black text */
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px; /* Reduced padding */
    border: 2px solid #000000; /* Black border for contrast */
    border-radius: 4px;
    font-size: 16px; /* Smaller font size */
    background-color: #ffffff; /* White background */
    color: #000000; /* Black text */
    transition: border-color 0.2s, box-shadow 0.2s;
    margin-bottom: 10px; /* Reduced margin */
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0000ff; /* Bright blue border */
    box-shadow: 0 0 0 4px rgba(0, 0, 255, 0.4); /* Stronger blue glow */
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px; /* Reduced margin */
}

.primary-btn {
    padding: 10px 20px; /* Reduced padding */
    background-color: #0066cc; /* Bright blue */
    color: white;
    border: 2px solid #004080; /* Darker blue border */
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px; /* Smaller font size */
    font-weight: bold;
    transition: background-color 0.2s, transform 0.1s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.secondary-btn {
    padding: 10px 20px; /* Reduced padding */
    background-color: #6c757d; /* Gray */
    color: white;
    border: 2px solid #495057; /* Darker gray border */
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px; /* Smaller font size */
    font-weight: bold;
    transition: background-color 0.2s, transform 0.1s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.danger-btn {
    padding: 10px 20px; /* Reduced padding */
    background-color: #dc3545; /* Bright red */
    color: white;
    border: 2px solid #a71d2a; /* Darker red border */
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px; /* Smaller font size */
    font-weight: bold;
    transition: background-color 0.2s, transform 0.1s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.primary-btn:hover {
    background-color: #0052a3; /* Darker blue */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.secondary-btn:hover {
    background-color: #5a6268; /* Darker gray */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.danger-btn:hover {
    background-color: #bd2130; /* Darker red */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.primary-btn:active,
.secondary-btn:active,
.danger-btn:active {
    transform: translateY(1px);
}

.truncate {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.price-column {
    min-width: 80px;
}

.item-name-highlight {
    color: #ffffff; /* White text */
    font-weight: bold;
    background-color: #0066cc; /* Bright blue background */
    padding: 3px 8px; /* Reduced padding */
    border-radius: 4px;
    display: inline-block;
    font-size: 16px; /* Smaller font size */
    border: 1px solid #004080; /* Thinner border */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Smaller shadow */
    margin-top: 3px; /* Reduced margin */
}

/* Delete Modal Styles */
.delete-modal h2 {
    color: #dc3545; /* Bright red */
    text-align: center;
    font-size: 28px;
    border-bottom: 3px solid #dc3545;
}

.warning-icon {
    margin-right: 10px;
    font-size: 28px;
    vertical-align: middle;
}

.delete-warning {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 25px;
    background-color: #ffdddd; /* Light red background */
    padding: 15px;
    border-radius: 6px;
    border: 2px solid #dc3545; /* Red border */
    color: #000000; /* Black text */
    font-weight: 500;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .items-controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .search-container {
        width: 100%;
    }

    #search-input {
        width: 100%;
    }

    .category-filter {
        flex-direction: column;
        align-items: flex-start;
    }

    #category-select {
        width: 100%;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}
