{"id": 352, "tableNumber": "1", "customerName": "diana", "items": [{"name": "<PERSON><PERSON>", "price": 7.99, "quantity": 1, "notes": ""}, {"name": "Wings", "price": 9.99, "quantity": 1, "notes": ""}], "coverCharge": "[object Object]", "paid": 1, "status": "pending", "kitchenStatus": "pending", "report": null, "timestamp": "2025-04-13T06:18:42.053Z", "paymentMethod": "cash", "transactionId": null, "payment": {"paymentMethod": "cash", "tip": 0, "subtotal": 17.98, "tax": 0, "taxRate": 0, "gst": 0, "gstRate": 0, "pst": 0, "pstRate": 0, "total": 17.98, "transactionId": null}, "archiveTimestamp": "2025-04-13T06:19:06.540Z"}