{"id": "table-2-1754951795380", "tableNumber": "2", "customerName": "Table 2 (Combined)", "timestamp": "2025-08-11T22:36:35.380Z", "items": [{"name": "Grape + Double apple + Lemon", "price": 25, "quantity": 1, "notes": ""}, {"name": "Tea pot-s", "price": 6.5, "quantity": 1, "notes": ""}, {"name": "Barbican", "price": 4.99, "quantity": 1, "notes": ""}, {"name": "Blueberry1", "price": 25, "quantity": 1, "notes": ""}, {"name": "<PERSON><PERSON>", "price": 1, "quantity": 1, "notes": ""}, {"name": "Tea pot-l", "price": 9.99, "quantity": 1, "notes": ""}, {"name": "<PERSON><PERSON>", "price": 7.99, "quantity": 1, "notes": ""}, {"name": "Chocolate Cake", "price": 1.11, "quantity": 1, "notes": ""}, {"name": "Grape + Mango", "price": 25, "quantity": 1, "notes": ""}], "payment": {"paymentMethod": "cash", "tip": 0, "subtotal": 106.58, "tax": 0, "taxRate": 0, "gst": 0, "gstRate": 0, "pst": 0, "pstRate": 0, "total": 106.58, "transactionId": null}, "archiveTimestamp": "2025-08-11T22:36:35.385Z"}