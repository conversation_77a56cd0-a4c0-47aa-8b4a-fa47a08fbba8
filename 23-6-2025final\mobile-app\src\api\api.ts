import axios from 'axios';
import { setupMockInterceptors } from './mockData';

// Create an axios instance
export const api = axios.create({
  baseURL: 'http://localhost:5000', // Change this to your server URL in production
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Setup mock interceptors for development
if (__DEV__) {
  setupMockInterceptors(api);
}

// Menu API
export const menuApi = {
  getCategories: () => api.get('/api/menu/categories'),
  getItems: (categoryId?: string) => api.get(`/api/menu/items${categoryId ? `?category=${categoryId}` : ''}`),
  getItemDetails: (itemId: string) => api.get(`/api/menu/items/${itemId}`),
  searchItems: (query: string) => api.get(`/api/menu/search?q=${query}`),
};

// Reservation API
export const reservationApi = {
  getAvailability: (date: string) => api.get(`/api/reservations/availability?date=${date}`),
  createReservation: (data: any) => api.post('/api/reservations', data),
  getUserReservations: () => api.get('/api/reservations/user'),
  cancelReservation: (id: string) => api.delete(`/api/reservations/${id}`),
};

// Order API
export const orderApi = {
  createOrder: (data: any) => api.post('/api/orders', data),
  getTableOrder: (tableId: string, sessionId: string) => api.get(`/api/orders/table/${tableId}?sessionId=${sessionId}`),
  addToOrder: (orderId: string, items: any[]) => api.post(`/api/orders/${orderId}/items`, { items }),
  getUserOrders: () => api.get('/api/orders/user'),
  getOrderDetails: (orderId: string) => api.post(`/api/orders/${orderId}`),
  payOrder: (orderId: string, paymentData: any) => api.post(`/api/orders/${orderId}/pay`, paymentData),
};

// User API
export const userApi = {
  updateProfile: (data: any) => api.put('/api/users/profile', data),
  getFavorites: () => api.get('/api/users/favorites'),
  addFavorite: (itemId: string) => api.post('/api/users/favorites', { itemId }),
  removeFavorite: (itemId: string) => api.delete(`/api/users/favorites/${itemId}`),
};

// Notification API
export const notificationApi = {
  getNotifications: () => api.get('/api/notifications'),
  markAsRead: (notificationId: string) => api.put(`/api/notifications/${notificationId}/read`),
  markAllAsRead: () => api.put('/api/notifications/read-all'),
  registerPushToken: (token: string) => api.post('/api/users/push-token', { token }),
};

// Events API
export const eventsApi = {
  getEvents: () => api.get('/api/events'),
  getEventDetails: (eventId: string) => api.get(`/api/events/${eventId}`),
  setEventReminder: (eventId: string, data: any) => api.post(`/api/events/${eventId}/reminder`, data),
};
