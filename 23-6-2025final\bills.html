<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill Archive - Paradiz <PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .bills-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .back-button-container {
            margin-top: 10px;
            margin-bottom: 20px;
        }

        .back-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
            min-width: 200px;
            justify-content: center;
        }

        .back-btn i {
            font-size: 20px;
        }

        .back-btn:hover {
            background-color: #0b7dda;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .back-btn:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .back-btn {
                padding: 18px 30px;
                font-size: 20px;
                min-width: 250px;
            }

            .back-btn i {
                font-size: 24px;
            }
        }

        .search-section {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .search-btn, .reset-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            min-width: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .search-btn i, .reset-btn i {
            font-size: 18px;
        }

        .search-btn {
            background-color: #4CAF50;
            color: white;
        }

        .search-btn:hover {
            background-color: #3e8e41;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .reset-btn {
            background-color: #f44336;
            color: white;
        }

        .reset-btn:hover {
            background-color: #d32f2f;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .search-btn:active, .reset-btn:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .search-btn, .reset-btn {
                padding: 18px 30px;
                font-size: 18px;
                min-width: 160px;
            }

            .search-btn i, .reset-btn i {
                font-size: 20px;
            }
        }

        .bills-list {
            margin-top: 30px;
        }

        .bill-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-wrap: wrap;
        }

        .bill-info {
            flex: 3;
            min-width: 300px;
        }

        .bill-actions {
            flex: 1;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 10px;
            padding-left: 15px;
        }

        .bill-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .bill-id {
            font-weight: bold;
            color: #333;
        }

        .bill-date {
            color: #666;
        }

        .bill-customer {
            margin-bottom: 10px;
        }

        .bill-table {
            color: #666;
            margin-bottom: 5px;
        }

        .bill-items {
            margin-bottom: 10px;
        }

        .bill-total {
            font-weight: bold;
            font-size: 1.1em;
            margin-top: 10px;
        }

        .bill-payment {
            color: #4CAF50;
        }

        .action-btn {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15px;
            font-weight: bold;
            margin-bottom: 10px;
            width: 100%;
            gap: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .action-btn i {
            font-size: 16px;
        }

        .view-btn {
            background-color: #2196F3;
            color: white;
        }

        .view-btn:hover {
            background-color: #0b7dda;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .download-btn {
            background-color: #FF9800;
            color: white;
        }

        .download-btn:hover {
            background-color: #e68a00;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .print-btn {
            background-color: #9C27B0;
            color: white;
        }

        .print-btn:hover {
            background-color: #7B1FA2;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .action-btn:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .action-btn {
                padding: 15px 20px;
                font-size: 16px;
            }

            .action-btn i {
                font-size: 18px;
            }
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination button {
            padding: 12px 18px;
            margin: 0 5px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            border-radius: 8px;
            font-size: 16px;
            min-width: 50px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .pagination button.active {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
            font-weight: bold;
        }

        .pagination button:hover {
            background-color: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .pagination button.active:hover {
            background-color: #3e8e41;
        }

        .pagination button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .pagination button {
                padding: 15px 20px;
                font-size: 18px;
                min-width: 60px;
                margin: 0 8px;
            }
        }

        .no-bills {
            text-align: center;
            padding: 30px;
            background-color: #f9f9f9;
            border-radius: 8px;
            color: #666;
        }

        .loading {
            text-align: center;
            padding: 30px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close-btn {
            color: #aaa;
            float: right;
            font-size: 36px;
            font-weight: bold;
            cursor: pointer;
            padding: 10px;
            line-height: 30px;
            width: 50px;
            height: 50px;
            text-align: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            color: black;
            background-color: #f0f0f0;
        }

        .close-btn:active {
            transform: scale(0.95);
        }

        @media (max-width: 768px) {
            .close-btn {
                font-size: 42px;
                width: 60px;
                height: 60px;
                line-height: 36px;
            }
        }

        .bill-detail-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }

        .bill-detail-section {
            margin-bottom: 20px;
        }

        .bill-detail-section h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #333;
        }

        .bill-items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .bill-items-table th, .bill-items-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .bill-items-table th {
            background-color: #f5f5f5;
        }

        .bill-summary {
            text-align: right;
            margin-top: 15px;
        }

        .bill-summary div {
            margin-bottom: 5px;
        }

        .bill-total-amount {
            font-weight: bold;
            font-size: 1.2em;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Bill Archive</h1>
            <div class="back-button-container">
                <button id="back-to-settings-btn" class="back-btn"><i class="fas fa-arrow-left"></i> Back to Settings</button>
            </div>
        </header>

        <div class="bills-container">
            <div class="search-section">
                <h2>Search Bills</h2>
                <div class="search-form">
                    <div class="form-group">
                        <label for="date-filter">Date</label>
                        <input type="date" id="date-filter">
                    </div>
                    <div class="form-group">
                        <label for="customer-filter">Customer Name</label>
                        <input type="text" id="customer-filter" placeholder="Enter customer name">
                    </div>
                    <div class="form-group">
                        <label for="table-filter">Table Number</label>
                        <input type="text" id="table-filter" placeholder="Enter table number">
                    </div>
                    <div class="form-group">
                        <label for="payment-filter">Payment Method</label>
                        <select id="payment-filter">
                            <option value="">All</option>
                            <option value="cash">Cash</option>
                            <option value="card">Card</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="search-buttons">
                    <button id="search-btn" class="search-btn"><i class="fas fa-search"></i> Search</button>
                    <button id="reset-btn" class="reset-btn"><i class="fas fa-undo"></i> Reset</button>
                </div>
            </div>

            <div class="bills-list" id="bills-list">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading bills...</p>
                </div>
            </div>

            <div class="pagination" id="pagination">
                <!-- Pagination buttons will be added here -->
            </div>
        </div>
    </div>

    <!-- Bill Detail Modal -->
    <div id="bill-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn" id="close-modal">&times;</span>
            <div id="bill-detail-content">
                <!-- Bill details will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const dateFilter = document.getElementById('date-filter');
            const customerFilter = document.getElementById('customer-filter');
            const tableFilter = document.getElementById('table-filter');
            const paymentFilter = document.getElementById('payment-filter');
            const searchBtn = document.getElementById('search-btn');
            const resetBtn = document.getElementById('reset-btn');
            const billsList = document.getElementById('bills-list');
            const pagination = document.getElementById('pagination');
            const billModal = document.getElementById('bill-modal');
            const closeModal = document.getElementById('close-modal');
            const billDetailContent = document.getElementById('bill-detail-content');
            const backToSettingsBtn = document.getElementById('back-to-settings-btn');

            // Set default date to today
            const today = new Date().toISOString().split('T')[0];
            dateFilter.value = today;

            // Variables
            let currentPage = 1;
            const itemsPerPage = 10;
            let allBills = [];

            // Initialize
            loadBillsByDate(today);

            // Event Listeners
            searchBtn.addEventListener('click', searchBills);
            resetBtn.addEventListener('click', resetFilters);
            closeModal.addEventListener('click', function() {
                billModal.style.display = 'none';
            });

            // Back to Settings button
            backToSettingsBtn.addEventListener('click', function() {
                window.location.href = 'settings/index.html';
            });

            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === billModal) {
                    billModal.style.display = 'none';
                }
            });

            /**
             * Load bills by date
             * @param {string} date - Date in YYYY-MM-DD format
             */
            function loadBillsByDate(date) {
                showLoading();

                fetch(`http://localhost:5000/api/bills/date/${date}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            allBills = data.bills;
                            displayBills(allBills);
                        } else {
                            showNoBills('No bills found for this date');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading bills:', error);
                        showNoBills('Error loading bills. Please try again.');
                    });
            }

            /**
             * Search bills with filters
             */
            function searchBills() {
                const date = dateFilter.value;
                const customer = customerFilter.value.trim();
                const table = tableFilter.value.trim();
                const payment = paymentFilter.value;

                // If date is provided, load bills by date first
                if (date) {
                    loadBillsByDate(date);
                    return;
                }

                // Otherwise, use search API
                showLoading();

                let searchParams = new URLSearchParams();
                if (customer) searchParams.append('customerName', customer);
                if (table) searchParams.append('tableNumber', table);
                if (payment) searchParams.append('paymentMethod', payment);

                fetch(`http://localhost:5000/api/bills/search?${searchParams.toString()}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            allBills = data.bills;
                            displayBills(allBills);
                        } else {
                            showNoBills('No bills found matching your criteria');
                        }
                    })
                    .catch(error => {
                        console.error('Error searching bills:', error);
                        showNoBills('Error searching bills. Please try again.');
                    });
            }

            /**
             * Reset all filters
             */
            function resetFilters() {
                dateFilter.value = today;
                customerFilter.value = '';
                tableFilter.value = '';
                paymentFilter.value = '';

                loadBillsByDate(today);
            }

            /**
             * Display bills with pagination
             * @param {Array} bills - Bills to display
             */
            function displayBills(bills) {
                if (!bills || bills.length === 0) {
                    showNoBills('No bills found');
                    return;
                }

                // Calculate pagination
                const totalPages = Math.ceil(bills.length / itemsPerPage);
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = Math.min(startIndex + itemsPerPage, bills.length);
                const currentBills = bills.slice(startIndex, endIndex);

                // Generate HTML for bills
                let html = '';

                currentBills.forEach(bill => {
                    const date = new Date(bill.timestamp).toLocaleString();
                    // Calculate total correctly
                    const total = bill.payment?.total || calculateTotal(bill.items);

                    html += `
                        <div class="bill-card">
                            <div class="bill-info">
                                <div class="bill-header">
                                    <div class="bill-id">Bill #${bill.id}</div>
                                    <div class="bill-date">${date}</div>
                                </div>
                                <div class="bill-customer">Customer: ${bill.customerName}</div>
                                <div class="bill-table">Table: ${bill.tableNumber}</div>
                                <div class="bill-items">${bill.items.length} item(s)</div>
                                <div class="bill-total">Total: $${total.toFixed(2)}</div>
                                <div class="bill-payment">Payment: ${bill.payment?.paymentMethod || 'Unknown'}</div>
                            </div>
                            <div class="bill-actions">
                                <button class="action-btn view-btn" onclick="window.viewBill('${bill.id}')"><i class="fas fa-eye"></i> View Details</button>
                                <a href="http://localhost:5000/api/bills/${bill.id}/html" class="action-btn download-btn" target="_blank"><i class="fas fa-receipt"></i> View Receipt</a>
                                <button class="action-btn print-btn" onclick="window.printBill('${bill.id}')"><i class="fas fa-print"></i> Print</button>
                            </div>
                        </div>
                    `;
                });

                billsList.innerHTML = html;

                // Update pagination
                updatePagination(totalPages);
            }

            /**
             * Update pagination controls
             * @param {number} totalPages - Total number of pages
             */
            function updatePagination(totalPages) {
                let html = '';

                if (totalPages > 1) {
                    // Previous button
                    html += `<button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})"><i class="fas fa-chevron-left"></i> Prev</button>`;

                    // Page buttons
                    for (let i = 1; i <= totalPages; i++) {
                        html += `<button class="${currentPage === i ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
                    }

                    // Next button
                    html += `<button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">Next <i class="fas fa-chevron-right"></i></button>`;
                }

                pagination.innerHTML = html;
            }

            /**
             * Change current page
             * @param {number} page - Page number
             */
            window.changePage = function(page) {
                currentPage = page;
                displayBills(allBills);
                window.scrollTo(0, 0);
            };

            /**
             * View bill details
             * @param {number} billId - Bill ID
             */
            window.viewBill = function(billId) {
                fetch(`http://localhost:5000/api/bills/${billId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayBillDetails(data.bill);
                            billModal.style.display = 'block';
                        } else {
                            alert('Error loading bill details');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading bill details:', error);
                        alert('Error loading bill details');
                    });
            };

            /**
             * Print bill
             * @param {number} billId - Bill ID
             */
            window.printBill = function(billId) {
                fetch(`http://localhost:5000/api/bills/${billId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Create a new window for printing
                            const printWindow = window.open('', '_blank');

                            // Generate print-friendly HTML
                            const bill = data.bill;
                            const date = new Date(bill.timestamp).toLocaleString();
                            // Calculate total correctly
                            const total = bill.payment?.total || calculateTotal(bill.items);

                            let html = `
                                <!DOCTYPE html>
                                <html>
                                <head>
                                    <title>Bill #${bill.id}</title>
                                    <style>
                                        body { font-family: Arial, sans-serif; margin: 20px; }
                                        .bill-header { text-align: center; margin-bottom: 20px; }
                                        .bill-info { margin-bottom: 20px; }
                                        .bill-items { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                                        .bill-items th, .bill-items td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                                        .bill-summary { text-align: right; }
                                        .bill-total { font-weight: bold; font-size: 1.2em; }
                                        @media print {
                                            .no-print { display: none; }
                                            body { margin: 0; }
                                        }
                                    </style>
                                </head>
                                <body>
                                    <div class="bill-header">
                                        <h1>Paradiz Hookah Lounge</h1>
                                        <h2>Receipt</h2>
                                    </div>

                                    <div class="bill-info">
                                        <p><strong>Bill #:</strong> ${bill.id}</p>
                                        <p><strong>Date:</strong> ${date}</p>
                                        <p><strong>Customer:</strong> ${bill.customerName}</p>
                                        <p><strong>Table:</strong> ${bill.tableNumber}</p>
                                    </div>

                                    <table class="bill-items">
                                        <thead>
                                            <tr>
                                                <th>Item</th>
                                                <th>Quantity</th>
                                                <th>Price</th>
                                                <th>Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                            `;

                            bill.items.forEach(item => {
                                // Ensure price and quantity are numbers
                                const price = parseFloat(item.price) || 0;
                                const quantity = parseInt(item.quantity) || 1;
                                const itemTotal = (price * quantity).toFixed(2);

                                html += `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${quantity}</td>
                                        <td>$${price.toFixed(2)}</td>
                                        <td>$${itemTotal}</td>
                                    </tr>
                                `;
                            });

                            html += `
                                        </tbody>
                                    </table>

                                    <div class="bill-summary">
                            `;

                            if (bill.payment) {
                                if (bill.payment.subtotal) {
                                    html += `<p>Subtotal: $${bill.payment.subtotal.toFixed(2)}</p>`;
                                }

                                // For cash payments, show a message that no taxes are applied
                                if (bill.payment.paymentMethod === 'cash') {
                                    html += `<p style="color: #27ae60; font-style: italic;">Cash payment - No taxes applied</p>`;
                                } else {
                                    // For card payments, show tax details
                                    if (bill.payment.gst) {
                                        html += `<p>GST (${(bill.payment.gstRate * 100).toFixed(0)}%): $${bill.payment.gst.toFixed(2)}</p>`;
                                    }
                                    if (bill.payment.pst) {
                                        html += `<p>PST (${(bill.payment.pstRate * 100).toFixed(0)}%): $${bill.payment.pst.toFixed(2)}</p>`;
                                    }
                                    if (bill.payment.tax && !bill.payment.gst && !bill.payment.pst) {
                                        html += `<p>Tax (${(bill.payment.taxRate * 100).toFixed(0)}%): $${bill.payment.tax.toFixed(2)}</p>`;
                                    }
                                }

                                if (bill.payment.tip) {
                                    html += `<p>Tip: $${bill.payment.tip.toFixed(2)}</p>`;
                                }
                            }

                            html += `
                                        <p class="bill-total">Total: $${total.toFixed(2)}</p>
                                        <p>Payment Method: ${bill.payment?.paymentMethod || 'Unknown'}</p>
                                    </div>

                                    <div class="bill-footer" style="text-align: center; margin-top: 40px;">
                                        <p>Thank you for your business!</p>
                                    </div>

                                    <div class="no-print" style="text-align: center; margin-top: 30px;">
                                        <button onclick="window.print()">Print</button>
                                        <button onclick="window.close()">Close</button>
                                    </div>
                                </body>
                                </html>
                            `;

                            printWindow.document.open();
                            printWindow.document.write(html);
                            printWindow.document.close();
                        } else {
                            alert('Error loading bill for printing');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading bill for printing:', error);
                        alert('Error loading bill for printing');
                    });
            };

            /**
             * Display bill details in modal
             * @param {Object} bill - Bill data
             */
            function displayBillDetails(bill) {
                const date = new Date(bill.timestamp).toLocaleString();
                // Calculate total correctly
                const total = bill.payment?.total || calculateTotal(bill.items);

                let html = `
                    <div class="bill-detail-header">
                        <h2>Bill #${bill.id}</h2>
                        <p><strong>Date:</strong> ${date}</p>
                    </div>

                    <div class="bill-detail-section">
                        <h3>Customer Information</h3>
                        <p><strong>Name:</strong> ${bill.customerName}</p>
                        <p><strong>Table:</strong> ${bill.tableNumber}</p>
                    </div>

                    <div class="bill-detail-section">
                        <h3>Items</h3>
                        <table class="bill-items-table">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                bill.items.forEach(item => {
                    // Ensure price and quantity are numbers
                    const price = parseFloat(item.price) || 0;
                    const quantity = parseInt(item.quantity) || 1;
                    const itemTotal = (price * quantity).toFixed(2);

                    html += `
                        <tr>
                            <td>${item.name}</td>
                            <td>${quantity}</td>
                            <td>$${price.toFixed(2)}</td>
                            <td>$${itemTotal}</td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>

                        <div class="bill-summary">
                `;

                if (bill.payment) {
                    if (bill.payment.subtotal) {
                        html += `<div>Subtotal: $${bill.payment.subtotal.toFixed(2)}</div>`;
                    }

                    // For cash payments, show a message that no taxes are applied
                    if (bill.payment.paymentMethod === 'cash') {
                        html += `<div style="color: #27ae60; font-style: italic;">Cash payment - No taxes applied</div>`;
                    } else {
                        // For card payments, show tax details
                        if (bill.payment.gst) {
                            html += `<div>GST (${(bill.payment.gstRate * 100).toFixed(0)}%): $${bill.payment.gst.toFixed(2)}</div>`;
                        }
                        if (bill.payment.pst) {
                            html += `<div>PST (${(bill.payment.pstRate * 100).toFixed(0)}%): $${bill.payment.pst.toFixed(2)}</div>`;
                        }
                        if (bill.payment.tax && !bill.payment.gst && !bill.payment.pst) {
                            html += `<div>Tax (${(bill.payment.taxRate * 100).toFixed(0)}%): $${bill.payment.tax.toFixed(2)}</div>`;
                        }
                    }

                    if (bill.payment.tip) {
                        html += `<div>Tip: $${bill.payment.tip.toFixed(2)}</div>`;
                    }
                }

                html += `
                            <div class="bill-total-amount">Total: $${total.toFixed(2)}</div>
                        </div>
                    </div>

                    <div class="bill-detail-section">
                        <h3>Payment Information</h3>
                        <p><strong>Method:</strong> ${bill.payment?.paymentMethod || 'Unknown'}</p>
                        ${bill.payment?.transactionId ? `<p><strong>Transaction ID:</strong> ${bill.payment.transactionId}</p>` : ''}
                        <p><strong>Status:</strong> Paid</p>
                    </div>
                `;

                billDetailContent.innerHTML = html;
            }

            /**
             * Calculate total from items
             * @param {Array} items - Order items
             * @returns {number} Total amount
             */
            function calculateTotal(items) {
                if (!items || !Array.isArray(items)) return 0;
                return items.reduce((sum, item) => {
                    const price = parseFloat(item.price) || 0;
                    const quantity = parseInt(item.quantity) || 1;
                    return sum + (price * quantity);
                }, 0);
            }

            /**
             * Show loading state
             */
            function showLoading() {
                billsList.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Loading bills...</p>
                    </div>
                `;
                pagination.innerHTML = '';
            }

            /**
             * Show no bills message
             * @param {string} message - Message to display
             */
            function showNoBills(message) {
                billsList.innerHTML = `
                    <div class="no-bills">
                        <p>${message}</p>
                    </div>
                `;
                pagination.innerHTML = '';
            }
        });
    </script>
</body>
</html>
