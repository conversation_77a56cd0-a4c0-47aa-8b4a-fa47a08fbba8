/**
 * Thermal Printer Service
 * Handles communication with thermal printers for kitchen tickets
 */

const escpos = require('escpos');
// Install USB adapter if using USB printers
escpos.USB = require('escpos-usb');
// Install network adapter if using network printers
escpos.Network = require('escpos-network');
// Install serialport adapter if using serial printers
escpos.Serial = require('escpos-serialport');

class PrinterService {
    constructor(config = {}) {
        this.config = {
            // Default configuration
            enabled: true,
            printerType: 'usb', // 'usb', 'network', or 'serial'
            networkPrinterIP: '*************',
            networkPrinterPort: 9100,
            serialPortPath: '/dev/usb/lp0',
            usbVendorId: null,
            usbProductId: null,
            characterSet: 'PC437_USA',
            ...config
        };

        this.printers = {
            kitchen: null,
            hookah: null
        };

        this.initialize();
    }

    /**
     * Initialize printer connections
     */
    initialize() {
        try {
            if (!this.config.enabled) {
                console.log('Printer service is disabled');
                return;
            }

            // Find and configure printers
            this.findPrinters();
        } catch (error) {
            console.error('Error initializing printer service:', error);
        }
    }

    /**
     * Find available printers
     */
    findPrinters() {
        try {
            if (this.config.printerType === 'usb') {
                // List available USB devices
                const devices = escpos.USB.findPrinter();
                console.log('Available USB printers:', devices);

                if (devices && devices.length > 0) {
                    // Use the first printer for kitchen if not specified
                    const kitchenPrinterDevice = this.config.usbVendorId && this.config.usbProductId
                        ? devices.find(d => d.vendorId === this.config.usbVendorId && d.productId === this.config.usbProductId)
                        : devices[0];

                    // Use the second printer for hookah if available, otherwise use the same as kitchen
                    const hookahPrinterDevice = devices.length > 1 ? devices[1] : kitchenPrinterDevice;

                    if (kitchenPrinterDevice) {
                        console.log('Using USB printer for kitchen:', kitchenPrinterDevice);
                        this.printers.kitchen = kitchenPrinterDevice;
                    }

                    if (hookahPrinterDevice) {
                        console.log('Using USB printer for hookah:', hookahPrinterDevice);
                        this.printers.hookah = hookahPrinterDevice;
                    }
                } else {
                    console.warn('No USB printers found');
                }
            } else if (this.config.printerType === 'network') {
                // Network printer doesn't need to be found, just configured
                console.log(`Using network printer at ${this.config.networkPrinterIP}:${this.config.networkPrinterPort}`);
                this.printers.kitchen = { ip: this.config.networkPrinterIP, port: this.config.networkPrinterPort };
                this.printers.hookah = { ip: this.config.networkPrinterIP, port: this.config.networkPrinterPort };
            } else if (this.config.printerType === 'serial') {
                // Serial printer doesn't need to be found, just configured
                console.log(`Using serial printer at ${this.config.serialPortPath}`);
                this.printers.kitchen = { path: this.config.serialPortPath };
                this.printers.hookah = { path: this.config.serialPortPath };
            }
        } catch (error) {
            console.error('Error finding printers:', error);
        }
    }

    /**
     * Get a printer device
     * @param {string} type - 'kitchen' or 'hookah'
     * @returns {object} printer device
     */
    getPrinter(type = 'kitchen') {
        if (!this.config.enabled) {
            return null;
        }

        const printerInfo = this.printers[type];
        if (!printerInfo) {
            console.warn(`No ${type} printer configured`);
            return null;
        }

        try {
            let device;

            if (this.config.printerType === 'usb') {
                device = new escpos.USB(printerInfo.vendorId, printerInfo.productId);
            } else if (this.config.printerType === 'network') {
                device = new escpos.Network(printerInfo.ip, printerInfo.port);
            } else if (this.config.printerType === 'serial') {
                device = new escpos.Serial(printerInfo.path);
            } else {
                console.warn(`Unsupported printer type: ${this.config.printerType}`);
                return null;
            }

            return device;
        } catch (error) {
            console.error(`Error getting ${type} printer:`, error);
            return null;
        }
    }

    /**
     * Print a hookah ticket
     * @param {object} order - Order data
     * @returns {Promise<boolean>} success
     */
    printHookahTicket(order) {
        return new Promise((resolve, reject) => {
            try {
                if (!this.config.enabled) {
                    console.log('Printer service is disabled, skipping hookah ticket printing');
                    resolve(false);
                    return;
                }

                const device = this.getPrinter('hookah');
                if (!device) {
                    console.warn('No hookah printer available');
                    resolve(false);
                    return;
                }

                // Find hookah items in the order
                const hookahItems = this.extractHookahItems(order);
                if (hookahItems.length === 0) {
                    console.log('No hookah items in order, skipping ticket');
                    resolve(false);
                    return;
                }

                // Create printer instance
                const printer = new escpos.Printer(device);

                // Open device
                device.open((error) => {
                    if (error) {
                        console.error('Error opening hookah printer:', error);
                        reject(error);
                        return;
                    }

                    // Print the ticket
                    printer
                        .font('a')
                        .align('ct')
                        .style('b')
                        .size(1, 1)
                        .text('HOOKAH ORDER')
                        .text('------------------------')
                        .align('lt')
                        .size(0, 0)
                        .text(`Order #: ${order.id}`)
                        .text(`Table: ${order.tableNumber}`)
                        .text(`Customer: ${order.customerName}`)
                        .text(`Time: ${new Date().toLocaleTimeString()}`)
                        .text('------------------------')
                        .align('ct')
                        .style('b')
                        .text('HOOKAH ITEMS')
                        .align('lt')
                        .style('normal');

                    // Print each hookah item
                    hookahItems.forEach(item => {
                        printer
                            .text(`${item.name} x${item.quantity}`)
                            .text(`Flavor: ${item.flavor || 'Not specified'}`)
                            .text('');
                    });

                    printer
                        .text('------------------------')
                        .cut()
                        .close();

                    console.log('Hookah ticket printed successfully');
                    resolve(true);
                });
            } catch (error) {
                console.error('Error printing hookah ticket:', error);
                reject(error);
            }
        });
    }

    /**
     * Print a kitchen ticket
     * @param {object} order - Order data
     * @returns {Promise<boolean>} success
     */
    printKitchenTicket(order) {
        return new Promise((resolve, reject) => {
            try {
                if (!this.config.enabled) {
                    console.log('Printer service is disabled, skipping kitchen ticket printing');
                    resolve(false);
                    return;
                }

                const device = this.getPrinter('kitchen');
                if (!device) {
                    console.warn('No kitchen printer available');
                    resolve(false);
                    return;
                }

                // Find food and drink items in the order
                const foodItems = this.extractFoodItems(order);
                if (foodItems.length === 0) {
                    console.log('No food/drink items in order, skipping ticket');
                    resolve(false);
                    return;
                }

                // Create printer instance
                const printer = new escpos.Printer(device);

                // Open device
                device.open((error) => {
                    if (error) {
                        console.error('Error opening kitchen printer:', error);
                        reject(error);
                        return;
                    }

                    // Print the ticket
                    printer
                        .font('a')
                        .align('ct')
                        .style('b')
                        .size(1, 1)
                        .text('KITCHEN ORDER')
                        .text('------------------------')
                        .align('lt')
                        .size(0, 0)
                        .text(`Order #: ${order.id}`)
                        .text(`Table: ${order.tableNumber}`)
                        .text(`Customer: ${order.customerName}`)
                        .text(`Time: ${new Date().toLocaleTimeString()}`)
                        .text('------------------------')
                        .align('ct')
                        .style('b')
                        .text('FOOD & DRINKS')
                        .align('lt')
                        .style('normal');

                    // Print each food item
                    foodItems.forEach(item => {
                        printer
                            .text(`${item.name} x${item.quantity}`)
                            .text(`Notes: ${item.notes || 'None'}`)
                            .text('');
                    });

                    printer
                        .text('------------------------')
                        .cut()
                        .close();

                    console.log('Kitchen ticket printed successfully');
                    resolve(true);
                });
            } catch (error) {
                console.error('Error printing kitchen ticket:', error);
                reject(error);
            }
        });
    }

    /**
     * Extract hookah items from an order
     * @param {object} order - Order data
     * @returns {Array} hookah items
     */
    extractHookahItems(order) {
        try {
            let items = [];
            
            // Parse items if they're in string format
            if (typeof order.items === 'string') {
                items = JSON.parse(order.items);
            } else if (Array.isArray(order.items)) {
                items = order.items;
            }
            
            // Filter for hookah items (assuming they have a category or type field)
            return items.filter(item => 
                item.category === 'hookah' || 
                item.type === 'hookah' || 
                (item.name && item.name.toLowerCase().includes('hookah'))
            );
        } catch (error) {
            console.error('Error extracting hookah items:', error);
            return [];
        }
    }

    /**
     * Extract food and drink items from an order
     * @param {object} order - Order data
     * @returns {Array} food and drink items
     */
    extractFoodItems(order) {
        try {
            let items = [];
            
            // Parse items if they're in string format
            if (typeof order.items === 'string') {
                items = JSON.parse(order.items);
            } else if (Array.isArray(order.items)) {
                items = order.items;
            }
            
            // Filter for food and drink items (assuming they have a category or type field)
            return items.filter(item => 
                item.category === 'food' || 
                item.category === 'drink' || 
                item.type === 'food' || 
                item.type === 'drink' || 
                !(item.category === 'hookah' || item.type === 'hookah' || 
                  (item.name && item.name.toLowerCase().includes('hookah')))
            );
        } catch (error) {
            console.error('Error extracting food items:', error);
            return [];
        }
    }

    /**
     * Test the printer connection
     * @param {string} type - 'kitchen' or 'hookah'
     * @returns {Promise<boolean>} success
     */
    testPrinter(type = 'kitchen') {
        return new Promise((resolve, reject) => {
            try {
                if (!this.config.enabled) {
                    console.log('Printer service is disabled');
                    resolve(false);
                    return;
                }

                const device = this.getPrinter(type);
                if (!device) {
                    console.warn(`No ${type} printer available`);
                    resolve(false);
                    return;
                }

                // Create printer instance
                const printer = new escpos.Printer(device);

                // Open device
                device.open((error) => {
                    if (error) {
                        console.error(`Error opening ${type} printer:`, error);
                        reject(error);
                        return;
                    }

                    // Print test page
                    printer
                        .font('a')
                        .align('ct')
                        .style('b')
                        .size(1, 1)
                        .text('PRINTER TEST')
                        .text('------------------------')
                        .align('lt')
                        .size(0, 0)
                        .text(`Printer: ${type}`)
                        .text(`Time: ${new Date().toLocaleString()}`)
                        .text('------------------------')
                        .text('If you can read this, the printer is working correctly.')
                        .text('------------------------')
                        .cut()
                        .close();

                    console.log(`${type} printer test completed successfully`);
                    resolve(true);
                });
            } catch (error) {
                console.error(`Error testing ${type} printer:`, error);
                reject(error);
            }
        });
    }

    /**
     * Update printer configuration
     * @param {object} config - New configuration
     */
    updateConfig(config = {}) {
        this.config = {
            ...this.config,
            ...config
        };

        // Re-initialize with new config
        this.initialize();
    }
}

module.exports = PrinterService;
