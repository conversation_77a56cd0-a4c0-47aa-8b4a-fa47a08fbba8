# Paradiz Hookah Lounge Mobile App - Installation Guide

This guide will walk you through the process of setting up and deploying the Paradiz Hookah Lounge mobile app.

## Prerequisites

- Node.js (v14 or newer)
- npm or yarn
- Expo CLI (`npm install -g expo-cli`)
- Android Studio (for Android development)
- Xcode (for iOS development, Mac only)
- Apple Developer Account (for iOS deployment)
- Google Play Developer Account (for Android deployment)

## Development Setup

### 1. Install Dependencies

```bash
# Navigate to the mobile app directory
cd mobile-app

# Install dependencies
npm install
# or
yarn install
```

### 2. Configure Environment Variables

Create a `.env` file in the mobile-app directory with the following variables:

```
API_URL=http://your-server-ip:5000
```

Replace `your-server-ip` with the IP address or domain name of your POS server.

### 3. Start Development Server

```bash
npm start
# or
yarn start
```

This will start the Expo development server and display a QR code in your terminal.

### 4. Run on Device or Emulator

- **iOS Simulator**: Press `i` in the terminal
- **Android Emulator**: Press `a` in the terminal
- **Physical Device**: Scan the QR code with the Expo Go app (available on App Store and Google Play)

## Server Configuration

### 1. Install Required Dependencies

The mobile app requires additional dependencies on the server:

```bash
npm install bcrypt jsonwebtoken
```

### 2. Set Up Database Tables

The mobile app requires additional database tables. These should be created automatically when you start the server with the mobile app API integration.

### 3. Generate Table QR Codes

The server will automatically generate QR codes for tables 1-20 in the `public/table-qr-codes` directory. You can print these QR codes and place them on your tables for in-house ordering.

## Building for Production

### Android

1. Configure app.json:
   - Update the `android.package` name
   - Add your own adaptive icons

2. Build the APK or AAB:
   ```bash
   expo build:android -t apk  # For APK
   # or
   expo build:android -t app-bundle  # For AAB (recommended for Play Store)
   ```

3. Follow the prompts to generate a keystore or use an existing one

4. Wait for the build to complete and download the APK/AAB

### iOS

1. Configure app.json:
   - Update the `ios.bundleIdentifier`
   - Add your own icons and splash screens

2. Build the IPA:
   ```bash
   expo build:ios
   ```

3. Follow the prompts to set up credentials

4. Wait for the build to complete and download the IPA

## Deployment

### Google Play Store

1. Create a Google Play Developer account
2. Create a new application in the Google Play Console
3. Upload your AAB file
4. Fill in the store listing information
5. Set up pricing and distribution
6. Submit for review

### Apple App Store

1. Create an Apple Developer account
2. Create a new application in App Store Connect
3. Upload your IPA file using Transporter
4. Fill in the App Store information
5. Submit for review

## Customization

### Branding

1. Update the colors in `App.tsx`:
   ```javascript
   const theme = {
     ...DefaultTheme,
     colors: {
       ...DefaultTheme.colors,
       primary: '#YOUR_PRIMARY_COLOR',
       accent: '#YOUR_ACCENT_COLOR',
       // other colors...
     },
   };
   ```

2. Replace the logo and splash images in the `assets` folder

3. Update the app name and other details in `app.json`

### Features

You can customize the app features by:

1. Modifying the screens in the `src/screens` directory
2. Adding or removing components in the `src/components` directory
3. Updating the API endpoints in the `src/api/api.ts` file

## Troubleshooting

### Common Issues

1. **API Connection Errors**:
   - Make sure your server is running
   - Check that the API_URL in .env is correct
   - Ensure your device/emulator can reach the server

2. **Build Errors**:
   - Clear the Expo cache: `expo r -c`
   - Delete node_modules and reinstall dependencies

3. **QR Code Scanning Issues**:
   - Ensure the camera has proper permissions
   - Check that the QR code format matches what the app expects

### Getting Help

If you encounter issues not covered in this guide, please:

1. Check the Expo documentation: https://docs.expo.dev/
2. Search for solutions on Stack Overflow
3. Contact your developer support team

## Maintenance

### Regular Updates

It's recommended to:

1. Update Expo SDK regularly
2. Keep dependencies up to date
3. Test on new OS versions
4. Monitor app performance and crash reports

### Backup

Always keep backups of:

1. Your keystore file (Android)
2. Your certificates (iOS)
3. Your source code
4. Your app configuration
