/* Base Styles */
:root {
    --primary-color: #6B46C1; /* Dark Lavender */
    --secondary-color: #38B2AC; /* Turquoise */
    --danger-color: #E53E3E;
    --background-color: #F7FAFC;
    --card-color: #FFFFFF;
    --text-color: #2D3748;
    --text-secondary: #4A5568;
    --border-color: #E2E8F0;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    padding: 20px;
}

#app {
    width: 375px;
    height: 667px;
    background-color: var(--background-color);
    border-radius: 30px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.status-bar {
    height: 24px;
    background-color: var(--primary-color);
}

.screen {
    height: calc(100% - 74px); /* Subtract status bar and nav bar height */
    overflow-y: auto;
    padding-bottom: 20px;
}

.hidden {
    display: none;
}

/* Navigation Bar */
.nav-bar {
    display: flex;
    justify-content: space-around;
    background-color: white;
    padding: 10px 0;
    position: absolute;
    bottom: 0;
    width: 100%;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #A0AEC0;
    font-size: 12px;
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-item.active {
    color: var(--primary-color);
}

/* Home Screen */
.header {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logo {
    width: 150px;
    height: 80px;
    background-color: #ddd;
    margin-bottom: 10px;
    object-fit: contain;
}

.card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    padding: 16px;
    margin: 16px;
    box-shadow: var(--box-shadow);
}

.scan-card {
    text-align: center;
}

.scan-card h3 {
    color: var(--primary-color);
    margin-bottom: 8px;
}

.scan-card p {
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.primary-btn i {
    margin-right: 8px;
}

.section-title {
    padding: 0 16px;
    margin-top: 24px;
    margin-bottom: 16px;
    color: var(--text-color);
}

.featured-items {
    display: flex;
    overflow-x: auto;
    padding: 0 16px;
}

.featured-item {
    min-width: 150px;
    margin-right: 16px;
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    padding-bottom: 12px;
    box-shadow: var(--box-shadow);
}

.item-image {
    height: 100px;
    background-color: #ddd;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    margin-bottom: 8px;
}

.featured-item h4 {
    padding: 0 12px;
    font-size: 14px;
    margin-bottom: 4px;
    color: var(--text-color);
}

.price {
    padding: 0 12px;
    color: var(--primary-color);
    font-weight: bold;
}

.quick-links {
    display: flex;
    justify-content: space-around;
    padding: 24px 16px;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.icon-container {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    color: white;
}

.purple {
    background-color: var(--primary-color);
}

.teal {
    background-color: var(--secondary-color);
}

.orange {
    background-color: #ED8936;
}

/* Menu Screen */
.screen-header {
    padding: 20px 16px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.screen-header.purple {
    background-color: var(--primary-color);
    color: white;
}

.search-bar {
    display: flex;
    align-items: center;
    background-color: #EDF2F7;
    border-radius: 20px;
    padding: 8px 16px;
    margin-top: 12px;
}

.search-bar i {
    color: #A0AEC0;
    margin-right: 8px;
}

.search-bar input {
    border: none;
    background: transparent;
    flex: 1;
    font-size: 16px;
    outline: none;
}

.categories {
    display: flex;
    padding: 16px;
    overflow-x: auto;
}

.category {
    background-color: white;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 20px;
    margin-right: 8px;
    font-size: 14px;
    white-space: nowrap;
}

.category.active {
    background-color: var(--primary-color);
    color: white;
}

.menu-items {
    padding: 16px;
}

.menu-item {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 16px;
    display: flex;
    box-shadow: var(--box-shadow);
}

.item-details {
    flex: 1;
}

.item-details h3 {
    font-size: 16px;
    margin-bottom: 4px;
    color: var(--text-color);
}

.item-details p {
    color: var(--text-secondary);
    margin-bottom: 8px;
    font-size: 14px;
}

.item-image.small {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    margin-left: 16px;
}

/* Reservation Screen */
.reservation-form {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 16px;
    box-shadow: var(--box-shadow);
}

.reservation-form h3 {
    margin-bottom: 16px;
    color: var(--text-color);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--text-color);
}

.form-input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
}

.time-slots {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.time-slot {
    background-color: white;
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 14px;
}

.full-width {
    width: 100%;
}

.upcoming-reservations {
    padding: 0 16px;
}

.upcoming-reservations h3 {
    margin-bottom: 16px;
    color: var(--text-color);
}

.reservation-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    box-shadow: var(--box-shadow);
}

.reservation-details h4 {
    font-size: 16px;
    margin-bottom: 4px;
    color: var(--text-color);
}

.reservation-details p {
    color: var(--text-secondary);
    font-size: 14px;
}

.cancel-btn {
    background: none;
    border: none;
    color: var(--danger-color);
    font-size: 20px;
    cursor: pointer;
}

/* Profile Screen */
.profile-header {
    background-color: white;
    padding: 24px 16px;
    display: flex;
    align-items: center;
    box-shadow: var(--box-shadow);
}

.avatar {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    margin-right: 16px;
}

.profile-info {
    flex: 1;
}

.profile-info h3 {
    font-size: 20px;
    margin-bottom: 4px;
    color: var(--text-color);
}

.profile-info p {
    color: var(--text-secondary);
    margin-bottom: 8px;
    font-size: 14px;
}

.outline-btn {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: bold;
    cursor: pointer;
}

.outline-btn.danger {
    border-color: var(--danger-color);
    color: var(--danger-color);
    margin: 16px;
}

.profile-section {
    background-color: white;
    margin: 16px;
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: var(--box-shadow);
}

.profile-section h3 {
    margin-bottom: 16px;
    color: var(--text-color);
}

.order-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.order-date {
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 4px;
}

.order-number {
    color: var(--text-secondary);
    font-size: 14px;
}

.order-total {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    font-weight: bold;
}

.order-total i {
    margin-left: 8px;
    color: #A0AEC0;
}

.settings-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.settings-icon {
    width: 36px;
    height: 36px;
    background-color: #EDF2F7;
    border-radius: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 12px;
    color: var(--primary-color);
}

.settings-item p {
    flex: 1;
    color: var(--text-color);
}

.settings-item i {
    color: #A0AEC0;
}
